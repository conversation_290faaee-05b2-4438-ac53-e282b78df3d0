import useOverview from '@/hooks/useGetOverView';
import OverviewCardA from './OverviewCardA';
import { FC } from 'react';
import { formatCurrency } from '@/features/utils/formatCurrency';
interface OverviewUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: {
    balance: number;
    expenses: number;
    income: number;
    savings: number;
  };
}

interface OverviewProps {
  showSavings?: boolean;
}

const OverviewA:FC<OverviewProps> = ({ showSavings }) => {
  const { loading, balance, expenses, income, savings } = useOverview({
    select: ({ isLoading, isUninitialized, data }: OverviewUtils) => ({
      loading: isLoading || isUninitialized,
      balance: data?.balance || 0,
      expenses: data?.expenses || 0,
      income: data?.income || 0,
      savings: data?.savings || 0,
    }),
  });

  return (
    <dl className={`grid grid-cols-1 gap-5 sm:grid-cols-2 ${showSavings ? 'lg:grid-cols-4' : 'lg:grid-cols-3'}`}>
      <OverviewCardA
        loading={loading}
        name="Ghana cedies"
        stat={formatCurrency(balance)}
      />
      <OverviewCardA
        loading={loading}
        name="US Dollar"
        stat={formatCurrency(income, 'USD')}
      />
      <OverviewCardA
        loading={loading}
        name="Naigeria Naira"
        stat={formatCurrency(expenses, 'NGN')}
        />
      {showSavings && (
        <OverviewCardA
          loading={loading}
          name="dashboard.savings"
          stat={formatCurrency(savings)}
        />
      )}
    </dl>
  );
};

export default OverviewA;
