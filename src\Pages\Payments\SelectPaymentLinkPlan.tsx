import { But<PERSON> } from "@/components/button"
import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"
import { paymentLinkPlans } from "@/data/payments"
import PlanLinkItem from "@/features/Payments/components/PlanLinkItem"
import { ArrowLeft } from "heroicons-react"
import { useState } from "react"
import { Link } from "react-router-dom"

const SelectPaymentLinkPlan = () => {
  const [selectedPlan, setSelectedPlan] = useState<string>('onetime')

  return (
    <div className="p-5 lg:p-10 w-screen h-screen">
      <Button component={Link} variant='transparent' to='/dashboard/payments/get-paid' className='!p-0 text-center w-fit flex gap-5 rounded-md mb-2' >
        <ArrowLeft />
        <Translate msgId='dashboard.back' />
      </Button>

      <div className=' flex flex-col items-center gap-10 justify-center p-5 lg:h-full'>
        <div>
          <SimpleText component="h1" className="font-semibold text-center text-3xl">
            <Translate msgId="dashboard.generatePaymentLink" />
          </SimpleText>
          <SimpleText component="p" className=" text-center text-gray-300">
            <Translate msgId="dashboard.selectSubscription" />
          </SimpleText>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl">
          {paymentLinkPlans.map(plan => (
            <PlanLinkItem 
              key={plan.id} 
              {...plan} 
              selectedPlan={selectedPlan} 
              setSelectedPlan={setSelectedPlan} 
            />
          ))}
        </div>

        <Button component={Link} to={`/generate-payment-link?planId=${selectedPlan}`} variant="darkPrimary" className="rounded-lg w-60 text-center">
          <Translate msgId="dashboard.continue" />
        </Button>
      </div>
    </div>
  )
}

export default SelectPaymentLinkPlan
// 0e9b582