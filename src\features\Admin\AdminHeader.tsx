import { Button } from '@/components/button'
import { SearchBar } from '@/components/search'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'
import { Mail } from 'heroicons-react'
import AddUserModal from './components/AddUserModal'

const AdminHeader = () => {
  return (
    <div className="grid lg:grid-cols-3 items-center w-full gap-5">
      <div className="flex gap-2 items-center">
      <input type='checkbox' className=' checked:border-gray-500 checked:bg-gray-500 border border-gray-500 rounded-md' />
      <SimpleText>
        <Translate msgId='dashboard.allUsers' />
      </SimpleText>
      <div className="bg-raven-green-800/40  px-2 rounded font-semibold">
        10
      </div>
      </div>

      <SearchBar />

      <div className="flex gap-5 items-center lg:justify-self-end">
        <Button variant='light' className='flex gap-2 '>
          <Mail />
          <Translate msgId='dashboard.invite' />
        </Button>
        <AddUserModal />

      </div>
    </div>
  )
}

export default AdminHeader