import { SimpleText } from '@/components/typography';
import { Trans } from 'react-i18next';

const Statistics = () => {
  return (
    <div className="mt-20 md:py-10 md:px-10 md:flex-row flex-col flex flex-wrap items-center justify-around gap-5">
      <div className="text-3xl ">
        <SimpleText component="h3">
          <Trans
            i18nKey="business.statsCurrencies"
            components={{
              span: <span className="text-raven-green-800" />,
            }}
          />
        </SimpleText>
      </div>
      
      <div className="text-3xl ">
        <SimpleText component="h3">
          <Trans
            i18nKey="business.statsCountries"
            components={{
              span: <span className="text-raven-green-800" />,
            }}
          />
        </SimpleText>
      </div>
      
      <div className="text-3xl ">
        <SimpleText component="h3">
          <Trans
            i18nKey="business.statsApi"
            components={{
              span: <span className="text-raven-green-800" />,
            }}
          />
        </SimpleText>
      </div>
      
    </div>
  );
};

export default Statistics;
