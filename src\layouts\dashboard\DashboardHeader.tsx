import { FC, ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';
import Button from 'components/button/Button';
import useMenu from '@/hooks/useMenu';
import useSidebar from '@/hooks/useSidebar';
import DashboardMenu from './DashboardMenu';
import useNotifications from '@/hooks/useNotifications';
import { BellOutline, MenuAlt2 } from 'heroicons-react';
import { Logo } from 'components/logo';
import { Avatar } from 'components/avatar';
import useIsAuthorized from '@/hooks/useIsAuthorized';

interface DashboardHeaderProps {
  children?: ReactNode;
  disableMenu: boolean;
  disableLogo: boolean;
  disableAvatar?: boolean;
  disableNotifications: boolean;
}

interface Actions {
  on: () => void;
  off: () => void;
  toggle: () => void;
}

const DashboardHeader: FC<DashboardHeaderProps> = ({
  children,
  disableMenu,
  disableLogo,
  disableAvatar,
  disableNotifications,
}) => {
  const [, menuActions] = useMenu();
  const [, sidebarActions] = useSidebar();
  const [, notificationsActions] = useNotifications();

  const { avatar } = JSON.parse(sessionStorage.getItem('session')!);
  const isAuthorized = useIsAuthorized();
  const location = useLocation();

  return (
    <div className="relative z-10 shrink-0 flex h-16 bg-white border-b border-gray-200 flex-row">
      {disableMenu ? null : (
        <button
          onClick={(sidebarActions as Actions).toggle}
          className="md:hidden flex items-center w-16 justify-center border-r relative p-1 text-gray-500 hover:text-gray-400 focus:outline-none focus:bg-cool-gray-100 focus:text-gray-500 transition duration-150 ease-in-out"
          aria-label="Navigation Toggle"
        >
          <MenuAlt2 className="h-6 w-6" />
        </button>
      )}
      {disableLogo ? null : (
        <div className="flex items-center justify-center px-6">
          <Logo />
        </div>
      )}
      <div className="flex flex-1 px-6 space-x-6 items-center justify-end">
        {children}
        {!isAuthorized || disableNotifications ? null : (
          <div className="relative">
            <button
              onClick={(notificationsActions as Actions).toggle}
              className="relative p-1 text-gray-500 border-2 border-transparent rounded-full hover:text-gray-400 focus:outline-none focus:bg-cool-gray-100 focus:text-gray-500 transition duration-150 ease-in-out"
              aria-label="Notifications"
            >
              <BellOutline className="h-6 w-6" />
              <span className="absolute top-0 right-0 w-2 h-2 rounded-full bg-green-400" />
            </button>
          </div>
        )}
        {!isAuthorized || disableAvatar ? null : (
          <div className="relative">
            <button
              onClick={(menuActions as Actions).toggle}
              className="max-w-xs flex items-center text-sm rounded-full focus:outline-none focus:bg-gray-200"
              id="user-menu"
              aria-label="User menu"
              aria-haspopup="true"
            >
              <Avatar radius="full" size="sm" src={avatar} />
            </button>
            <DashboardMenu />
          </div>
        )}
        {!isAuthorized && (
          <div>
            <Button
              size="lg"
              variant="secondary"
              component={Link}
              to={`/login?redirect=${location?.pathname}`}
            >
              Sign in
            </Button>
            <Button
              variant="primary"
              className="ml-3"
              component={Link}
              to={`/signup?redirect=${location?.pathname}`}
            >
              Sign up
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

DashboardHeader.defaultProps = {
  disableLogo: true,
};

export default DashboardHeader;
