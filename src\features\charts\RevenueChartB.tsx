import { FC, useCallback } from 'react';
import { Skeleton } from 'components/skeleton';
import {
  CartesianGrid,
  YAxis,
  XAxis,
  ResponsiveContainer,
  BarChart,
  Bar,
  Cell,
  TooltipProps,
  Tooltip,
} from 'recharts';
import { SimpleText } from 'components/typography';
import EmptyChart from './EmptyChart';
import formatCurrency from 'features/utils/formatCurrency';
import { Translate } from '@/components/translate';
import clsx from 'features/utils/clsx';
import formatCurrencyUtil from 'features/utils/formatCurrency';

interface RevenueChartProps {
  loading: boolean;
  data: Record<string, string | number>[];
}

const RevenueChart: FC<RevenueChartProps> = ({ loading, data = [] }) => {
  const formatter = useCallback(
    (value: number) => [formatCurrency(value), 'Earnings'],
    []
  );

  return (
    <Skeleton active={loading} className="h-full">
      <div
        className={clsx('rounded relative', {
          'h-full': data.length === 0,
        })}
      >
        <div className="flex justify-between">
          <SimpleText truncate className=" text-base font-medium">
            <Translate msgId={"Overview"} />
          </SimpleText>
          <SimpleText truncate className=" text-sm text-gray-400">
            <Translate msgId={"Monthly"} />
          </SimpleText>

        </div>
        <div
          className={clsx('h-full aspect-w-16 aspect-h-9', {
            'mt-16': data.length > 0,
          })}
        >
          <div className="flex items-center h-full justify-center">
            {data.length === 0 ? (
              <EmptyChart variant="bar" />
            ) : (
              <ResponsiveContainer minHeight={300}>
                <BarChart width={150} height={40} data={data} >
                <Tooltip content={<CustomTooltip />} formatter={formatter} />

                  <CartesianGrid vertical={false} strokeDasharray="3 3" />
                  <YAxis stroke="#6B7280" tickLine={false} />
                  <XAxis stroke="#6B7280" tickLine={false} dataKey="month" />
                  <Bar dataKey="earning">
                    {data.map((_, index) => (
                      <Cell cursor="pointer" fill={'#82ca9d'} key={`cell-${index}`} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            )}
          </div>
        </div>
      </div>
    </Skeleton>
  );
};

export default RevenueChart;


const CustomTooltip: React.FC<TooltipProps<any, any>> = ({
  active,
  payload,
  label,
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-raven-green-800 py-3 px-5 text-white rounded-lg ">
        <p className="font-semibold">{`${label} `}</p>
        {payload.map((entry, index) => (
          <div key={index}>
            <p className="capitalize">
              {entry.name.replace('_', ' ')}:{' '}
              <span className="font-semiboldd">
                {formatCurrencyUtil(entry.value)}
              </span>
            </p>
          </div>
        ))}
      </div>
    );
  }
  return null;
};