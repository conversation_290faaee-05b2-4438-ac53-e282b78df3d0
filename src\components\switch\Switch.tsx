import { FC } from 'react'

interface SwitchProps {
  id: string;
  checked: boolean;
  onChange: () => void;
}

const Switch: FC<SwitchProps> = ({ id, checked, onChange }) => {
  return (
    <div className='relative w-10 h-6'>
      <input type="checkbox" id={id} checked={checked} onChange={onChange} className='hidden peer'  />
      <label htmlFor={id} className=' peer-checked:justify-end w-10 transition-all duration-500  peer-checked:bg-raven-green-800 flex px-1 items-center  h-6 cursor-pointer bg-gray-200 rounded-full'>
        <div className="w-5 h-5 transition-all duration-500 rounded-full bg-white"></div>
      </label>
    </div>
  );
}
export default Switch