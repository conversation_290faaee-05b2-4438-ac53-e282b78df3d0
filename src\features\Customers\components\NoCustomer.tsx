import { SimpleText } from '@/components/typography';
import noData from '/images/no-data.png';
import { Translate } from '@/components/translate';
import { FC, ReactNode } from 'react';

interface NoCustomerProps {
  children?: ReactNode
}

const NoCustomer: FC<NoCustomerProps> = ({children}) => {
  return (
    <div className="px-5  py-3 flex flex-col items-center justify-center">
      <img src={noData} alt="no data "/>

      <SimpleText className="font-medium text-xl">
        <Translate msgId="dashboard.noData" />
      </SimpleText>

      {children}
    </div>
  )
}

export default NoCustomer