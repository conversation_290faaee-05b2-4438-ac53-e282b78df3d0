// Importing required modules from i18next and react-i18next
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// Importing translation JSON files for different languages
import en from "../translations/en.json";

// Resources object to hold the translations for each language
const resources = {
    en: {
        translation: en // English translations
    },
};


// Initialize and configure i18n
i18n
    .use(initReactI18next) // Using react-i18next for React projects
    .init({
        resources,         // Resource object for translation strings
        lng: "en",         // Default language to use (English)
        keySeparator: '.',  // Set to '.' to enable nested keys

        // Interpolation settings
        interpolation: {
            escapeValue: false // Not escaping interpolated values allows you to include HTML in translations
        }
    });

// Exporting the configured i18n instance for use in the application
export default i18n;
