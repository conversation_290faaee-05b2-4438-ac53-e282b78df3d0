import { FC, ReactNode, RefObject } from 'react';
import useClickAway from '@/hooks/useClickAway';
import { Transition } from '@headlessui/react';
import clsx from '@/features/utils/clsx';

interface DashboardPopoutProps {
  active?: boolean;
  children: ReactNode;
  className?: string;
  classNameContent?: string;
  // eslint-disable-next-line no-unused-vars
  onRequestClose: (event: MouseEvent | KeyboardEvent) => any;
}

const DashboardPopout: FC<DashboardPopoutProps> = ({
  active = false,
  children,
  className = 'origin-top-right right-0',
  classNameContent,
  onRequestClose,
}) => {
  const ref = useClickAway(onRequestClose, active);

  return (
    <Transition
      show={active}
      enter="transition ease-out duration-100"
      enterFrom="transform opacity-0 scale-95"
      enterTo="transform opacity-100 scale-100"
      leave="transition ease-in duration-75"
      leaveFrom="transform opacity-100 scale-100"
      leaveTo="transform opacity-0 scale-95"
      className={clsx(
        'absolute mt-2 w-screen max-w-sm rounded-md shadow-lg border border-gray-200 z-full',
        className
      )}
    >
      <div
        ref={ref as RefObject<HTMLDivElement>}
        className={clsx(
          'rounded-md bg-white shadow-xs overflow-hidden',
          classNameContent
        )}
        role="menu"
        aria-orientation="vertical"
        aria-labelledby="user-menu"
      >
        {children}
      </div>
    </Transition>
  );
};

export default DashboardPopout;
