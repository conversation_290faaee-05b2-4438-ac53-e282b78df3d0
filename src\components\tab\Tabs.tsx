import { FC } from 'react';
import Tab from './Tab';
import { TabProps } from '@/models/tab-routes';
import clsx from '@/features/utils/clsx';

interface TabsProps {
  routes: TabProps[];
  className?: string;
  inset?: boolean;
  noBorder?: boolean;
}

const Tabs: FC<TabsProps> = ({ routes, className, inset, noBorder }) => {
  return (
    <div
      className={clsx(
        'border-b',
        {
          'border-gray-200': !noBorder,
          'border-transparent': noBorder,
        },
        className
      )}
    >
      <div
        className={clsx('max-w-7xl mx-auto', {
          'px-4 sm:px-6 lg:px-8': inset,
        })}
      >
        <nav className="-mb-px flex space-x-8">
          {routes.map((route) => (
            <Tab key={route.label} {...route} />
          ))}
        </nav>
      </div>
    </div>
  );
};

export default Tabs;
