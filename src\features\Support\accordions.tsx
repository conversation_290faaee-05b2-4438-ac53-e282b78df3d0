import { Accordion } from '@/components/accordion';
import { Trans } from 'react-i18next';
import { Link } from 'react-router-dom';
export default function Accordions() {
  const questions = [
    {
      title: 'support.faq1T',
      content: 'support.faq1Content'
    },
    {
      title: 'support.faq2T',
      content: 'support.faq2Content'
    },
    {
      title: 'support.faq3T',
      content: (
        <Trans
          i18nKey="support.faq3Content"
          components={{
            ol: <ol className="list-decimal pl-5 space-y-2" />,
            li: <li />,
            b: <b />,
            a: <Link to="/" className="text-raven-link" />
          }}
        />
      )
    }
  ];

  return (
    <section className="py-5">
      <div className="space-y-5 w-full mx-auto px-5">
        <Accordion data={questions} itemClass="bg-gray-100 " />
      </div>
    </section>
  );
}
