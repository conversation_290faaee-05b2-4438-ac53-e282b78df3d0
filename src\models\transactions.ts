
export interface Transaction {
  id: string;
  timestamp: string;
  type: string;
  status: string;
  name: string;
  amount: number;
  accountNumber: number;
  photo: string;
}
export interface TransactionDetails {
  id: string;
  timestamp: string;
  type: string;
  status: string;
  name: string;
  amount: number;
  accountNumber: number;
  photo: string;
  paymentMethod: string,
  dueDate: string,
  serviceCharge: number,
  note: string,
}