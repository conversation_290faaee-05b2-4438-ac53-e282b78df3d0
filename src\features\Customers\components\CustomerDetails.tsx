import { But<PERSON> } from "@/components/button";
import { Avatar } from "@/components/avatar";
import { Translate } from "@/components/translate";
import { SimpleText } from "@/components/typography";
import { useSelector } from "react-redux";
import { getCustomer } from "../cusomterSlice";
import { ChevronDown } from "heroicons-react";

const CustomerDetails = () => {
  const customer = useSelector(getCustomer);

  return (
    <div className="border-none space-y-5 bg-gray-100 p-5 rounded">
      <div className=" md:flex justify-between items-center">
        <div className=" flex gap-3 items-center flex-1">
          <Avatar radius="full" size="lg" src={'/images/profile.png'} />
          <SimpleText truncate className="text-sm md:text-lg font-semibold">
            {customer?.full_name}
          </SimpleText>
        </div>

        <Button variant="light" className=" flex items-center justify-between gap-2 w-full md:w-[150px]">
          <Translate msgId="dashboard.action" />
          <b>
            <ChevronDown />
          </b>
        </Button>
      </div>

      <div className=" bg-white p-5 space-y-5 rounded-md">
        <SimpleText className=" text-xl">
          <Translate msgId="dashboard.customerDetails" />
        </SimpleText>

        <div className="mb-3 form-group">
          <SimpleText>
            <Translate msgId="dashboard.companyName" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-500 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full"
            type="text"
            value={customer?.company_name}
            readOnly
          />
        </div>

        <div className="mb-3 form-group">
          <SimpleText>
            <Translate msgId="dashboard.fullName" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-500 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full"
            type="text"
            value={customer?.full_name}
            readOnly
          />
        </div>

        <div className="mb-3 form-group">
          <SimpleText>
            <Translate msgId="dashboard.emailAddress" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-500 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full"
            type="text"
            value={customer?.email}
            readOnly
          />
        </div>

        <div className="mb-3 form-group">
          <SimpleText>
            <Translate msgId="dashboard.phoneNumber" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-500 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full"
            type="text"
            value={customer?.phone_number}
            readOnly
          />
        </div>
      </div>
    </div>
  )
};

export default CustomerDetails;
