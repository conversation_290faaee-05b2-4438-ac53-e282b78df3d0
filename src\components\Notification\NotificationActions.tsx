import clsx from '@/features/utils/clsx';
import { FC, ReactNode } from 'react';

interface AlertActionsProps {
  className?: string;
  children: ReactNode;
}

const AlertActions: FC<AlertActionsProps> = ({ className = '', children }) => {
  const rootClass = clsx('flex flex-col divide-y divide-gray-200', className);

  return <div className={rootClass}>{children}</div>;
};

export default AlertActions;
