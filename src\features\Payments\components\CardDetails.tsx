import { Card, CardContent, CardHeader } from '@/components/Card';
import { Translate } from '@/components/translate';
import CardDetailsForm from './CardDetailsForm';

const CardDetails = () => {
  return (
    <Card
      variant="outlined"
      className="w-full bg-raven-green-50 lg:py-10 lg:px-12"
    >
      <CardHeader
        primary={<Translate msgId="dashboard.cardDetails" />}
        secondary={<Translate msgId="dashboard.enterCardDetails" />}
      />
      <CardContent className="bg-white">
        <CardDetailsForm />
      </CardContent>
    </Card>
  );
};

export default CardDetails;
