import { Button } from '@/components/button';
import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import heroImage from '/images/documentationHero.png';
import { Link } from 'react-router-dom';

export default function Hero() {
  return (
    <div className="flex w-full flex-col lg:flex-row items-center md:mt-16 mt-5 ">
      <section className="flex flex-col gap-y-5 w-full">
        <SimpleText className="text-4xl font-semibold">
          <Translate msgId="documentation.developers" />
        </SimpleText>
        <SimpleText className="md:w-2/3 w-full">
          <Translate msgId="documentation.hero" />
        </SimpleText>
        <Button variant="secondary" to="/docs" component={Link}>
          <SimpleText className="rounded-lg w-max border-2 !border-raven-green-800 p-2">
            <Translate msgId="home.docs" />
          </SimpleText>
        </Button>
      </section>
      <img
        src={heroImage}
        alt="intelligence"
        className="rounded-3xl md:mt-0 mt-5"
      />
    </div>
  );
}
