import { configureStore } from "@reduxjs/toolkit";
import logger from 'redux-logger';
import { exchangeAppSlice } from "@/features/Landing/exchange-app/exchangeAppSlice";
import { registerSlice } from "@/features/Signup/SignupSlice";
import { loginSlice } from "@/features/Signin/LoginSlice";
import { authSlice } from "@/features/Auth/authSlice";
import rtkQueryBase from "@/services/api/rtk-query-base";
import notificationsSlice from "features/Notifications/notificationsSlice";
import { paymentSlice } from "@/features/Payments/paymentsSlice";
import { customerSlice } from "@/features/Customers/cusomterSlice";
import customerBaseQuery from "@/services/api/customer-base-query";
import { settingsSlice } from "@/features/Settings/settingsSlice";
import settingsBaseQuery from "@/services/api/settings-base-query";
import { transactionSlice } from "@/features/Transactions/transactionsSlice";
import transactionBaseQuery from "@/services/api/transaction-base-query";
import userBaseQuery from "@/services/api/user-base-query";
import { userSlice } from "@/features/Admin/adminSlice";

export const store = configureStore({
    reducer: {
        [exchangeAppSlice.name]: exchangeAppSlice.reducer,
        [registerSlice.name]: registerSlice.reducer,
        [loginSlice.name]: loginSlice.reducer,
        [authSlice.name]: authSlice.reducer,
        [notificationsSlice.name]: notificationsSlice.reducer,
        [rtkQueryBase.reducerPath]: rtkQueryBase.reducer,
        [customerBaseQuery.reducerPath]: customerBaseQuery.reducer,
        [paymentSlice.name]: paymentSlice.reducer,
        [customerSlice.name]: customerSlice.reducer,
        [settingsBaseQuery.reducerPath]: settingsBaseQuery.reducer,
        [settingsSlice.name]: settingsSlice.reducer,
        [transactionBaseQuery.reducerPath]: transactionBaseQuery.reducer,
        [transactionSlice.name]: transactionSlice.reducer,
        [userBaseQuery.reducerPath]: userBaseQuery.reducer,
        [userSlice.name]: userSlice.reducer,

    },
    middleware(getDefaultMiddleware) {

        const defaultMiddleWare = getDefaultMiddleware({ serializableCheck: false })

        // if we are in a development environment we will attach a logger
        if (process.env.NODE_ENV === 'development') {

            return defaultMiddleWare.concat(rtkQueryBase.middleware).concat(customerBaseQuery.middleware).concat(settingsBaseQuery.middleware).concat(logger).concat(transactionBaseQuery.middleware)
        }

        return defaultMiddleWare.concat(rtkQueryBase.middleware).concat(customerBaseQuery.middleware).concat(settingsBaseQuery.middleware).concat(transactionBaseQuery.middleware);
    },
})


// Inferred type: { auth: AuthState }
export type AppDispatch = typeof store.dispatch;

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
