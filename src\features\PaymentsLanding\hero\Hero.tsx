import { Button } from '@/components/button'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'
import { Trans } from 'react-i18next'
import { Link } from 'react-router-dom';

const Hero = () => {
  return (
    <section>
      <div className="mt-10 grid grid-cols-1 items-center lg:grid-cols-2 gap-5 md:gap-10 ">
        <div className='flex flex-col gap-5'>
          <div>
            <SimpleText
              component="h1"
              className="md:text-6xl text-raven-green-800 text-5xl font-extrabold leading-tight"
            >
              <Trans
                i18nKey="landing.payments"
                components={{
                  style1: (
                    <span className="text-raven-green-800 leading-tight" />
                  ),
                  style2: (
                    <span className="text-raven-green-800 leading-tight" />
                  ),
                }}
              />
            </SimpleText>
          </div>
          <div className="mt-6">
            <SimpleText component="h2" className="text-lg font-light">
              <Trans
                i18nKey="landing.paymentHeroSubtitle"
                components={{
                  span: (
                    <span className="font-bold leading-tight" />
                  )
                }}
              />
            </SimpleText>
            <SimpleText component="h2" className="text-lg mt-5 font-light">
              <Translate msgId="landing.paymentHeroDes" />
            </SimpleText>
          </div>
          <div>
            <div className="mt-6 flex-col md:flex-row gap-3 flex md:items-center">
              <Button
                variant="darkPrimary"
                className="mr-5 rounded"
                type="button"
              >
                <Translate msgId="landing.contactSales" />
              </Button>
              <Button
                variant="light"
                className="mr-5 rounded !text-raven-green-800"
                type="button"
              >
                <Button
                  variant="light"
                  component={Link}
                  to="/pricing"
                >

                  <Translate msgId="landing.seePricing" />
                </Button>
              </Button>
            </div>
          </div>
        </div>
        <div className="  flex items-center justify-start lg:justify-end">
          <div className="w-full rounded-xl">
            <video className='rounded-xl ' controls={false} autoPlay={true} loop={true}>
              <source src='/videos/payment-hero.mp4' type="video/mp4" />
              Your browser does not support the video tag.
            </video>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero

