import { SimpleText } from '@/components/typography';
import fin_tech from '/images/fintech.jpg';
import marketplace from '/images/marketplace.jpg';
import retail from '/images/retail.jpg';
import travel from '/images/travel.png';
import education from '/images/education.jpg';
import eCommerce from '/images/ecommerce.jpg';

import { ReactNode } from 'react';
import { Trans } from 'react-i18next';
interface consumerCardProps {
  image: string;
  service: ReactNode | string;
}

const ConsumerCard = ({ image, service }: consumerCardProps) => {
  return (
    <div className=" w-full lg:w-[350px] sm:h-[350px] relative bg-gray-200 h-[350px] rounded-[20px] overflow-hidden">
      <img
        src={image}
        className=" absolute right-0 top-0  w-full h-full object-cover rounded-[20px]"
      />
      <div className="absolute bg-black/50 backdrop-blur-md p-10 -bottom-32 -right-16 min-w-[250px] flex items-center min-h-[250px] rounded-full">
        <div className="pb-20 text-white">
          {typeof service == 'string' ? (
            <SimpleText>
              <Trans i18nKey={service} />
            </SimpleText>
          ) : (
            <SimpleText>{service}</SimpleText>
          )}
        </div>
      </div>
    </div>
  );
};
const Consumers = () => {
  const consumerCardData = [
    {
      image: fin_tech,
      service: (
        <Trans
          i18nKey="Financial services <br/>& Fintech"
          components={{ br: <br /> }}
        />
      ),
      alt: 'financial services and fintech'
    },
    {
      image: education,
      service: 'Education'
    },
    {
      image: eCommerce,
      service: 'eCommerce'
    },
    {
      image: retail,
      service: 'Retail'
    },
    {
      image: marketplace,
      service: 'Marketplace'
    },
    {
      image: travel,
      service: 'Travel'
    }
  ];

  return (
    <section className="space-y-10 py-8 md:py-20">
      <SimpleText className="text-center text-gray-500">
        Reach millions of untapped consumers in Africa with local payment
        methods.
      </SimpleText>

      {/* Card container */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:flex lg:overflow-x-auto hide-scrollbar">
        {consumerCardData.map(({ image, service }, index) => (
          <div key={index}>
            <ConsumerCard image={image} service={service} />
          </div>
        ))}
      </div>
    </section>
  );
};

export default Consumers;
