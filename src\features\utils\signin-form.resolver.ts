import { Resolver } from 'react-hook-form';
import { SigninFormData } from '@/models/signin-form-data';

export const resolver: Resolver<SigninFormData> = async (values) => {
    const errors: any = {};

    // Email validation
    if (!values.email) {
        errors.email = {
            type: 'required',
            message: 'Please enter your email',
        };
        // test to validate email format
    } else if (!/^\S+@\S+\.\S+$/.test(values.email)) {
        errors.email = {
            type: 'pattern',
            message: 'Please enter a valid email address',
        };
    }

    // password validation
    if (!values.password) {
        errors.password = {
            type: 'required',
            message: 'Please enter your password',
        };
    }

    return {
        values,
        errors,
    };
};
