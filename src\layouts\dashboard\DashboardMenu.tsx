import useMenu from '@/hooks/useMenu';
import DashboardPopout from './DashboardPopout';
import DashboardUserNavigation from './DashboardUserNavigation';
import DashboardUserCard from './DashboardUserCard';
import { MenuAction } from '@/models/menu-actions.interface';

const DashboardMenu = () => {
  const [menuActive, menuActions] = useMenu();

  return (
    <DashboardPopout
      active={menuActive as boolean}
      onRequestClose={(menuActions as MenuAction).off}
    >
      <DashboardUserCard />
      <DashboardUserNavigation className="border-t" />
    </DashboardPopout>
  );
};

export default DashboardMenu;
