// LinkText.jsx
import { FC, ReactNode } from 'react';
import { Link } from 'react-router-dom';

interface LinkTextProps {
  to: string;
  title: string;
  children: ReactNode;
  newTab?: boolean;
}

const LinkText: FC<LinkTextProps> = (props) => {
  return (
    <Link
      to={props.to}
      {...(props.newTab ? { target: '_blank' } : {})}
      title={props.title}
      className="text-raven-link"
    >
      {props.children}
    </Link>
  );
};

export default LinkText;
