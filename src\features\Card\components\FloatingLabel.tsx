import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"
import { ChatAlt } from "heroicons-react"
import CircleCheck from '/images/circle-check.png';

const FloatingLabel = () => {
  return (
    <div className=" mt-10 border absolute -top-14 md:right-24 -right-5 l lg:right-10 rounded-xl bg-white md:-top-14 z-50">
      <div className=" p-4 flex gap-3 items-center">
        <img
          src={CircleCheck}
          alt="Phone showing raven app"
          className="shrink-0 object-contain w-14"
        />
        <div className="">
          <SimpleText className="text-lg ">
            <Translate msgId={'card.paymentDone'} />
          </SimpleText>
          <SimpleText className="text-base text-gray-300 ">
            <Translate msgId={'card.transactWithRaven'} />
          </SimpleText>
        </div>
        <div className="h-9 w-9 p-1 rounded-full flex items-center justify-center bg-gradient-to-r via-[#108440]/20 from-[#108440]/30 to-[#108440]/30">
          <ChatAlt className=' text-[#108440]/50' />
        </div>
      </div>
    </div>
  )
}

export default FloatingLabel