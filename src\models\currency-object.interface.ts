export type CurrencyObject = { id: string; flag: string; sign: string; label: string };

export const currencyList: CurrencyObject[] = [
    {
        id: 'USD',
        flag: '🇺🇸',
        sign: '$',
        label: 'United States Dollar',
    },
    {
        id: 'GBP',
        flag: '🇬🇧',
        sign: '£',
        label: 'Great British Pound',
    },
    {
        id: 'EUR',
        flag: '🇪🇺',
        sign: '€',
        label: 'Euro',
    },
    {
        id: 'NGN',
        flag: '🇳🇬',
        sign: '₦',
        label: '<PERSON><PERSON>',
    },
    {
        id: 'GHS',
        flag: '🇬🇭',
        sign: '₵',
        label: 'Ghana Cedi',
    },
];
