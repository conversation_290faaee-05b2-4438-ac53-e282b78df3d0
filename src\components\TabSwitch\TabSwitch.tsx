import { Disp<PERSON>, FC, SetStateAction, useState } from 'react';
import { Button } from '../button';
import { Translate } from '../translate';

interface TabSwitchProps {
  tabs: {id: string, label: string}[]
  defaultTab: string;
  variant?: 'dark' | 'darkPrimary'
  onClick?: () => void;
  setCurrentTab?: Dispatch<SetStateAction<string>>;
}

const TabSwitch: FC<TabSwitchProps> = ({tabs, defaultTab,setCurrentTab, onClick, variant='dark'}) => {
    const [activeTab, setActiveTab] = useState<string>(defaultTab);

    const handleTabChange = (id: string)=>{
      setActiveTab(id);
      setCurrentTab?.(id);
      onClick?.()
    }

    return (
      <div className="bg-gray-100 flex justify-between rounded-l lg:w-max rounded-lg">
        {tabs.map((tab, index) => (
          <Button
            variant={`${activeTab === tab.id ? variant : 'transparent'}`}
            key={index+tab.id}
            className='rounded-lg '
            onClick={()=> handleTabChange(tab.id)}
          >
            <Translate msgId={tab.label} />
          </Button>
        ))}
      </div>
    );
};

export default TabSwitch;
