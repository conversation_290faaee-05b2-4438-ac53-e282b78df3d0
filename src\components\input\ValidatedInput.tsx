import { FC, JSX } from 'react';
import { FieldErrors, UseFormRegister } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Translate } from 'components/translate';
import { SimpleText } from 'components/typography';
import clsx from '@/features/utils/clsx';
import { SignupFormData } from '@/features/Auth/schemas/signupSchema';

interface ValidatedInputProps {
  msgId: string;
  ariaLabel: string;
  field: keyof SignupFormData;
  type: string;
  inputClass: string;
  className: string;
  placeholder?: string;
  hint?: string;
  register: UseFormRegister<SignupFormData>;
  errors: FieldErrors<SignupFormData>;
}

const ValidatedInput: FC<ValidatedInputProps> = (
  props: ValidatedInputProps
): JSX.Element => {
  const { t } = useTranslation();

  return (
    <div className={props.className}>
      <label className="form-label dark:text-white">
        <Translate msgId={props.msgId} />
      </label>
      <input
        className={clsx(
          props.inputClass,
          'dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
        )}
        type={props.type}
        {...props.register(props.field)}
        aria-label={props.ariaLabel}
        {...(props.placeholder ? { placeholder: t(props.placeholder) } : {})}
      />
      {props.hint && (
        <span className="text-sm text-gray-400">
          <Translate msgId={props.hint} />
        </span>
      )}
      {props.errors[props.field] && (
        <SimpleText component="small" className="mt-1 text-red-500">
          <Translate msgId={(props.errors[props.field]!.message as string)!} />
        </SimpleText>
      )}
    </div>
  );
};

export default ValidatedInput;
