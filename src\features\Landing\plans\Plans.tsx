import PlanCard from '@/components/cards/PlanCard';
import { SimpleText } from '@/components/typography';
import features from '@/data/plan-features.json';
import formatCurrencyUtil from '@/features/utils/formatCurrency';
import { BadgeColor } from '@/models/badge-props';
import { ButtonVariants } from '@/models/button-props.interface';
import { Trans } from 'react-i18next';

const standard = features['standard'];
const premium = features['premium'];
const gold = features['gold'];

const planFeatures = [standard, premium, gold];

const Plans = () => {
  return (
    <div className="mt-20 md:py-20 md:px-10">
      <div className="mb-10 text-center">
        <SimpleText
          component="h2"
          className="font-semibold text-3xl md:text-5xl"
        >
          <Trans
            i18nKey="home.plansToChoose"
            components={{
              style: <span className="text-raven-green-800 leading-tight" />,
            }}
          />
        </SimpleText>
      </div>
      <div className="grid gap-3 md:gap-10 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {planFeatures.map((feature, ind) => (
          <PlanCard
            key={ind}
            price={
              feature.freeTier
                ? (feature.price as string)
                : formatCurrencyUtil(feature.price as number)
            }
            features={feature.features}
            buttonVariant={feature.buttonVariant as ButtonVariants}
            buttonLink={feature.link}
            badgeColor={feature.badgeColor as BadgeColor}
            tier={feature.tier}
            {...(feature.freeTier && { freeTier: feature.freeTier })}
          />
        ))}
      </div>
    </div>
  );
};

export default Plans;
