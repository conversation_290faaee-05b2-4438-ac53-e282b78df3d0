import { Button } from '@/components/button';
import { Text } from '@/components/typography';
import bank from '@/data/bank-options.json';
import { FormLabel } from '@/components/form';
import { Translate } from '@/components/translate';
import { useDispatch, useSelector } from 'react-redux';
import { BankFieldsType } from '@/models/payment-option';
import { ChangeEvent, FormEvent } from 'react';
import { getBankField, setBankFields } from '../paymentsSlice';

const BankDetailsForm = () => {
  const dispatch = useDispatch();
  const bankFields = useSelector(getBankField);

  const handleFieldChange =
    (name: BankFieldsType) =>
    (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      dispatch(setBankFields({ [name]: e.target.value }));
    };

  const handleBankFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  return (
    <form onSubmit={handleBankFormSubmit}>
      <div className="mb-10">
        <FormLabel name="bankName" text="dashboard.selectBank" />
        <select
          onChange={handleFieldChange('bankName')}
          name="bank-name"
          className="border bg-slate-50 w-full border-gray-100 rounded"
        >
          {!bankFields['bankName'] && <option>Select Your Bank</option>}
          {bank.map((bank) => (
            <option value={bank.value} key={bank.name}>
              <Translate msgId={bank.name} />
            </option>
          ))}
        </select>
      </div>
      <div className="mb-10">
        <FormLabel name="bankNumber" text="dashboard.accountNumber" />
        <input
          type="text"
          onChange={handleFieldChange('bankNumber')}
          name="bankNumber"
          value={bankFields['bankNumber']}
          className="border bg-slate-50 w-full border-gray-100 rounded"
          placeholder="XXXX-XXXXX-XXXXX-XXXX"
        />
      </div>
      <div className="mb-10">
        <div>
          <FormLabel name="bankAccountName" text="dashboard.accountName" />
          <input
            type="text"
            onChange={handleFieldChange('bankAccountName')}
            name="bankAccountName"
            value={bankFields['bankAccountName']}
            placeholder="Kofi Obeng"
            className="border bg-slate-50 w-full border-gray-100 rounded"
          />
        </div>
      </div>
      <Button variant="darkPrimary" className="w-full rounded-lg">
        <Text size="sm" shade="white">
          <Translate msgId="dashboard.addAccount" />
        </Text>
      </Button>
    </form>
  );
};

export default BankDetailsForm;
