import { ComponentType, FC, useMemo } from 'react';
import { Route } from 'react-router-dom';
import DashboardRestricted from './DashboardRestricted';

interface DashboardProfileRouteProps {
  component: string;
  type: string;
  [key: string]: any;
}

const DashboardProfileRoute: FC<DashboardProfileRouteProps> = ({
  component,
  type,
  ...rest
}) => {
  const isMerchant = sessionStorage.getItem('merchant');
  const isAdmin = sessionStorage.getItem('admin');
  const isType = useMemo(
    () => (isMerchant ? 'merchant' : 'individual'),
    [isMerchant]
  );

  const Component = useMemo(() => {
    if (isAdmin || isType === type) {
      return component;
    }

    return DashboardRestricted;
  }, [type, isType, isAdmin, component]);

  return <Route {...rest} Component={Component as ComponentType} />;
};

export default DashboardProfileRoute;
