import waves from '/icons/waves.svg';
import visa from '/icons/visa.svg';
import { SimpleText } from 'components/typography';
import { FC } from 'react';
import clsx from '@/features/utils/clsx';

interface ATMCardProps {
  className?: string;
}

const ATMCard: FC<ATMCardProps> = ({ className }) => {
  return (
    <div className={clsx('rounded-3xl card-blurred p-8', className)}>
      <div className="flex flex-col gap-28">
        <div className="flex items-center justify-between">
          <div>
            <img src={waves} alt="waves" />
          </div>
          <div>
            <img src={visa} alt="visa logo" />
          </div>
        </div>
        <div>
          <SimpleText className="text-3xl">7200 9690 5570 8874</SimpleText>
          <SimpleText className="text-lg">NEERAJ KR. GUPTA</SimpleText>
          <SimpleText className="text-lg">01/23</SimpleText>
        </div>
      </div>
    </div>
  );
};

export default ATMCard;
