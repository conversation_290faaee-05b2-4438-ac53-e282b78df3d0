import { useSelector } from 'react-redux';
import { getOption } from '../paymentsSlice';
import { useMemo } from 'react';
import { paymentOptionStrings } from '@/models/payment-option';
import CardDetails from './CardDetails';
import BankDetails from './BankDetails';
import MomoDetails from './MomoDetails';

const SelectedPaymentDetails = () => {
  const selectedOption = useSelector(getOption);

  const Component = useMemo(() => {
    switch (selectedOption) {
      case paymentOptionStrings.card:
        return CardDetails;
      case paymentOptionStrings.bank:
        return BankDetails;
      case paymentOptionStrings.momo:
        return MomoDetails;

      default:
        return 'div';
    }
  }, [selectedOption]);

  return selectedOption && <Component />;
};

export default SelectedPaymentDetails;
