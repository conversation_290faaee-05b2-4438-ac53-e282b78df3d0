import { Outlet } from 'react-router-dom';
import DashboardHomeNew from './DashboardHomeNew';
import useMatchPath from '@/hooks/useMatchPath';

const Dashboard = ({ ...props }) => {
  const isDashboardHomePath = useMatchPath('/dashboard');

  // return isDashboardHomePath ? <DashboardHome /> : <Outlet {...props} />;
  return isDashboardHomePath ? <DashboardHomeNew /> : <Outlet {...props} />;
};

export default Dashboard;
