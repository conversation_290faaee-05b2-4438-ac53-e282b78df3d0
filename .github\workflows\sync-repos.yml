name: Sync to Personal Repo

on:
  push:
    branches:
      - main

jobs:
  sync:
    if: github.repository == 'getraven/raven-web'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the code
        uses: actions/checkout@v3

      - name: Push to Private Repository
        run: |
          git remote add personal https://davidquartz:<EMAIL>/davidquartz/raven-web.git
          git push personal main --force
