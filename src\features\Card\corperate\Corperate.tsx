import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import Phone from '/images/phone-co.png';
import { Button } from '@/components/button';
import ravenLogo from '/images/logo-header.png';

const Corperate = () => {
  return (
    <section className="md:py-20 md:px-10">
      <section className=" container mx-auto   bg-raven-dark-900  rounded-2xl ">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
          <div className="relative w-full px-5 md:px-12  rounded-3xl">
            <img
              src={Phone}
              alt="Phone showing raven app"
              className="shrink-0 object-contain w-64 mx-auto"
            />
            <div className=" mt-14 h-20 border absolute top-14 lg:-top-50  md:top-14 backdrop-blur-md right-5 left-5 w-64 mx-auto rounded-2xl bg-white/80 z-50"/>
            <div className=" mt-10 border absolute lg:-top-50 top-14  md:top-14 backdrop-blur-md right-5 left-5 w-72 mx-auto rounded-xl bg-white/80 z-50">
              <div className=" p-4 space-y-1">
                <img
                  src={ravenLogo}
                  alt="Phone showing raven app"
                  className="shrink-0 object-contain w-10"
                />
                <div className=" w-64 ">
                  <SimpleText className="text-base ">
                    <Translate msgId={'card.onlinePurchase'} />
                  </SimpleText>
                  <SimpleText className="text-gray-400 text-xs">
                    <Translate msgId={'card.onlinePurchaseDesc'} />
                  </SimpleText>
                </div>
              </div>
            </div>
          </div>
          <div className="flex p-5 md:p-10 lg:p-0 flex-col justify-center lg:space-y-10 order-first lg:order-last w-full dark:text-white text-gray-800  md:pr-16 mb-10 md:mb-0">
            <SimpleText className=" text-raven-green-800 text-3xl ">
              <Translate msgId="card.corperateTitle" />
            </SimpleText>
            <SimpleText component="h1" className="text-gray-400  ">
              <Translate msgId="card.corperateDesc" />
            </SimpleText>

            <div className="">
              <Button
                variant="darkPrimary"
                className="mt-5 rounded"
                type="button"
              >
                <Translate msgId="card.corperateCards" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </section>
  );
};

export default Corperate;
