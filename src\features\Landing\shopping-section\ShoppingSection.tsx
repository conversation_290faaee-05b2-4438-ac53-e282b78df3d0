import { Button } from '@/components/button';
import { SimpleText } from 'components/typography';
import { Translate } from '@/components/translate';
import shoppingGirl from '/images/shopping-girl.png';

const ShoppingSection = () => {
  return (
    <section className="py-20 mt-20">
      <div>
        <div className="flex flex-wrap items-center justify-between">
          <div className="space-y-10 lg:p-5 md:w-1/2 w-full">
            <div className="space-y-5 w-full">
              <SimpleText
                component="h3"
                className="text-2xl text-raven-green-900 font-semibold"
              >
                <Translate msgId="home.lifestyle" />
              </SimpleText>
              <SimpleText
                component="h2"
                className="text-5xl leading-normal text-raven-green-500 font-semibold"
              >
                <Translate msgId="home.dayToDay" />
              </SimpleText>
            </div>
            <div className="w-full space-y-5">
              <SimpleText
                component="h3"
                className="text-5xl leading-normal text-raven-green-900 font-semibold"
              >
                <Translate msgId="home.shopping" />
              </SimpleText>
              <SimpleText>
                <Translate msgId="home.earnMore" />
              </SimpleText>
            </div>
            <div>
              <Button variant="primary">
                <Translate msgId="home.startShopping" />
              </Button>
            </div>
          </div>
          <div className="p-5 md:w-1/2 w-full">
            <img
              src={shoppingGirl}
              alt="girl holding shopping bags"
              className="rounded"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ShoppingSection;
