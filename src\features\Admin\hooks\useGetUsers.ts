import { useGetUsersQuery } from '@/services/endpoints/users';
import { useMemo } from 'react';

const useGetUsers = (options: { page: number, limit: number, select: any }) => {
    const { page = 1, limit = 10, select } = options;

    return useGetUsersQuery(useMemo(() => ({
        page,
        limit
    }), [
        page,
        limit
    ]), {
        selectFromResult: (result) => select(result)
    });
};

export default useGetUsers;
