import { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import { AppDispatch } from '@/store/store';
import { getError, setCountry, getCountry } from '../SignupSlice';
import { Translate } from '@/components/translate';
import CountrySelector from '@/components/selector/CountrySelector';
import { COUNTRIES } from '@/data/countries';
import { SelectMenuOption } from '@/data/types';
import ValidatedInput from '@/components/input/ValidatedInput';
import { Button } from '@/components/button';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  SignupFormData,
  signupSchema
} from '@/features/Auth/schemas/signupSchema';
import { useSignup } from '@/features/Auth/hooks/useSignup';

const SignupForm = () => {
  const dispatch: AppDispatch = useDispatch();
  const error = useSelector(getError);
  const { handleSignup, signupLoading } = useSignup();

  const {
    handleSubmit,
    register,
    formState: { errors },
    getValues,
    setValue
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema),
    mode: 'all',
    defaultValues: {
      country: 'Ghana'
    }
  });

  const handleCountrySelector = (event: any) => {
    console.log('event from country select: ', event);
    dispatch(setCountry(event.value));
    // Also set the value in the form
    setValue('country', event.title, { shouldValidate: true });
    console.log('form values : ', getValues());
  };

  const [isOpen, setIsOpen] = useState(false);
  const country = useSelector(getCountry);

  return (
    <form className="text-left" onSubmit={handleSubmit(handleSignup)}>
      {!!error && (
        <div className="px-3 py-1 mb-3 bg-red-50 dark:bg-red-900">
          <p className="m-0 text-center text-red-500 dark:text-red-200">
            {error.errors.map((err, index) => (
              <span key={index}>{err.message}</span>
            ))}
          </p>
        </div>
      )}
      <div className="gap-2 mb-4 md:flex">
        <ValidatedInput
          register={register}
          errors={errors}
          className="flex flex-col w-full mb-3 form-group md:w-1/2"
          field="firstName"
          type="text"
          inputClass={`form-control form-input w-full${
            errors.firstName ||
            error?.errors.some((err) => err.field === 'firstName')
              ? ' error-field'
              : ''
          }`}
          msgId="auth.firstName"
          ariaLabel="Enter first name"
          placeholder="Kofi"
        />
        <ValidatedInput
          register={register}
          errors={errors}
          className="flex flex-col w-full mb-3 form-group md:w-1/2"
          field="otherNames"
          type="text"
          inputClass={`form-control form-input w-full${
            errors.otherNames ||
            error?.errors.some((err) => err.field === 'otherNames')
              ? ' error-field'
              : ''
          }`}
          msgId="auth.otherNames"
          ariaLabel="Enter your other names"
          placeholder="Sintim"
        />
        <ValidatedInput
          register={register}
          errors={errors}
          className="flex flex-col w-full mb-3 form-group md:w-1/2"
          field="lastName"
          type="text"
          inputClass={`form-control form-input w-full${
            errors.lastName ||
            error?.errors.some((err) => err.field === 'lastName')
              ? ' error-field'
              : ''
          }`}
          msgId="auth.lastName"
          ariaLabel="Enter last name"
          placeholder="Obeng"
        />
      </div>
      <div className="gap-2 mb-4 md:flex">
        <ValidatedInput
          register={register}
          errors={errors}
          className="flex flex-col w-full mb-3 form-group md:w-1/2"
          field="phoneNumber"
          type="tel"
          inputClass={`form-control form-input w-full${
            errors.phoneNumber ||
            error?.errors.some((err) => err.field === 'phoneNumber')
              ? ' error-field'
              : ''
          }`}
          msgId="auth.phoneNumber"
          ariaLabel="Enter company phone number"
        />
        <div className="flex flex-col mb-3 form-group md:w-1/2">
          <label className="form-label">
            <Translate msgId="auth.country" />
          </label>
          <CountrySelector
            id={'countries'}
            open={isOpen}
            onToggle={() => setIsOpen(!isOpen)}
            onChange={handleCountrySelector}
            selectedValue={
              COUNTRIES.find(
                (option) => option.value === country
              ) as SelectMenuOption
            }
          />
          {/* Hidden input to register country field with react-hook-form */}
          <input type="hidden" {...register('country')} value={country || ''} />
        </div>
      </div>
      <ValidatedInput
        register={register}
        errors={errors}
        className="flex flex-col mb-7 form-group"
        field="email"
        type="email"
        inputClass={`form-control form-input w-full${
          errors.email || error?.errors.some((err) => err.field === 'email')
            ? ' error-field'
            : ''
        }`}
        msgId="auth.companyEmail"
        ariaLabel="Enter company email"
        placeholder="auth.email"
      />
      <ValidatedInput
        register={register}
        errors={errors}
        className="flex flex-col mb-7 form-group"
        field="password"
        type="password"
        inputClass={`form-control form-input w-full${
          errors.password ||
          error?.errors.some((err) => err.field === 'password')
            ? ' error-field'
            : ''
        }`}
        msgId="auth.password"
        ariaLabel="Enter a password"
        hint="auth.passMin"
      />

      <ValidatedInput
        register={register}
        errors={errors}
        className="flex flex-col mb-8 form-group"
        field="confirmPassword"
        type="password"
        inputClass={`form-control form-input w-full${
          errors.confirmPassword ||
          error?.errors.some((err) => err.field === 'confirmPassword')
            ? ' error-field'
            : ''
        }`}
        msgId="auth.confirmPassword"
        ariaLabel="Confirm your password"
      />

      <Button
        loading={signupLoading}
        variant="primary"
        type="submit"
        className="w-full mb-8 rounded"
      >
        <Translate msgId="auth.signup" />
      </Button>
    </form>
  );
};

export default SignupForm;
