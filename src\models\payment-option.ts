export type NetworkOptionsType = 'mtn_momo' | 'voda_cash' | 'at_money'
export type PaymentOptionsType = 'card' | 'bank_payment' | 'mobile_money'
export interface PaymentOptionStrings {
    card: 'card';
    bank: 'bank_payment';
    momo: 'mobile_money';
}
export interface NetworkOptionStrings {
    mtn: 'mtn_momo';
    voda: 'voda_cash';
    atmoney: 'at_money';
}

export const networkOptionStrings: NetworkOptionStrings = {
    mtn: 'mtn_momo',
    voda: 'voda_cash',
    atmoney: 'at_money'
}
export const paymentOptionStrings: PaymentOptionStrings = {
    card: 'card',
    bank: 'bank_payment',
    momo: 'mobile_money'
}

export type CardTypes = 'visa' | 'mastercard' | 'raven_card'

export type BankNames = 'United African Bank' | 'Ecobank' | 'Stanbic Bank'

export type MomoOptions = 'MTN Mobile Money' | 'Tigo Cash' | 'Vodafone Cash'

export type PaymentFieldsType = 'momoFields' | 'bankFields' | 'cardFields'
export interface PaymentFields {
    momo: 'momoFields';
    bank: 'bankFields';
    card: 'cardFields'
}

export const paymentFields: PaymentFields = {
    momo: 'momoFields',
    bank: 'bankFields',
    card: 'cardFields'
}

export type CardFieldsType = 'cardType' | 'cardNumber' | 'expiryDate' | 'cvv'

export type BankFieldsType = 'bankName' | 'bankNumber' | 'bankAccountName'

export type MomoFieldsType = 'momoNetwork' | 'phoneNumber'

export type Payment = {
    id: number,
    status: string,
    type: string,
    name: string,
    amount: number,
    photo: string,
    timestamp: string
}

export type GeneralLinkFormValues = {
    amount: string
    currency?: string
    description?: string
}
  
export type SpecificUserLinkFormValues = {
    email: string
    accountName: string
    accountNumber: string
    amount: string
    currency?: string
    description?: string
}