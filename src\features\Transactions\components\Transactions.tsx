import TransactionsTable from './TransactionsTable';
import { SearchBar } from '@/components/search';
import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { Collection } from 'heroicons-react';
import { Transaction } from '@/models/transactions';
import { Skeleton } from '@/components/skeleton';
import { DashboardContent, DashboardGrid } from '@/layouts/dashboard';
import DashboardHeading from '@/layouts/dashboard/DashboardHeading';
import { FC, ReactNode } from 'react';
import { TabSwitch } from '@/components/TabSwitch';
import NoData from '@/features/Payments/components/NoData';

interface TransactionsProps {
  page?: ReactNode
  transactions: Transaction[]
  loading: boolean
  total: number;
}

const Transactions: FC<TransactionsProps> = ({ page, transactions, loading, total }) => {
  const tabs = [ {id: 'vendors', label: 'dashboard.vendors'}, {id: 'clients', label: 'dashboard.clients'}]

  return (
    <Skeleton active={false}>
      <DashboardContent>
        <DashboardHeading className="mb-5">
          {page}
        </DashboardHeading>
        <DashboardGrid
          className="grid-flow-row-dense"
          gap="gap-3 md:gap-4"
          columns="grid-cols-1"
        >
          <DashboardGrid
            item
            span="md:col-span-2 col-span-4 md:col-start-1 md:col-end-3"
          >
            <div className="bg-white min-h-[500px] px-5 py-10 space-y-5">
              <div className="flex flex-col lg:flex-row w-full gap-5 justify-between items-center">
                <div className="flex-1 w-full">
                  <SearchBar />
                </div>
                <div className="w-full lg:w-auto items-center flex-col lg:flex-row flex gap-2">
                  <Button variant="light" className="flex gap-x-2 flex-row w-full lg:w-auto border rounded-lg  ">
                    <Collection className=' text-gray-300' />
                    <Translate msgId="dashboard.filter" />
                  </Button>
                  <Button variant="darkPrimary" className=" w-full lg:w-auto border rounded-lg  ">
                    <Translate msgId="dashboard.downloadExcel" />
                  </Button>
                </div>
              </div>
              {!loading && !transactions.length ? (
                <NoData />
              ) : (
                <>
                  <TabSwitch tabs={tabs} variant='darkPrimary' defaultTab="vendors"/>
                  <div className="mt-5">
                    <TransactionsTable transactions={transactions} loading={loading} total={total} />
                  </div>
                </>
              )}
            </div>
          </DashboardGrid>
        </DashboardGrid>
      </DashboardContent>
    </Skeleton>
  );
};

export default Transactions;
