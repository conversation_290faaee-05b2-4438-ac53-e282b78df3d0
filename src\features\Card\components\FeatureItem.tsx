import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { FC } from 'react';

interface FeatureItemProps {
  title: string;
  icon: string;
  descriptoin: string;
}
const FeatureItem: FC<FeatureItemProps> = ({title, icon, descriptoin}) => {
  return (
    <div className="flex flex-col gap-5 items-center">
      <div className="p-5 bg-gradient-to-r via-[#108440]/20 from-[#108440]/30 to-[#108440]/30 w-24 h-24 rounded-full">
        <img
          src={icon}
          alt="Phone showing raven app"
          className="shrink-0 w-[300px] object-contain"
        />
      </div>
      <SimpleText
        component="h1"
        className="text-gray-500 font-semibold leading-8  text-center  "
      >
        <Translate msgId={title} />
      </SimpleText>
      <SimpleText className="text-center text-gray-400 ">
        <Translate msgId={descriptoin} />
      </SimpleText>
    </div>
  );
};

export default FeatureItem;
