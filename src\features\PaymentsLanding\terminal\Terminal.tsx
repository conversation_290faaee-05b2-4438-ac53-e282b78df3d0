import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import TerminalImages from '/images/poss.png';

const Terminal = () => {
  return (
    <section className="mt-20 md:py-10 md:px-10">
      <div className="mb-8 space-y-5 text-center">
        <SimpleText className='text-raven-green-800 text-4xl font-bold'>
          <Translate msgId="landing.terminalTitle" />
        </SimpleText>
        <SimpleText className='text-raven-green-800 text-2xl'>
          <Translate msgId="landing.terminalSub" />
        </SimpleText>
        <SimpleText className='md:w-1/2 mx-auto text-center'>
          <Translate msgId="landing.terminalDes" />
        </SimpleText>
      </div>

      <div className="flex items-center justify-center gap-5">
        <img
          src={TerminalImages}
          alt="Phone showing raven app"
          className="shrink-0 w-1/2 object-contain"
        />
      </div>
    </section>
  );
};

export default Terminal;
