import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { notify } from 'features/Notifications/notificationsSlice';

const useNotify = () => {
    const dispatch = useDispatch();

    const notifyCallback = useCallback(
        (options = {}) => {
            /**
             * Example Payload
             * {
             * 	 severity: 'info',
             * 	 title: 'Test Notification',
             * 	 description: 'This is testing Raven notifications',
             * 	 action: {
             * 	 	 type: 'custom/action',
             * 	 	 text: 'Click me!',
             * 	 	 payload: { foo: 'bar' },
             * 	 }
             * }
             */
            dispatch(notify(options));
        },
        [dispatch]
    );

    return notifyCallback;
};

export default useNotify;
