import Footer from '@/features/footer/Footer';
import { Header } from '@/components/header';
import headset from '/images/headset.png';
import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import { Button } from '@/components/button';
import ContactForm from '@/features/Support/contactForm';
import Tags from '@/features/Support/tags';
import Accordions from '@/features/Support/accordions';
import { SearchBar } from '@/components/search';
export default function SupportPage() {
  return (
    <div>
      <Header />

      <main className="body">
        <div className="grid grid-cols-1 lg:grid-cols-2 p-5">
          {/* left */}
          <div className="flex flex-col items-center justify-center md:w-1/2 mb-8">
            <SimpleText component="h1" className="text-5xl">
              <Translate msgId="support.realPerson" />
            </SimpleText>
            <img
              src={headset}
              alt="customer care head gear"
              className="w-[200px] h-auto dark:invert"
            />
          </div>
          {/* right */}

          <ContactForm />
        </div>
        {/* faq */}
        <div className="flex justify-center flex-col items-center mt-10 md:mt-20 gap-y-8">
          <SimpleText component="h1" className="text-4xl">
            <Translate msgId="landing.faq" />
          </SimpleText>
          <SimpleText>
            <Translate msgId="support.gotQuestions" />
          </SimpleText>
          {/* search bar */}
          <div className="w-full">
            <SearchBar />
          </div>
          {/* two way buttons */}
          <section className="space-x-10">
            <Button variant="darkPrimary" className="rounded-lg">
              <SimpleText>
                <Translate msgId="home.personal" />
              </SimpleText>
            </Button>
            <Button
              variant="secondary"
              className="rounded-lg border-2 border-raven-green-800 px-10"
            >
              <SimpleText>
                <Translate msgId="home.business" />
              </SimpleText>
            </Button>
          </section>
          {/* tags */}
          <Tags />
          {/* questions */}
          <Accordions />
        </div>
      </main>
      <Footer />
    </div>
  );
}
