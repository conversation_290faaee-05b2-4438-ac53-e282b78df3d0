import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';

interface PanelItemProps {
  index: number;
  targetId: string; // New prop for target section id
}

export default function PanelItem({ index, targetId }: PanelItemProps) {
  return (
    <div className="p-2  w-auto rounded-lg hover:bg-raven-green-50 cursor-pointer">
      <a href={`#${targetId}`} className="flex gap-x-2 items-center">
        <SimpleText className="text-raven-green-800">
          <Translate msgId={`docs.panelItem${index + 1}`} />
        </SimpleText>
        {index == 7 && (
          <span className="text-sm bg-raven-green-50 p-1 rounded-lg px-2">
            beta
          </span>
        )}
      </a>
    </div>
  );
}
