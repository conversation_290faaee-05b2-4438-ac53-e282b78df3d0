import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import { Button } from '@/components/button';
interface lastSection {
  isCareerPage?: boolean;
}
export default function LastSection({ isCareerPage = true }: lastSection) {
  return (
    <div className="flex md:flex-row flex-col items-center gap-x-10 mt-28 mb-28 justify-center">
      <SimpleText className="md:text-2xl text-lg">
        <Translate msgId="careers.account" />
      </SimpleText>
      <Button variant="primary" className="rounded-lg md:mt-0 mt-2">
        <Translate
          msgId={isCareerPage ? 'landing.getStarted' : 'about.signUpNow'}
        />
      </Button>
    </div>
  );
}
