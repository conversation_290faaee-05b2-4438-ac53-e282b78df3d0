import raven from '/images/logo-header.png';
import ravenhive from '/images/ravenhive.png';
import { FC } from 'react';
import { useLocation } from 'react-router-dom';
interface LogoProps {
  className?: string;
}

const Logo: FC<LogoProps> = ({ className }) => {
  const { pathname } = useLocation();

  return (
    <img
      src={pathname.endsWith('/') ? raven : ravenhive}
      alt="raven logo"
      title="Click to go home"
      className={className || 'w-100'}
    />
  );
};

export default Logo;
