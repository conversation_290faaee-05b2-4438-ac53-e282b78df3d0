import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import LineChart from '/images/line.png';
import { ArrowCircleUp } from 'heroicons-react';

const IncomeCard = () => {
  return (
    <div className=" py-3 pl-5  w-64 ">
      <div className="flex items-center gap-3">
        <SimpleText component="h3" className="text-gray-500 text-lg">
          <Translate msgId={'Income'} />
        </SimpleText>
        <div className="text-[#728B4E] flex items-center justify-center gap-2 bg-[#E0F7C8] rounded-lg p-2 px-3">
          <ArrowCircleUp />
          <SimpleText className="font-semibold ">
            <Translate msgId={'50%'} />
          </SimpleText>
        </div>
      </div>
      <div className="flex items-center justify-between m-0">
        <SimpleText component="h3" className="p-0 m-0 font-semibold text-xl">
          <Translate msgId={'$5,600'} />
        </SimpleText>
        <img
            src={LineChart}
            alt="Phone showing raven app"
            className="shrink-0 object-contain h-20"
          />
      </div>
    </div>
  );
};

export default IncomeCard;
