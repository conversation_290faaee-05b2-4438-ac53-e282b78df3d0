import { FC } from 'react';
import { Skeleton } from 'components/skeleton';
import { TableRow, TableColumn } from 'components/Table';
import { TextSecondary } from 'components/typography/Text';

interface TransactionHistoryItemProps {
  date?: string;
  type?: string;
  status?: string;
  amount?: number;
  loading?: boolean;
}

const TransactionHistoryItem: FC<TransactionHistoryItemProps> = ({
  date,
  type,
  status,
  amount,
  loading = false,
}) => (
  <TableRow nowrap>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="dark">
          {loading ? 'Date' : date}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="light" transform="capitalize">
          {loading ? 'Type' : type}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="light" transform="capitalize">
          {loading ? 'Status' : status}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="dark">
          {loading ? 'Amount' : amount}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
  </TableRow>
);

export default TransactionHistoryItem;
