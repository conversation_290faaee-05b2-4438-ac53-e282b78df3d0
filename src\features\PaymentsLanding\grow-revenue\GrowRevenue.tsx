import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import EmpowerImage from '/images/all-cards.png';
import { Trans } from 'react-i18next';

const GrowRevenue = () => {
  return (
    <section className="mt-20 md:py-10 md:px-10">
      <div className="mb-8 space-y-5 text-center">
        <SimpleText className='text-raven-green-800 text-4xl font-bold'>
            <Trans
                i18nKey="landing.revenueTitle"
                components={{
                  span: (
                    <span className="text-gray-500 leading-tight" />
                  ),
                }}
              />
        </SimpleText>
        <SimpleText className='text-raven-green-800 text-2xl'>
          <Translate msgId="landing.revenueSub" />
        </SimpleText>
        <SimpleText className=' mx-auto md:w-1/2 text-center'>
          <Translate msgId="landing.revenueDes" />
        </SimpleText>
      </div>

      <div className="flex items-center justify-center gap-5">
        <img
          src={EmpowerImage}
          alt="Phone showing raven app"
          className="shrink-0 w-1/2 object-contain"
        />
      </div>
    </section>
  );
};

export default GrowRevenue;
