import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import { Trans } from 'react-i18next';
import { Button } from '@/components/button';
import spring from '/images/enterprise-arrow.png';

const Hero = () => {
  return (
    <section>
      <div className="mt-10 grid grid-cols-1 lg:grid-cols-2 gap-3 md:gap-10 ">
        <div>
          <div className="space-y-5">
            <SimpleText className="font-semibold text-raven-green-800">
              <Translate msgId="Enterprise" />
            </SimpleText>
            <SimpleText
              component="h1"
              className="md:text-9xl text-5xl font-extrabold leading-tight"
            >
              <Trans
                i18nKey="GLOBAL BUSINESS PAYMENTS"
                components={{
                  style1: (
                    <span className="text-raven-green-800 leading-tight" />
                  ),
                  style2: (
                    <span className="text-raven-green-800 leading-tight" />
                  )
                }}
              />
            </SimpleText>
          </div>
        </div>

        <div className=" flex justify-center lg:items-center flex-col">
          <div className="space-y-6">
            <SimpleText className="text-5xl md:text-6xl font-bold text-gray-500">
              <Translate msgId="One API" />
            </SimpleText>
            <div className="flex gap-5">
              <div className="w-full md:w-4/6">
                <div className="mt-7 lg:mt-10">
                  <SimpleText className=" text-raven-green-800 font-bold">
                    <Translate msgId="African Markets" />
                  </SimpleText>
                  <SimpleText className=" text-raven-green-800">
                    <Translate msgId="Local Payment Methods" />
                  </SimpleText>
                </div>
                <div className="mt-7 lg:mt-10">
                  <Button variant="darkPrimary" className="rounded md:w-full">
                    Get in touch
                  </Button>
                </div>
              </div>

              <img src={spring} alt="spring arrow" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
