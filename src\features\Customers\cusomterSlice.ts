import { CustomerTypes, Vendor, Client } from "@/models/customer-types";
import { RootState } from "@/store/store";
import { createSelector, createSlice } from "@reduxjs/toolkit";

interface InitialState {
    customerType: CustomerTypes;
    customer: Vendor | Client | null,
    vendors: Record<string, unknown>[]
    clients: Record<string, unknown>[]
}

const initialState: InitialState = {
    customerType: 'vendor',
    customer: null,
    vendors: [],
    clients: []
}

export const customerSlice = createSlice({
    name: 'customer',
    initialState,
    reducers: {
        setCustomerType: (state, action: { type: string, payload: CustomerTypes }) => {
            state.customerType = action.payload
        },
        setCustomer: (state, action: { type: string, payload: Vendor | Client | null }) => {
            state.customer = action.payload
        },
        setVendors: (state, action) => {
            state.vendors = action.payload
        },
        setClients: (state, action) => {
            state.clients = action.payload
        }
    },
})

const { name, actions } = customerSlice

export const { setCustomerType, setVendors, setClients, setCustomer } = actions

const getSlice = (state: RootState) => state[name]

export const getCustomerType = createSelector(getSlice, (slice) => slice?.customerType || 'vendor')

export const getVendors = createSelector(getSlice, (slice) => slice?.vendors || [])

export const getClients = createSelector(getSlice, (slice) => slice?.clients || [])

export const getCustomer = createSelector(getSlice, (slice) => slice?.customer || null)