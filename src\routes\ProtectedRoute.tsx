import { FC, ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { getToken, getCookie } from 'features/utils/cookie';
interface ProtectedRouteProps {
  redirectPath?: string;
  rule?: string;
  children: ReactNode;
}

/**
 * A protected route component that restricts access to its children based on the availability of an access token.
 *
 * This component is intended to be used as an element prop in RRD Route component. It checks if an access token is present and
 * redirects to a specified path if not. If the access token is present, the component renders its children.
 *
 * @param {Object} props - The properties object.
 * @param {string} props.redirectPath - The path to redirect when there's no access token.
 * @param {string} props.rule - The access cookie item to be used.
 * @param {ReactNode} props.children - The children components to render if the route is protected.
 *
 * @example
 * // Using the ProtectedRoute component
 * <ProtectedRoute redirectPath="/signin" rule="otp">
 *   <Dashboard />
 * </ProtectedRoute>
 */
const ProtectedRoute: FC<ProtectedRouteProps> = ({
  redirectPath = '/login',
  rule,
  children,
}) => {
  const accessCookie = rule ? getCookie(rule) : getToken();

  // Check if there's an access token, if not, redirect to the specified path.
  if (!accessCookie) {
    return <Navigate to={redirectPath} replace />;
  }

  // Render children if the access token is present.
  return <>{children}</>;
};

export default ProtectedRoute;
