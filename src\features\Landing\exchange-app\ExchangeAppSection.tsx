import { Translate } from 'components/translate';
import { ExchangeApp } from 'components/exchange-app';
import { SimpleText } from 'components/typography';
import spring from '/images/spring-arrow.png';

const ExchangeAppSection = () => {
  return (
    <section className="mt-20 md:py-20 md:px-10">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        <div className="space-y-3 w-full dark:text-white text-gray-800 flex items-center justify-center md:pr-16 mb-10 md:mb-0">
          <div className="relative">
            <img
              src={spring}
              alt="spring arrow"
              className="absolute sm:right-[-30px] top-[-80px]"

            />

            <SimpleText
              component="h2"
              className="md:text-6xl text-3xl font-semibold leading-normal"
            >
              <Translate msgId="home.exchangeRate" />
            </SimpleText>
            <SimpleText>
              <Translate msgId="home.exchangeRateDescription" />
            </SimpleText>
          </div>
        </div>
        <div className="w-full bg-green-200 dark:bg-green-900 p-5 md:p-12 rounded-3xl">
          <ExchangeApp />
        </div>
      </div>
    </section>
  );
};

export default ExchangeAppSection;
