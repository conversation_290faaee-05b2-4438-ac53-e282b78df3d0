import PropTypes from 'prop-types';
import { XOutline } from 'heroicons-react';
import clsx from '@/features/utils/clsx';

const NotificationDismiss = ({ className = '', onClick = () => {} }) => {
  const rootClass = clsx(className);

  return (
    <div className="ml-auto pl-3">
      <div className="-mx-1.5 -my-1.5">
        <button onClick={onClick} className={rootClass} aria-label="Dismiss">
          <XOutline className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

NotificationDismiss.propTypes = {
  onClick: PropTypes.func,
};

export default NotificationDismiss;
