import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import BusinessHero from '/images/business-hero.png';
import { Trans } from 'react-i18next';
import { Button } from '@/components/button';
import { ArrowRight } from 'heroicons-react';
import IncomeCard from '../components/IncomeCard';

const Hero = () => {
  return (
    <section>
      <div className="mt-10 grid grid-cols-1 lg:grid-cols-2 gap-3 ">
        <div>
          <div>
            <Button
              variant="light"
              className="mb-10 rounded !text-raven-green-800 flex  items-center gap-4"
              type="button"
            >
              <div className="bg-red-600 h-3 w-3 rounded-full" />
              <Translate msgId="business.weAreHiring" />
              <ArrowRight />
            </Button>
            <SimpleText
              component="h1"
              className="md:text-6xl text-5xl font-extrabold leading-tight"
            >
              <Trans
                i18nKey="business.heroTitle"
                components={{
                  style1: (
                    <span className="text-raven-green-800 leading-tight" />
                  ),
                  style2: (
                    <span className="text-raven-green-800 leading-tight" />
                  ),
                }}
              />
            </SimpleText>
          </div>
          <div className="mt-6">
            <SimpleText component="h2" className="text-lg font-light">
              <Translate msgId="business.heroDesc" />
            </SimpleText>
          </div>
          <div>
            <div className="mt-6 flex-col md:flex-row gap-3 flex md:items-center">
              <Button
                variant="darkPrimary"
                className="mr-5 rounded"
                type="button"
              >
                <Translate msgId="business.getStarted" />
              </Button>
              <Button
                variant="light"
                className="mr-5 rounded !text-raven-green-800"
                type="button"
              >
                <Translate msgId="business.talkToUs" />
              </Button>
            </div>
          </div>
        </div>
        <div className=" relative flex items-center justify-start lg:justify-end">
          <img
            src={BusinessHero}
            alt="Phone showing raven app"
            className="shrink-0 object-contain"
          />
          <div className=" border absolute -bottom-10 rounded-xl bg-white left-20 z-50">
            <IncomeCard />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
