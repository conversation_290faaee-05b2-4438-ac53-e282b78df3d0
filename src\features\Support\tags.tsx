import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';

export default function Tags() {
  return (
    <div className="flex items-center justify-center flex-wrap w-full gap-10">
      <section className="flex gap-x-2">
        <button className="rounded-lg focus:bg-raven-green-800 p-2 border-raven-green-800 border-2">
          <SimpleText>
            <Translate msgId="support.cards" />
          </SimpleText>
        </button>
        <button className="rounded-lg focus:bg-raven-green-800 p-2 border-raven-green-800 border-2">
          <SimpleText>
            <Translate msgId="support.ravenapp" />
          </SimpleText>
        </button>
      </section>
      <section className="flex gap-x-2">
        <button className="rounded-lg focus:bg-raven-green-800 p-2 border-raven-green-800 border-2">
          <SimpleText>
            <Translate msgId="support.solutions" />
          </SimpleText>
        </button>
        <button className="rounded-lg focus:bg-raven-green-800 p-2 border-raven-green-800 border-2">
          <SimpleText>
            <Translate msgId="support.smes" />
          </SimpleText>
        </button>
        <button className="rounded-lg focus:bg-raven-green-800 p-2 border-raven-green-800 border-2">
          <SimpleText>
            <Translate msgId="support.enterprise" />
          </SimpleText>
        </button>
      </section>
      <section className="flex gap-x-2">
        <button className="rounded-lg focus:bg-raven-green-800 p-2 border-raven-green-800 border-2">
          <SimpleText>
            <Translate msgId="support.security" />
          </SimpleText>
        </button>
        <button className="rounded-lg focus:bg-raven-green-800 p-2 border-raven-green-800 border-2">
          <SimpleText>
            <Translate msgId="support.payment" />
          </SimpleText>
        </button>
      </section>
    </div>
  );
}
