import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"

const GetPaidInfo = () => {
  return (
    <div className=" space-y-5">
      <div className=' flex items-center justify-center flex-col'>
        <img src='/images/get-paid.png' alt='get paid' className=' inline-block mb-3 w-10 h-10' />
        <SimpleText component='h1' className="text-lg text-center leading-6 font-medium text-gray-900">
          <Translate msgId='dashboard.getReady' />
        </SimpleText>
        <SimpleText component='h1' className="mt-1 text-sm text-center text-gray-500">
          <Translate msgId='dashboard.someSteps' />
        </SimpleText>
      </div>

      <div className=' space-y-5 p-1 border border-raven-green-500/20 bg-raven-green-50'>
        <div className=" py-2 px-3 gap-5 flex items-center rounded-md">
          <img src='/images/check.png' alt='get paid' className=' inline-block mb-3 w-10 h-10' />
          <div>
            <SimpleText component="h1" className=" text-base">
              <Translate msgId='dashboard.addInvoice' />
            </SimpleText>
            <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
              <Translate msgId='dashboard.syncUpload' />
            </SimpleText>
          </div>
        </div>
        <div className=" py-2 px-3 gap-5 flex items-center rounded-md">
          <img src='/images/check.png' alt='get paid' className=' inline-block mb-3 w-10 h-10' />
          <div>
            <SimpleText component="h1" className=" text-base">
              <Translate msgId='dashboard.sendPaymentRequest' />
            </SimpleText>
            <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
              <Translate msgId='dashboard.selectInvoice' />
            </SimpleText>
          </div>
        </div>
        <div className=" py-2 px-3 gap-5 flex items-center rounded-md">
          <img src='/images/check.png' alt='get paid' className=' inline-block mb-3 w-10 h-10' />
          <div>
            <SimpleText component="h1" className=" text-base">
              <Translate msgId='dashboard.reconcilePayment' />
            </SimpleText>
            <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
              <Translate msgId='dashboard.syncBack' />
            </SimpleText>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GetPaidInfo