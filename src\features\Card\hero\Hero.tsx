import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import { Button } from '@/components/button';
import BusinessHero from '/images/card-hero.png';
import CircleCheck from '/images/circle-check.png';
import { ChatAlt } from 'heroicons-react';

const Hero = () => {
  return (
    <section className="bg-gradient-to-br p-5 from-[#ECEFF2] to-[#108440]/10">
      <div className="pt-14 lg:pt-28 flex flex-col items-center justify-center container mx-auto space-y-5 ">
        <SimpleText
          component="h1"
          className="text-gray-400 leading-8  text-center  font-extrabold "
        >
          <Translate msgId="card.ravenCards" />
        </SimpleText>
        <SimpleText className="lg:text-6xl lg:w-4/5 text-center text-raven-green-800 text-4xl md:text-5xl font-extrabold ">
          <Translate msgId="card.heroTitle" />
        </SimpleText>
        <Button variant="darkPrimary" className="my-10 rounded" type="button">
          <Translate msgId="card.getCard" />
        </Button>

        <div className="relative px-5 md:px-12  rounded-3xl">
          <img
            src={BusinessHero}
            alt="Phone showing raven app"
            className="shrink-0 w-[600px] object-contain"
          />

          <div className=" mt-20 border w-64 md:w-auto absolute -top-10 md:top-8 md:-right-8 -right-5 l lg:-right-40 rounded-xl bg-white lg:top-36 z-50">
            <div className=" p-4 flex gap-3 items-center">
              <img
                src={CircleCheck}
                alt="Phone showing raven app"
                className="shrink-0 object-contain w-14"
              />
              <div className="">
                <SimpleText className="text-xs md:text-lg ">
                  <Translate msgId={'card.paymentDone'} />
                </SimpleText>
                <SimpleText className="text-xs md:text-base text-gray-300 ">
                  <Translate msgId={'card.transactWithRaven'} />
                </SimpleText>
              </div>
              <div className="h-9 w-9 p-1 rounded-full flex items-center justify-center bg-gradient-to-r via-[#108440]/20 from-[#108440]/30 to-[#108440]/30">
                <ChatAlt className=" text-[#108440]/50" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
