import { authApi } from '@/services/endpoints/auth';
import { SignupFormData } from '../schemas/signupSchema';
import { useNavigate } from 'react-router-dom';
import useNotify from '@/features/Notifications/hooks/useNotify';
import { setCookie } from '@/features/utils/cookie';
import { RAVEN_OTP_COOKIE_ID } from '@/lib/constants';
import { SignUpRequestDto, SignupResponseDto } from '@/models/signup.types';
import { ApiErrorResponse } from '@/models/common.types';

export const useSignup = () => {
  const navigate = useNavigate();
  const notify = useNotify();
  const [signup, { isLoading: signupLoading }] = authApi.useSignupMutation();

  const handleSignupSuccess = (response: SignupResponseDto) => {
    notify({
      severity: 'default',
      title: 'Success',
      description: response.message || 'Account created successfully'
    });
    const token = response.data.access_token;

    // set otp cookie since verify-otp route is guarded
    setCookie(RAVEN_OTP_COOKIE_ID, token);
    navigate('/verify-otp');
  };

  const handleSignupError = (err: ApiErrorResponse) => {
    const message = err.data.message
    notify({
      severity: 'error',
      title: 'Error',
      description: message || 'Error creating account'
    });
  };

  const handleSignup = async (data: SignupFormData) => {
    const signupPayload: SignUpRequestDto = {
      email: data.email,
      username: data.firstName + data.lastName,
      password: data.password,
      role: 'individual',
      first_name: data.firstName,
      last_name: data.lastName,
      phone_number: data.phoneNumber,
      country: data.country,
      accept_terms: true,
      marketing_emails: false
    };

    try {
      const response = await signup(signupPayload).unwrap();
      handleSignupSuccess(response);
    } catch (error: any) {
      const err = error as ApiErrorResponse;
      handleSignupError(err);
    }
  };

  return { handleSignup, signupLoading };
};
