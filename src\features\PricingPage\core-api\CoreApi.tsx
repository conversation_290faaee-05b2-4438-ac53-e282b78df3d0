import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"

const CoreApi = () => {
  return (
    <div className='py-16'>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
        <div className="flex items-center justify-center">
          <img src="/images/api-widget.png" className="px-0 max-w-sm" />
        </div>

        <div>
          <div className="p-5 lg:p-10 space-y-5 lg:text-right">
            <SimpleText className="text-raven-green-800">
              <Translate msgId="landing.coreApi" />
            </SimpleText>
            <SimpleText className="font-bold text-4xl leading-tight">
              <Translate msgId="landing.coreApiTitle" />
            </SimpleText>
            <SimpleText className="lg:text-right">
              <Translate msgId="landing.coreApiDes" />
            </SimpleText>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CoreApi