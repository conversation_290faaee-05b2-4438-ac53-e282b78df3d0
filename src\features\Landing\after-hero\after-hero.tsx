import Card from './card';
import image2 from './images/cardmachine.jpg';
import image3 from './images/smilngblack.jpg';
import image4 from './images/smiling.jpg';
import ghana from '/flags/ghana.png';
import gb from '/flags/GB.png';
import nigeria from '/flags/nigeria.png';
import sa from '/flags/south-africa.png';

const cardData = [
  {
    image: image2,
    flag: nigeria,
    utilityBill: 'Utility Bills',
    amount: 'NGN 1000'
  },
  {
    image: image2,
    flag: sa,
    utilityBill: 'Utility Bills',
    amount: 'ZAR 500'
  },
  {
    image: image3,
    flag: ghana,
    nameAction: 'Kwame | Received'
  },
  {
    image: image4,
    flag: gb,
    nameAction: 'Cleo | Sent'
  }
];

const AfterHero = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:flex lg:overflow-x-scroll lg:whitespace-nowrap gap-4 w-full mt-[100px] px-2 hide-scrollbar">
      {cardData.map((card, index) => (
        <Card card={card} index={index} key={index} />
      ))}
    </div>
  );
};

export default AfterHero;
