import { Translate } from "@/components/translate"

const PayoutTable = () => {
  return (
    <div className="border border-gray-200 rounded-md rounded-br-2xl rounded-bl-2xl overflow-hidden">
      <table className="min-w-full text-sm text-gray-400 bg-white">
        <thead className="">
          <tr className="w-full px-2 bg-raven-green-800 border-b text-white">
            <th className="py-2 px-4 text-left">
              <Translate msgId="landing.date" />
            </th>
            <th className="py-2 px-4 text-left">
              <Translate msgId="landing.payoutMethod" />
            </th>
            <th className="py-2 px-4 text-left">
              <Translate msgId="landing.status" />
            </th>
            <th className="py-2 px-4 text-left">
              <Translate msgId="landing.amount" />
            </th>
          </tr>
        </thead>
        <tbody>
          <tr className="">
            <td className="py-5 px-4">14 May 2024</td>
            <td className="py-5 px-4">*****4532</td>
            <td className="py-5 px-4 text-red-500">pending</td>
            <td className="py-5 px-4">$134.25</td>
          </tr>
          <tr className="">
            <td className="py-5 px-4">14 May 2024</td>
            <td className="py-5 px-4">*****4532</td>
            <td className="py-5 px-4 text-green-500">paid</td>
            <td className="py-5 px-4">$134.25</td>
          </tr>
          <tr className="">
            <td className="py-5 px-4">14 May 2024</td>
            <td className="py-5 px-4">*****4532</td>
            <td className="py-5 px-4 text-green-500">paid</td>
            <td className="py-5 px-4">$134.25</td>
          </tr>
          <tr className="">
            <td className="py-5 px-4">14 May 2024</td>
            <td className="py-5 px-4">*****4532</td>
            <td className="py-5 px-4 text-red-500">pending</td>
            <td className="py-5 px-4">$134.25</td>
          </tr>
        </tbody>
      </table>
    </div>
  )
}

export default PayoutTable