import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { FormLabel } from '@/components/form';
import {  SelectNetworkOption } from '@/data/types';
import MomoNetworkSelector from '@/components/selector/MomoNetworkSelector';
import { NETWORKS } from '@/data/networks';
import { getNetwork, setNetwork } from '../../paymentsSlice';

type FormValues = {
  phone: string;
  fullName: string;
  amount: number;
};

const MomoDepositForm = () => {
  const dispatch = useDispatch();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>();

  const [openNetworkOptions, setOpenNetworkOptions] = useState(false);

  const handlePayment: SubmitHandler<FormValues> = (data) => {
    // Handle Payment
    console.log(data);
  };

  const handleNetworkSelector = (event: any) => dispatch(setNetwork(event));
  
  const network = useSelector(getNetwork);

  return (
    <div>
      <form onSubmit={handleSubmit(handlePayment)}>
        <div className="space-y-5 cursor-pointer  ">
          <div className=" w-full ">
            <FormLabel
              name="selectNetwork"
              text="dashboard.selectNetwork"
              required
            />
            <MomoNetworkSelector
              id="momo"
              onToggle={() => setOpenNetworkOptions(!openNetworkOptions)}
              open={openNetworkOptions}
              onChange={handleNetworkSelector}
              selectedValue={
                NETWORKS.find(
                  (option) => option.value === network
                ) as SelectNetworkOption
              }
            />
          </div>
          <div className=" w-full ">
            <FormLabel
              name="phoneNumber"
              text="dashboard.phoneNumber"
              required
            />
            <input
              className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="text"
              placeholder="0540500000"
              {...register('phone', { required: true })}
            />
            {errors.phone && (
              <SimpleText className=" text-xs text-red-500">
                <Translate msgId="dashboard.phoneNumberRequired" />
              </SimpleText>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default MomoDepositForm;
