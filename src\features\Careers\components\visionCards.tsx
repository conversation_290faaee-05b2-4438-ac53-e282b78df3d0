import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
export default function VisionCards() {
  return (
    <div className="relative mt-32">
      <div className="absolute left-1/2 w-[2px] bg-gray-200 h-full hidden md:block"></div>
      <div className="absolute top-1/2 w-full bg-gray-200 h-[2px] hidden md:block"></div>
      <div className=" flex w-full flex-col md:flex-row gap-y-6 md:gap-0 mb-6 md:mb-0">
        {Array.from({ length: 2 }).map((_, index) => {
          return (
            <section
              className={`w-full md:w-1/2 p-2 md:p-10 flex md:flex-col flex-col-reverse ${
                index == 0 ? 'md:items-end' : 'items-start'
              }`}
            >
              <SimpleText className="w-full md:w-2/3 ">
                <Translate msgId={`careers.des${index + 1}`} />
              </SimpleText>
              <SimpleText className="text-3xl text-raven-green-800 justify-self-end">
                <Translate msgId={`careers.title${index + 1}`} />
              </SimpleText>
            </section>
          );
        })}
      </div>

      <div className=" flex w-full flex-col md:flex-row gap-y-6 md:gap-0  mb-6 md:mb-0">
        {Array.from({ length: 2 }).map((_, index) => {
          return (
            <section className="w-full md:w-1/2 p-2 md:p-10 ">
              <SimpleText
                className={`text-3xl text-raven-green-800 ${
                  index == 0 ? 'justify-self-end' : 'justify-self-start'
                }`}
              >
                <Translate msgId="careers.title3" />
              </SimpleText>
              <SimpleText
                className={`w-full md:w-2/3 ${
                  index == 0 ? 'justify-self-end' : 'justify-self-start'
                }`}
              >
                <Translate
                  msgId={`careers.${index == 0 ? 'des1' : 'changing'}`}
                />
              </SimpleText>
            </section>
          );
        })}
      </div>
    </div>
  );
}
