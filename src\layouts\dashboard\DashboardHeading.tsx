import { FC, ReactNode } from 'react';
import { Text } from 'components/typography';
import { Heading } from 'components/typography';
import clsx from 'features/utils/clsx';

interface DashboardHeadingProps {
  className?: string;
  subheading?: boolean;
  children: ReactNode;
}

const DashboardHeading: FC<DashboardHeadingProps> = ({
  subheading,
  className,
  children,
}) => (
  <div
    className={clsx(
      'select-none min-w-0 flex items-center space-x-2',
      className
    )}
  >
    {subheading ? (
      <Text shade="light" size="sm">
        {children}
      </Text>
    ) : (
      <Heading variant="h2">{children}</Heading>
    )}
  </div>
);

export default DashboardHeading;
