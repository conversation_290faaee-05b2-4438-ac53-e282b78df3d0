import { useEffect, useState } from 'react';
import { getSessionStorageItem } from '@/features/utils/sessionStorage';
import { RAVEN_TOKEN_ID } from '@/lib/constants';

const useAuthRedirect = () => {
  const [isAuthStatusLoading, setIsAuthStatusLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const token = getSessionStorageItem(RAVEN_TOKEN_ID);

    if (!token) {
      const redirectUrl = `/signin?redirect=${window.location.pathname ?? ''}`;
      window.location.replace(redirectUrl);
      return;
    }

    setIsAuthenticated(true);
    setIsAuthStatusLoading(false);
  }, []);

  return { isAuthStatusLoading, isAuthenticated };
};

export default useAuthRedirect;
