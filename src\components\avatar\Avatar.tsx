import { Skeleton } from 'components/skeleton';
import { User } from 'heroicons-react';
import { ElementType, FC, ReactNode } from 'react';

type AvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
type AvatarRadius = 'none' | 'sm' | 'md' | 'lg' | 'full';

interface AvatarProps {
  loading?: boolean;
  children?: ReactNode;
  className?: string;
  alt?: string;
  src: string | null;
  size: AvatarSize;
  radius?: AvatarRadius;
  component?: ElementType;
}

const Avatar: FC<AvatarProps> & {
  size: Record<AvatarSize, string>;
  radius?: Record<AvatarRadius, string>;
} = ({
  component: Component = 'div',
  src,
  alt = 'User',
  size = 'sm',
  radius,
  loading = false,
  children,
  className,
  ...rest
}) => (
  <Component {...rest} className="relative inline-flex">
    <div
      className={`overflow-hidden flex items-center justify-center ${
        Avatar.size[size] || ''
      } ${radius ? Avatar.radius![radius] : ''} ${!radius ? 'rounded' : ''} ${
        !src ? 'text-gray-300 bg-gray-100' : ''
      } ${className || ''}`}
    >
      <Skeleton active={loading}>
        {src ? (
          <img src={src} className="w-full h-full" alt={alt} />
        ) : (
          <User className="'w-7/12 h-7/12'" />
        )}
      </Skeleton>
    </div>
    {children}
  </Component>
);

Avatar.size = {
  xs: 'w-6 h-6',
  sm: 'w-8 h-8',
  md: 'w-10 h-10',
  lg: 'w-12 h-12',
  xl: 'w-14 h-14',
  full: 'w-full h-full',
};

Avatar.radius = {
  none: '',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  full: 'rounded-full',
};

export default Avatar;
