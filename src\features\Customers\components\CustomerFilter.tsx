import { useDispatch } from 'react-redux';
import { setCustomerType } from '../cusomterSlice';
import { CustomerTypes } from '@/models/customer-types';
import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import clsx from '@/features/utils/clsx';
import { Link } from 'react-router-dom';
import useMatchPath from '@/hooks/useMatchPath';
import AddCustomer from './AddCustomer';

const CustomerFilter = () => {
  const dispatch = useDispatch();

  const changeCustomerType = (value: CustomerTypes) => {
    dispatch(setCustomerType(value));
  };

  const vendorActive = useMatchPath('/dashboard/customers/vendors');
  const clientActive = useMatchPath('/dashboard/customers/clients');

  return (
    <div className="flex flex-wrap md:flex-nowrap items-center gap-5 justify-between w-full">
      <div className="inline-flex border-b pb-3 gap-5 flex-wrap md:flex-nowrap">
        <Button
          onClick={() => changeCustomerType('vendor')}
          variant="transparent"
          component={Link}
          to="/dashboard/customers/vendors"
          className={clsx({
            'text-gray-300': clientActive,
            'text-gray-700 underline underline-offset-[17px]': vendorActive,
          })}
        >
          <Translate msgId="dashboard.vendors" />
        </Button>
        <Button
          onClick={() => changeCustomerType('client')}
          variant="transparent"
          component={Link}
          to="/dashboard/customers/clients"
          className={clsx({
            'text-gray-300': vendorActive,
            'text-gray-700 underline underline-offset-[17px]': clientActive,
          })}
        >
          <Translate msgId="dashboard.clients" />
        </Button>
      </div>
      <AddCustomer />
    </div>
  );
};

export default CustomerFilter;
