import { Translate } from '../translate';
import { SimpleText } from '../typography';
import mastercardLogo from '/icons/mastercard-logo.png';

const RavenCardTwo = () => {
  return (
    <div className="bg-slate-900 text-white justify-between flex flex-col overflow-hidden relative rounded-lg h-96">
      <div className="flex justify-between items-center relative z-5 p-5">
        <div className="icon">raven</div>
        <div className="icon">
          <img src={mastercardLogo} alt="mastercard logo" className="w-12" />
        </div>
      </div>

      <div className="absolute -right-32 -top-10 h- h-60 w-60 rounded-full bg-[#35353580]"></div>

      <div>
        <div className="relative z-5 p-5">
          <div>
            <SimpleText component="small" className="text-[0.6rem]">
              <Translate msgId="dashboard.cardNumber" />
            </SimpleText>
          </div>
          <div>
            <SimpleText component="small">**** **** **** 3554</SimpleText>
          </div>
        </div>

        <div className="bg-black py-3 px-5">
          <div className="flex items-center relative z-5 justify-between">
            <div>
              <div>
                <SimpleText component="small" className="text-[0.6rem]">
                  <Translate msgId="dashboard.cardHolder" />
                </SimpleText>
              </div>
              <div>
                <SimpleText component="small">JOSEPH NARTEY</SimpleText>
              </div>
            </div>
            <div>
              <div>
                <SimpleText component="small" className="text-[0.6rem]">
                  <Translate msgId="dashboard.expiresOn" />
                </SimpleText>
              </div>
              <div>
                <SimpleText component="small">24/06</SimpleText>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RavenCardTwo;
