import { ElementType, FC } from 'react';
import { Avatar } from 'components/avatar';
import { SimpleText } from 'components/typography';
import { Skeleton } from 'components/skeleton';

interface UserHeadingProps {
  component?: ElementType;
  condensed?: boolean;
  className?: string;
  subtitle: string;
  loading?: boolean;
  avatar: string | null;
  title: string;
}

const UserHeading: FC<UserHeadingProps> = ({
  component: Component = 'div',
  condensed,
  className,
  subtitle = 'Subtitle',
  loading = false,
  avatar,
  title = 'Title',
}) => (
  <Component className={`flex items-center ${className}`}>
    <Avatar loading={loading} size={condensed ? 'lg' : 'xl'} src={avatar} />
    <div className="ml-4 min-w-0">
      <Skeleton active={loading}>
        <SimpleText truncate>{title}</SimpleText>
      </Skeleton>
      <Skeleton active={loading}>
        <SimpleText truncate>{subtitle}</SimpleText>
      </Skeleton>
    </div>
  </Component>
);

export default UserHeading;
