import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import birielPhoto from '/images/biriel.png';
import davidPhoto from '/images/david.png';
import facebook from '/icons/facebook.png';
import twitter from '/icons/twitter.png';
import linkedin from '/icons/linkedin.png';
export default function Team() {
  return (
    <div className="mt-24">
      <SimpleText className="text-raven-green-800">
        <Translate msgId="about.team" />
      </SimpleText>
      <SimpleText className="text-3xl mt-5">
        <Translate msgId="about.meet" />
      </SimpleText>
      <div className=" w-full flex lex-row justify-center md:mt-20 mt-28">
        <div className="flex md:flex-row flex-col gap-20 gap-y-40  md:p-10  ">
          {Array.from({ length: 2 }).map((_, index) => (
            <div className="border-2 md:p-10 p-5">
              <div className="flex items-end gap-5 -mt-20 ">
                <img
                  src={index == 0 ? birielPhoto : davidPhoto}
                  alt={
                    index == 0 ? 'Biriel Nii Armah Tagoe' : 'David Nii Quartey'
                  }
                  title={
                    index == 0 ? 'Biriel Nii Armah Tagoe' : 'David Nii Quartey'
                  }
                  className="rounded-3xl h-36 w-36 object-cover"
                />
                <div>
                  <SimpleText>
                    {index == 0
                      ? 'Biriel Nii Armah Tagoe'
                      : 'David Nii Quartey'}
                  </SimpleText>
                  <SimpleText>
                    <Translate msgId={`about.teamRole${index + 1}`} />
                  </SimpleText>
                </div>
              </div>
              <div className="mt-5">
                <SimpleText>
                  <Translate msgId={`about.bio${index + 1}`} />
                </SimpleText>
              </div>
              <div className="flex gap-1 items-center mt-2">
                <a
                  href="http://ravenhive.co"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img src={facebook} />
                </a>
                <a
                  href="http://ravenhive.co"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img src={twitter} />
                </a>
                <a
                  href="http://ravenhive.co"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img src={linkedin} />
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
