import { SignUpRequestDto, SignupResponseDto } from '@/models/signup.types';
import rtkQueryBase from '../api/rtk-query-base';

export const authApi = rtkQueryBase.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    signup: builder.mutation<SignupResponseDto, SignUpRequestDto>({
      query: (queryArg) => ({
        url: `/auth/register`,
        method: 'POST',
        body: queryArg
      }),
    }), 
    login: builder.mutation<any, any>({
      query: (queryArg) => ({
        url: `/auth/login`,
        method: 'POST',
        body: queryArg
      }),
    }), 
    refreshToken: builder.mutation<any, any>({

      query: (queryArg) => ({
        url: `/auth/refresh`,
        method: 'POST',
        body: queryArg
      }),
    }), 
    resetPassword: builder.mutation<any, any>({
      query: (queryArg) => ({
        url: `/auth/reset-password`,
        method: 'POST',
        body: queryArg
      }),
    }), 
    confirmResetPassword: builder.mutation<any, any>({
      query: (queryArg) => ({
        url: `/auth/reset-password/confirm`,
        method: 'POST',
        body: queryArg
      }),
    }), 
    logout: builder.mutation<any, any>({
      query: (queryArg) => ({
        url: `/auth/logout`,
        method: 'POST',
        body: queryArg
      }),
    }), 
  })
});
