import { FC, useState } from 'react';
import Table, {
  TableHead,
  TableBody,
  TableRow,
  TableColumn,
} from 'components/Table';
import { Card, CardPagination } from 'components/Card';
import UserItem from './UserItem';
import { User } from '@/models/users';
import { Translate } from '@/components/translate';

interface UsersTableProps {
  loading: boolean;
   users: User[];
   total: number;
   limit?: number
}

const UsersTable: FC<UsersTableProps> = ({ users, loading, total, limit = 10 }) => {
  const [page, setPage] = useState(1);

  return (
    <Card variant="outlined">
      <Table
        disableStyle
        disableBorderBottom
        className="lg:rounded-md lg:overflow-hidden"
      >
        <TableHead>
          <TableRow>
            <TableColumn heading>
              <div className='p-1 flex gap-1'>
                <Translate msgId='dashboard.user' />
              </div>
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.email' />
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.status' />
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.access' />
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.joined' />
            </TableColumn>
          </TableRow>
        </TableHead>
        {loading ? (
          <TableBody>
            <UserItem loading />
            <UserItem loading />
            <UserItem loading />
            <UserItem loading />
            <UserItem loading />
            <UserItem loading />
            <UserItem loading />
            <UserItem loading />
            <UserItem loading />
          </TableBody>
        ) : (
          <TableBody>
            {users.map((result: User) => (
              <UserItem key={result.id} {...result} />
            ))}
          </TableBody>
        )}
      </Table>
      <CardPagination
        total={total}
        limit={limit}
        page={page}
        setPage={setPage}
      />
    </Card>
  );
};

export default UsersTable;
