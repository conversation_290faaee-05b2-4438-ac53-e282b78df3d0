import { Header } from '@/components/header';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import manOnCard from '/images/man_on_card.png';
import cardDeclined from '/images/card_declined.png';
import SecurityShield from '/images/security_shield.png';
import Footer from '@/features/footer/Footer';
import { Trans } from 'react-i18next';
import { Button } from '@/components/button';
export default function Resources() {
  return (
    <div>
      <Header />
      <div className="body">
        <SimpleText className="text-raven-green-800 md:text-3xl text-2xl my-5 md:mt-36 md:mb-10">
          <Translate msgId="resources.resources" />
        </SimpleText>

        {Array.from({ length: 5 }).map((_, index) => (
          <div className="flex md:flex-row flex-col md:items-center justify-between border-[1px] mb-20 rounded-lg p-5 md:p-10 md:py-5">
            <div>
              <SimpleText className="text-raven-green-800 text-lg mb-2">
                <Translate msgId={`resources.title${index + 1}`} />
              </SimpleText>
              <SimpleText>
                <Translate msgId={`resources.des${index + 1}`} />
              </SimpleText>
            </div>
            {index % 2 === 0 && (
              <img
                src={
                  index == 0
                    ? manOnCard
                    : index == 2
                    ? cardDeclined
                    : SecurityShield
                }
                className="dark:invert"
              />
            )}
          </div>
        ))}
        <div className="flex md:flex-row flex-col items-center gap-y-5 my-10 justify-between md:px-10 md:my-40">
          <SimpleText className="text-xl md:text-4xl ">
            <Trans
              i18nKey="resources.notABank"
              components={{ span: <span className="text-raven-green-800" /> }}
            />
          </SimpleText>
          <Button variant="primary" className="rounded-lg">
            <Translate msgId="about.signUpNow" />
          </Button>
        </div>
      </div>
      <Footer />
    </div>
  );
}
