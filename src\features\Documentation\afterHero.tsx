import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { ShieldCheck, PaperAirplane } from 'heroicons-react';

export default function AfterHero() {
  return (
    <div className="flex flex-col items-center mt-20">
      <div className="w-full md:w-1/2 flex flex-col items-center  text-center md:text-left md:p-5 gap-y-2">
        <SimpleText className="text-2xl">
          <Translate msgId="documentation.developers" />
        </SimpleText>
        <SimpleText className="text-xl">
          <Translate msgId="documentation.accept" />
        </SimpleText>
        <SimpleText className="">
          <Translate msgId="documentation.coding" />
        </SimpleText>
      </div>
      <div className="flex flex-col md:flex-row gap-5 w-full items-center justify-center border-[1px] py-20 mt-10">
        {Array.from({ length: 3 }).map((_, index) => {
          return (
            <div
              className={`flex gap-2 w-56   bg-[#f8f8f8] dark:bg-slate-800 white p-5 ${
                index == 1
                  ? 'flex-col h-32 shadow-2xl items-start'
                  : 'flex-row h-20 items-center'
              }`}
            >
              <div className="bg-raven-dark-900 text-white rounded-full w-10 h-10 flex items-center justify-center">
                {index == 1 ? (
                  <PaperAirplane className="transform rotate-45" />
                ) : (
                  <ShieldCheck />
                )}
              </div>
              <SimpleText
                className={`${
                  index == 1 ? 'text-lg font-semibold' : 'text-base font-medium'
                }`}
              >
                <Translate msgId={`documentation.card${index + 1}`} />
              </SimpleText>
            </div>
          );
        })}
      </div>
    </div>
  );
}
