import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import LastSection from './lastSection';
interface roleCardsInterface {
  onCardClick: () => void;
}
export default function RoleCards({ onCardClick }: roleCardsInterface) {
  return (
    <div className="mt-32 w-full">
      <SimpleText className="text-xl text-raven-green-800">
        <Translate msgId="careers.explore" />
      </SimpleText>

      <div className="flex  md:p-10 p-5 flex-col pt-5">
        <SimpleText className="text-3xl">
          <Translate msgId="careers.role" />
        </SimpleText>
        <div className="flex flex-wrap gap-10  gap-x-24 mt-5 w-full justify-center ">
          {Array.from({ length: 6 }).map((_, index) => {
            return (
              <div
                className="border-2 hover:border-raven-green-800 transition-all duration-200 cursor-pointer rounded-xl p-5 flex flex-col gap-y-2 lg:w-[27%] md:w-[42%] w-full  justify-center"
                onClick={onCardClick}
              >
                <SimpleText className=" font-semibold text-lg">
                  <Translate
                    msgId={`careers.${
                      index <= 1 || index == 4
                        ? 'roleTitle125'
                        : `roleTitle${index + 1}`
                    }`}
                  />
                </SimpleText>
                <SimpleText>
                  <Translate msgId={`careers.location${index + 1}`} />
                </SimpleText>
                <SimpleText>
                  <Translate
                    msgId={`careers.${
                      index > 0 && index != 3
                        ? 'salary2356'
                        : `salary${index + 1}`
                    }`}
                  />
                </SimpleText>
              </div>
            );
          })}
        </div>
      </div>
      <LastSection />
    </div>
  );
}
