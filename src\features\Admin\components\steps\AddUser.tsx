import { FormLabel } from "@/components/form";
import { Translate } from "@/components/translate";
import { SimpleText } from "@/components/typography";
import { useForm } from "react-hook-form";

type FormValues = {
  email: string;
  nickname: string;
};

const AddUserForm = () => {

  const {
    register,
    formState: { errors },
  } = useForm<FormValues>();

  return (
    <div className='w-96 space-y-3 '>
      <SimpleText className='text-2xl text-center font-bold'>
        <Translate msgId='Add User' />
      </SimpleText>
      <SimpleText className='text-base  text-gray-400 text-center'>
        <Translate msgId='Who would you like to invite? New user added will be notified and signed in via account ' />
      </SimpleText>

      <div className=" w-full ">
        <FormLabel
          name="email"
          text="dashboard.email"
          required
        />
        <input
          className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
          type="text"
          placeholder="Email address"
          {...register('email', { required: true })}
        />
        {errors.email && (
          <SimpleText className=" text-xs text-red-500">
            <Translate msgId="dashboard.emailRequired" />
          </SimpleText>
        )}
      </div>
      <div className=" w-full ">
        <FormLabel
          name="nickname"
          text="User nickname"
          required
        />
        <input
          className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
          type="text"
          placeholder="username"
          {...register('nickname', { required: true })}
        />
        {errors.nickname && (
          <SimpleText className=" text-xs text-red-500">
            <Translate msgId="dashboard.nicknameRequired" />
          </SimpleText>
        )}
      </div>
    </div>
  )
}
 export default AddUserForm