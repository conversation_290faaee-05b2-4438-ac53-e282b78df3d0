import { FC, useState } from 'react';
import Table, {
  TableHead,
  TableBody,
  TableRow,
  TableColumn,
} from 'components/Table';
import { Card, CardPagination } from 'components/Card';
import TransactionItem from './TransactionItem';
import { Transaction } from '@/models/transactions';
import { Translate } from '@/components/translate';

interface TransactionsTableProps {
  loading: boolean;
   transactions: Transaction[];
   total: number;
   limit?: number
}

const TransactionsTable: FC<TransactionsTableProps> = ({ transactions, loading, total, limit = 10 }) => {
  const [page, setPage] = useState(1);

  return (
    <Card variant="outlined">
      <Table
        disableStyle
        disableBorderBottom
        className="lg:rounded-md lg:overflow-hidden"
      >
        <TableHead>
          <TableRow>
            <TableColumn heading>
              <div className='p-1'>
                <input type='checkbox' className=' checked:border-gray-500 checked:bg-gray-500 border border-gray-500 rounded-md' />
              </div>
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.tableAccountName' />
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.tableAccountNo' />
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.type' />
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.amount' />
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.transactionDate' />
            </TableColumn>
            <TableColumn heading>
              <Translate msgId='dashboard.status' />
            </TableColumn>
          </TableRow>
        </TableHead>
        {loading ? (
          <TableBody>
            <TransactionItem loading />
            <TransactionItem loading />
            <TransactionItem loading />
            <TransactionItem loading />
            <TransactionItem loading />
            <TransactionItem loading />
            <TransactionItem loading />
            <TransactionItem loading />
            <TransactionItem loading />
          </TableBody>
        ) : (
          <TableBody>
            {transactions.map((result: Transaction) => (
              <TransactionItem key={result.id} {...result} />
            ))}
          </TableBody>
        )}
      </Table>
      <CardPagination
        total={total}
        limit={limit}
        page={page}
        setPage={setPage}
      />
    </Card>
  );
};

export default TransactionsTable;
