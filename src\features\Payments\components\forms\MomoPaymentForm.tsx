import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { FormLabel } from '@/components/form';
import { SelectMenuOption, SelectNetworkOption } from '@/data/types';
import CountrySelector from '@/components/selector/CountrySelector';
import MomoNetworkSelector from '@/components/selector/MomoNetworkSelector';
import { COUNTRIES } from '@/data/countries';
import { NETWORKS } from '@/data/networks';
import { getCountry, setCountry } from '@/features/Signup/SignupSlice';
import { getNetwork, setNetwork } from '../../paymentsSlice';

type FormValues = {
  phone: string;
  fullName: string;
  amount: number;
};

const MomoPaymentForm = () => {
  const dispatch = useDispatch();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>();

  const [openNetworkOptions, setOpenNetworkOptions] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const handlePayment: SubmitHandler<FormValues> = (data) => {
    // Handle Payment
    console.log(data);
  };

  const handleCountrySelector = (event: any) => dispatch(setCountry(event));
  const handleNetworkSelector = (event: any) => dispatch(setNetwork(event));
  
  const country = useSelector(getCountry);
  const network = useSelector(getNetwork);

  return (
    <div>
      <form onSubmit={handleSubmit(handlePayment)}>
        <div className="space-y-5 cursor-pointer  ">
          <div className=" w-full ">
            <FormLabel
              name="selectNetwork"
              text="dashboard.selectNetwork"
              required
            />
            <MomoNetworkSelector
              id="momo"
              onToggle={() => setOpenNetworkOptions(!openNetworkOptions)}
              open={openNetworkOptions}
              onChange={handleNetworkSelector}
              selectedValue={
                NETWORKS.find(
                  (option) => option.value === network
                ) as SelectNetworkOption
              }
            />
          </div>
          <div className=" w-full ">
            <FormLabel
              name="phoneNumber"
              text="dashboard.phoneNumber"
              required
            />
            <input
              className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="text"
              placeholder="0540539205"
              {...register('phone', { required: true })}
            />
            {errors.phone && (
              <SimpleText className=" text-xs text-red-500">
                <Translate msgId="dashboard.phoneNumberRequired" />
              </SimpleText>
            )}
          </div>
          <div className=" w-full ">
            <FormLabel name="fullName" text="dashboard.fullName" required />
            <input
              className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="text"
              placeholder="Joseph Nartey"
              {...register('fullName', { required: true })}
            />
            {errors.amount && (
              <SimpleText className=" text-xs text-red-500">
                <Translate msgId="dashboard.accountNameRequired" />
              </SimpleText>
            )}
          </div>
          <div className="flex gap-5">
            <div className=" w-full ">
              <FormLabel name="amount" text="dashboard.amount" required />
              <input
                className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="text"
                placeholder="GHS 000.00"
                {...register('amount', { required: true })}
              />
              {errors.amount && (
                <SimpleText className=" text-xs text-red-500">
                  <Translate msgId="dashboard.amountRequired" />
                </SimpleText>
              )}
            </div>
            <div className=" w-full ">
              <FormLabel name="currency" text="dashboard.currency" />
              <CountrySelector
                id={'countries'}
                open={isOpen}
                onToggle={() => setIsOpen(!isOpen)}
                onChange={handleCountrySelector}
                selectedValue={
                  COUNTRIES.find(
                    (option) => option.value === country
                  ) as SelectMenuOption
                }
              />
            </div>
          </div>

          <Button variant="darkPrimary" className="rounded-lg w-full mt-5">
            <Translate msgId="dashboard.makePayment" />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default MomoPaymentForm;
