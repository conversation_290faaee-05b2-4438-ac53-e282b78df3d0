import { Card, CardHeader, CardContent } from '@/components/Card';
import { Translate } from '@/components/translate';
import BankDetailsForm from './BankDetailsForm';

const BankDetails = () => {
  return (
    <Card
      variant="outlined"
      className="w-full bg-raven-green-50 lg:py-10 lg:px-12"
    >
      <CardHeader
        primary={<Translate msgId="dashboard.bankDetails" />}
        secondary={<Translate msgId="dashboard.enterBankDetails" />}
      />
      <CardContent className="bg-white">
        <BankDetailsForm />
      </CardContent>
    </Card>
  );
};

export default BankDetails;
