import { FC } from 'react';
import playstore from '/images/google-play-badge.png';
import { Button } from 'components/button';

interface GoogleButtonProps {
  className?: string;
  href: string;
}
const GoogleButton: FC<GoogleButtonProps> = ({ className, href }) => {
  return (
    <div>
      <Button
        variant="transparent"
        component="a"
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        role="button"
      >
        <img
          src={playstore}
          alt="get it on playstore button"
          className={className}
          title="Click to download on PlayStore"
        />
      </Button>
    </div>
  );
};

export default GoogleButton;
