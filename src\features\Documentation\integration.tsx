import { Trans } from 'react-i18next';
import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import { ArrowRight } from 'heroicons-react';
import { Button } from '@/components/button';
import { Link } from 'react-router-dom';
export default function Integration() {
  return (
    <div className="mt-40">
      <SimpleText className="text-2xl mb-10">
        <Trans
          i18nKey="documentation.integration"
          components={{
            br: <br />
          }}
        />
      </SimpleText>
      <div className="flex md:flex-row flex-col items-center justify-between ">
        <div className="flex flex-col gap-y-10 ">
          <SimpleText>
            <Translate msgId="documentation.robust" />
          </SimpleText>
          <SimpleText>
            <Translate msgId="documentation.client" />
          </SimpleText>
          <SimpleText>
            <Translate msgId="documentation.sdks" />
          </SimpleText>
          <SimpleText>
            <Translate msgId="documentation.comprehensive" />
          </SimpleText>
          <SimpleText>
            <Translate msgId="documentation.sample" />
          </SimpleText>
          <Button
            variant="primary"
            className="md:w-2/3 w-fit rounded-md"
            component={Link}
            to="/docs"
          >
            <Translate msgId="documentation.technical" />
          </Button>
        </div>
        <div className="flex flex-col justify-between gap-y-12 mt-20 md:-mt-20">
          {Array.from({ length: 3 }).map((_, index) => (
            <div className="flex items-center gap-x-2 ">
              <ArrowRight className="text-raven-green-800" />
              <SimpleText className="border-[1px] min-w-[300px] p-5 ">
                <Translate
                  msgId={`documentation.integrationCard${index + 1}`}
                />
              </SimpleText>
            </div>
          ))}
        </div>
      </div>
      <div className="flex flex-col w-full items-center gap-y-5 my-24">
        <SimpleText>
          <Translate msgId="documentation.interested" />
        </SimpleText>
        <Button variant="primary" className="rounded-md">
          <Translate msgId="documentation.contactUs" />
        </Button>
      </div>
    </div>
  );
}
