import { FC, useState } from 'react';
import clsx from '@/features/utils/clsx';
import { Link } from 'react-router-dom';
import { Translate } from '../translate';
import { SimpleText } from '../typography';

interface LinkItem {
  label: string;
  href: string;
}

/** Interface for the data param
 * @interface
 * @property {string} label-translation message ID corresponding to the label.
 *
 * This value is a string identifier used to retrieve the appropriate
 * translated text from the translation file.
 *
 * All labels must be in the tip key of translations en.json
 * @property {string} href-the hyperlink reference (URL) pointing to the page associated with the link.
 */
interface TooltipMenuProps {
  data: LinkItem[];
  position?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}
/***
 * The `Tooltip Component` renders a tooltip item as a clickable link for the nav bar's subroutes
 * @component
 * @param {LinkItem} data - An array of `LinkItem` objects representing the links inside the menu.
 * @param {TooltipMenuProps[position]} position - The position of the tooltip menu relative to its       parent.
   @param {string} className - Custom css styling
@example 
 <TooltipMenu data={[{ label: 'home', href: '/home' }]} position="right" />
 */

const TooltipMenu: FC<TooltipMenuProps> = ({
  data,
  position = 'bottom',
  className
}) => {
  // Positioning classes for the tooltip and the tip
  const [top, setTop] = useState<number>(12);
  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2',
    left: 'right-full top-1/2 transform -translate-y-1/2',
    right: 'left-full top-1/2 transform -translate-y-1/2'
  };

  const tipPositionClasses = {
    bottom: 'top-[-6px] left-1/2 transform -translate-x-1/2 rotate-45',
    top: 'bottom-[-6px] left-1/2 transform -translate-x-1/2 rotate-[-135deg]',
    left: 'right-[-6px] top-1/2 transform -translate-y-1/2 rotate-[135deg]',
    right: 'left-[-6px] top-1/2 transform -translate-y-1/2 -rotate-45'
  };
  const changeHoverElementPosition = (index: number) => {
    const ELEMENT_HEIGHT = 32;
    const INITIAL_TOP_VALUE = 12;
    const finalElementPosition = ELEMENT_HEIGHT * index + INITIAL_TOP_VALUE;
    setTop(finalElementPosition);
  };

  return (
    <div
      className={clsx(
        'relative scale-0 group-hover:block z-10 opacity-0 group-hover:opacity-100 transform  group-hover:scale-100 transition-all duration-200 ease-out',

        className
      )}
      onMouseLeave={() => changeHoverElementPosition(0)}
    >
      {/* Tooltip */}
      <div
        className={clsx(
          'absolute  bg-white shadow-lg rounded-md border border-gray-200 p-3',
          /*p-3 combined is 24*/ positionClasses[position]
        )}
      >
        <ul className="overflow-hidden">
          {data.map(({ href, label }, index) => (
            <li
              key={index}
              className="text-raven-green-800 block h-[32px]  transition-all duration-100 ease-out opacity-0 -translate-x-96 group-hover:opacity-100  group-hover:translate-x-0 cursor-pointer w-full
               "
              style={{
                transitionDelay: `0.${index + 1}s`
              }}
              onMouseEnter={() => changeHoverElementPosition(index)}
            >
              <Link to={href}>
                <SimpleText className="h-[32px] px-2 py-1 w-full">
                  <Translate msgId={`tips.${label}`} />
                </SimpleText>
              </Link>
            </li>
          ))}
          {/* hover state element */}
          <div
            className=" absolute bg-gray-300  left-[12px] rounded-md -z-10 "
            style={{
              height: 32,
              width: 'calc(100% - 24px)', //24 is the padding of the container combined
              top,
              transition: 'all 0.2s ease'
            }}
          ></div>
        </ul>

        {/* Tip */}
        <div
          className={clsx(
            'absolute w-3 h-3 bg-white border-l border-t border-gray-200 transform -z-10',
            tipPositionClasses[position]
          )}
        />
      </div>
    </div>
  );
};

export default TooltipMenu;
