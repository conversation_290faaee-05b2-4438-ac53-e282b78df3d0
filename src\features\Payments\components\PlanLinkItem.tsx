import { Button } from "@/components/button"
import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"
import { Dispatch, FC, SetStateAction } from "react"

interface PlanLinkItemProps {
  title: string
  icon: string
  description: string
  id?: number
  plan: string
  selectedPlan: string
  setSelectedPlan: Dispatch<SetStateAction<string>>
}

const PlanLinkItem:FC<PlanLinkItemProps> = ({ title, icon, plan, description, selectedPlan, setSelectedPlan }) => {
  return (
    <Button variant="light" className={`${plan === selectedPlan ? ' border-raven-green-800 !border-[3px]': '!border-[2px]'} !p-5 flex items-center justify-between rounded-md`} onClick={()=> setSelectedPlan(plan)}>
      <div>
        <img src={icon} alt='one time' className=' inline-block mb-3' />
        <SimpleText component="h1" className=" text-lg">
          <Translate msgId={title} />
        </SimpleText>
        <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
          <Translate msgId={description} />
        </SimpleText>
      </div>
    </Button>
  )
}

export default PlanLinkItem