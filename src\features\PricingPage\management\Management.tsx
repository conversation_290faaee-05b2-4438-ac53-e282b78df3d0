import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'

const Management = () => {
  return (
    <div>
      <section className='py-5 lg:py-16'>
        <section className=' container mx-auto '>
          <SimpleText className=" text-center font-semibold lg:w-1/2 uppercase mx-auto leading-tight text-4xl  ">
            <Translate msgId="landing.mangTitle" />
          </SimpleText>

          <div className='space-y-10 lg:w-1/2 mx-auto px-5 lg:px-0  pt-6 md:pt-10'>
            <SimpleText className=" text-center  mx-auto">
              <Translate msgId="landing.mangDes1" />
            </SimpleText>
            <SimpleText className=" text-center  mx-auto">
              <Translate msgId="landing.mangDes2" />
            </SimpleText>
        </div>

          <div className="max-w-4xl mx-auto mt-10 ">
            <img src='/images/dashboard.png' className="shrink-0" />
          </div>
        </section>
      </section>
    </div>
  )
}

export default Management