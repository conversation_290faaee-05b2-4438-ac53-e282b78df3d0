import { FC, useState } from 'react';
import { Skeleton } from 'components/skeleton';
import {
  CartesianGrid,
  Tooltip,
  YAxis,
  XAxis,
  Line,
  ResponsiveContainer,
  Area,
  ComposedChart,
  TooltipProps,
} from 'recharts';
import { SimpleText } from 'components/typography';
import EmptyChart from './EmptyChart';
import { Translate } from '@/components/translate';
import clsx from 'features/utils/clsx';
import formatCurrencyUtil from '../utils/formatCurrency';
import ActionsDropdown from '@/components/button/ActionsDropdown/ActionsDropdown';
import { Button } from '@/components/button';

interface RevenueChartAProps {
  loading: boolean;
  data: Record<string, string | number>[];
  title: string;
}

const RevenueChartA: FC<RevenueChartAProps> = ({
  loading,
  data = [],
  title,
}) => {
  const [open, setOpen] = useState(false);

  return (
    <Skeleton active={loading} className="h-full">
      <div
        className={clsx('rounded relative', {
          'h-full': data.length === 0,
        })}
      >
        <div className="flex gap-5 flex-col lg:flex-row lg:items-center">
          <SimpleText truncate className="font-medium text-xl">
            <Translate msgId={title} />
          </SimpleText>

          <div className="px-4 py-2 rounded-lg bg-gray-100 flex gap-6 items-center">
            <span className="h-6 w-6 rounded-full bg-[#4169E11F]"/>
            <SimpleText truncate className=" font-semibold text-gray-500">
              <Translate msgId={"dashboard.revenue"} />
            </SimpleText>
          </div>
          <div className="px-4 py-2 rounded-lg bg-gray-100 flex gap-6 items-center">
            <span className="h-6 w-6 rounded-full bg-raven-green-800"/>
            <SimpleText truncate className=" font-semibold text-gray-500">
              <Translate msgId={'dashboard.netProfit'} />
            </SimpleText>
          </div>
          <div className="px-4 py-2 rounded-lg bg-gray-100 flex gap-6 items-center">
            <span className="h-6 w-6 rounded-full bg-[#FF4F79]"/>
            <SimpleText truncate className=" font-semibold text-gray-500">
              <Translate msgId={"dashboard.averageTransactions"} />
            </SimpleText>
          </div>
          
          <div className="ml-auto">
            <ActionsDropdown onToggle={()=> setOpen(!open)} open={open} label='dashboard.filter'>
              <Button
                variant="light"
                onClick={()=> setOpen(!open)}
                className=" !border-none flex items-center gap-1 w-full !px-5 !py-2 "
              >
                2022
              </Button>
              <Button
                variant="light"
                onClick={()=> setOpen(!open)}
                className=" !border-none flex items-center gap-1 w-full !px-5 !py-2 "
              >
                2023
              </Button>
              <Button
                variant="light"
                onClick={()=> setOpen(!open)}
                className=" !border-none flex items-center gap-1 w-full !px-5 !py-2 "
              >
                2024
              </Button>
            </ActionsDropdown>

          </div>
        </div>
        <div
          className={clsx('h-full aspect-w-16 aspect-h-9', {
            'mt-10': data.length > 0,
          })}
        >
          <div className="flex items-center h-full justify-center">
            {data.length === 0 ? (
              <EmptyChart variant="bar" />
            ) : (
              <ResponsiveContainer minHeight={500}>
                <ComposedChart
                  width={500}
                  height={900}
                  data={data}
                  margin={{
                    top: 20,
                    right: 20,
                    bottom: 20,
                    left: 20,
                  }}
                >
                  <CartesianGrid stroke="#f5f5f5" strokeDasharray="3 3" />
                  <XAxis dataKey="name" scale="band" />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="revenue"
                    fill="#4169E11F"
                    stroke="#8884d8"
                    strokeWidth={0}
                  />
                  <Line
                    type="monotone"
                    dataKey="profit"
                    stroke="#128542"
                    strokeWidth={3}
                  />
                  <Line
                    type="monotone"
                    dataKey="average_transaction"
                    stroke="#FF4F79"
                    strokeDasharray="10"
                    strokeWidth={2}
                  />
                </ComposedChart>
              </ResponsiveContainer>
            )}
          </div>
        </div>
      </div>
    </Skeleton>
  );
};

export default RevenueChartA;

const CustomTooltip: React.FC<TooltipProps<any, any>> = ({
  active,
  payload,
  label,
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-raven-green-800 py-3 px-5 text-white rounded-lg ">
        <p className="font-bold">{`${label} Analytics `}</p>
        {payload.map((entry, index) => (
          <div key={index}>
            <p className="capitalize">
              {entry.name.replace('_', ' ')}:{' '}
              <span className="font-semibold">
                {formatCurrencyUtil(entry.value)}
              </span>
            </p>
          </div>
        ))}
      </div>
    );
  }
  return null;
};