import { AppDispatch, RootState } from '@/store/store';
import { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  getSelectedBaseCurrency,
  requestExchangeRates,
  getConvertorInput,
  getSelectedReceivingCurrency,
  makeGetConversion,
} from '@/features/Landing/exchange-app/exchangeAppSlice';

const useCurrencyConvertor = () => {
  const dispatch: AppDispatch = useDispatch();
  const convertorInputValue = useSelector(getConvertorInput);
  const selectedBaseCurrency = useSelector(getSelectedBaseCurrency);
  const selectedReceivingCurrency = useSelector(getSelectedReceivingCurrency);

  const getConversion = makeGetConversion();
  const exchangeRate = useSelector((state: RootState) => {
    const utils = {
      base: selectedBaseCurrency.id,
      receiving: selectedReceivingCurrency.id,
      input: convertorInputValue,
    };
    const rates = getConversion(state, utils);
    return rates;
  });

  /**
   * getExchangeRates is a callback that dispatches the action to request the
   * latest exchange rates based on the currently selected base currency.
   *
   * @function
   * @callback
   */
  const getExchangeRates = useCallback(
    () => dispatch(requestExchangeRates({ base: selectedBaseCurrency.id })),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch]
  );

  /**
   * This effect hook calls getExchangeRates on mount and whenever
   * getExchangeRates changes, ensuring the application always has the latest
   * exchange rates.
   *
   * @effect
   */
  useEffect(() => {
    getExchangeRates();
  }, [getExchangeRates]);

  return { exchangeRate };
};

export default useCurrencyConvertor;
