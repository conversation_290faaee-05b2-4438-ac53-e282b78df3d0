import clsx from '@/features/utils/clsx';
import { BadgeColor } from '@/models/badge-props';
import { FC, ReactNode } from 'react';

const colors = {
  gray: 'bg-gray-100 text-gray-800',
  gold: 'bg-yellow-100 text-yellow-800',
  green: 'bg-green-100 text-green-800',
  black: 'bg-black text-white',
  purple: 'bg-purple-100 text-purple-800',
};

interface Sizes {
  sm: 'px-2.5 py-0.5 text-xs';
  lg: 'px-3 py-0.5 text-sm';
}

const sizes: Sizes = {
  sm: 'px-2.5 py-0.5 text-xs',
  lg: 'px-3 py-0.5 text-sm',
};

interface BadgeProps {
  size: 'sm' | 'lg';
  color: BadgeColor;
  children: ReactNode;
  className?: string;
}

const Badge: FC<BadgeProps> = ({ size = 'sm', color, children, className }) => {
  return (
    <span
      className={clsx(
        'inline-flex items-center rounded-full font-medium',
        sizes[size],
        colors[color],
        className
      )}
    >
      {children}
    </span>
  );
};

export default Badge;
