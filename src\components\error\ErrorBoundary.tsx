import { useState, useEffect, ReactNode, FC } from 'react';

interface ErrorBoundaryProps {
  children: ReactNode;
}

/**
 * ErrorBoundary component displays an error message and options for recovery
 * when an error is caught within its children components.
 *
 * @component
 *
 * @example
 * <ErrorBoundary>
 *   <AppComponent />
 * </ErrorBoundary>
 */
const ErrorBoundary: FC<ErrorBoundaryProps> = ({ children }) => {
  const [hasErrored, setHasErrored] = useState(false);

  /**
   * useEffect to handle error eventgs within the app.
   * Logs the error to the console in development mode.
   * @param {object} error - The caught error object.
   */
  useEffect(() => {
    const handleComponentError = (error: ErrorEvent) => {
      // This is where we handle the error. In production we can log it to an error reporting service like logRocket
      console.error('Error caught by ErrorBoundary:', error);
      setHasErrored(true);
    };

    window.addEventListener('error', handleComponentError);

    return () => {
      window.removeEventListener('error', handleComponentError);
    };
  }, []);

  if (hasErrored) {
    return (
      <div className="error-image-overlay">
        <img
          className="error-image"
          src="https://i.imgur.com/3suxlvm.png"
          alt="entangled shoe lace depicting error"
        />
        <div className="p-3 text-center px-5">
          <h4>Something went wrong.</h4>
          <p>
            This may be because of an error that we are working to get fixed.
            Try reloading this page, or go back to your home page.
          </p>
          <div className="mt-3 w-72 m-auto">
            <button
              onClick={() => window.location.reload()}
              className="btn btn-primary"
            >
              Reload Page
            </button>
            <button
              onClick={() => (window.location.href = '/')}
              className="btn btn-dark mt-2 px-10"
            >
              Go Back Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default ErrorBoundary;
