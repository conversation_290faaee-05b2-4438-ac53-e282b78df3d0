export interface User {
  id: string;
  email: string;
  username: string;
  raven_tag: string;
  phone_number: string;
  is_active: boolean;
  verification_status: string;  // "pending" | "verified" | "rejected"; 
  roles: string[];
  created_at: string;
}

export interface SignupData {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface SignupResponseDto {
  data: SignupData;
  message: string;
}

export interface SignUpRequestDto {
  email: string;
  username: string;
  password: string;
  role: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  country: string;
  accept_terms: boolean;
  marketing_emails: boolean;
}

