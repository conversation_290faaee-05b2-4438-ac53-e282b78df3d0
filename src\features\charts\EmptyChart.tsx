import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import { FC } from 'react';

interface EmptyChartProps {
  variant?: 'bar' | 'pie';
}

const EmptyChart: FC<EmptyChartProps> = ({ variant = 'pie' }) => {
  return (
    <div className="flex items-center flex-col justify-center text-center mt-5 pb-3">
      {variant === 'pie' ? (
        <svg
          className="mx-auto h-12 w-12 text-gray-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            vectorEffect="non-scaling-stroke"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"
          />
          <path
            vectorEffect="non-scaling-stroke"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"
          />
        </svg>
      ) : (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            vectorEffect="non-scaling-stroke"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      )}
      <SimpleText
        component="h3"
        className="mt-2 text-sm font-medium text-gray-900"
      >
        <Translate msgId="dashboard.noData" />
      </SimpleText>
      <SimpleText className="mt-1 text-sm text-gray-500 md:w-72">
        <Translate msgId="dashboard.makeSales" />
      </SimpleText>
    </div>
  );
};

export default EmptyChart;
