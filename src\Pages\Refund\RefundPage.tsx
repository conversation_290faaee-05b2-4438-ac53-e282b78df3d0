import { Translate } from "@/components/translate"
import Transactions from "@/features/Transactions/components/Transactions"
import useGetRefunds from "@/features/Transactions/hooks/useGetRefunds";
import { Transaction } from "@/models/transactions";

interface RefundsUtil {
  isLoading: boolean;
  isUninitialized: boolean;
  data: {refunds: Transaction, total: number };
}

const RefundPage = () => {
  const limit = 10;
  const page = 1;

  const { loading, refunds, total  } = useGetRefunds({
    page,
    limit,
    select: ({ isLoading, isUninitialized, data }: RefundsUtil) => ({
      loading: isLoading || isUninitialized,
      refunds: data?.refunds ?? [],
      total: data?.total ?? 0
    }),
  });

  return <Transactions transactions={refunds} loading={loading} total={total}  page={<Translate msgId="dashboard.refund" />} />
}

export default RefundPage