import RevenueChartA from '@/features/charts/RevenueChartA';
import useGetBusinessStats from '@/hooks/useGetBusinessStats';

interface MonthlyOverviewUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: Record<string, unknown>;
}

const StatisticsOverviewChart = () => {
  const { overview = [], loading } = useGetBusinessStats({
    select: ({ isLoading, isUninitialized, data }: MonthlyOverviewUtils) => ({
      loading: isLoading || isUninitialized,
      overview: data?.overview,
    }),
  });

  return (
    <div className="bg-white p-5">
      <RevenueChartA
        title="dashboard.statistics"
        loading={loading}
        data={overview}
      />
    </div>
  );
};

export default StatisticsOverviewChart;
