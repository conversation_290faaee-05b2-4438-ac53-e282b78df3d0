import rtkQueryBase from "../api/rtk-query-base";


const extendedAPI = rtkQueryBase.injectEndpoints({
    overrideExisting: true,
    endpoints: (builder) => ({
        getTransactions: builder.query({
            query: (params) => ({
                url: '/api/transactions/',
                params
            })
        }),
        getTransaction: builder.query({
            query: (params) => `/api/transactions/${params.id}`
        }),
        getRefunds: builder.query({
            query: (params) => ({
                url: '/api/transactions/refunds',
                params
            })
        }),
    })
});

export const { useGetRefundsQuery, useGetTransactionsQuery, useGetTransactionQuery } = extendedAPI;

export default extendedAPI;
