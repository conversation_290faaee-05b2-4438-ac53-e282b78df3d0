import settingsBaseQuery from "@/services/api/settings-base-query";

const extendedAPI = settingsBaseQuery.injectEndpoints({
    overrideExisting: true,
    endpoints: (builder) => ({
        getIntegrations: builder.query({
            query: () => ({
                url: '/api/settings/integrations',
            })
        }),
        getAccounts: builder.query({
            query: () => ({
                url: '/api/settings/accounts',
            })
        }),
    })
})

export const { useGetIntegrationsQuery, useGetAccountsQuery } = extendedAPI