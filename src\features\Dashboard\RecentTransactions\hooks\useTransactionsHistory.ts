import { useMemo } from 'react';
import { useGetTransactionsHistoryQuery } from '@/services/endpoints/getOverView';

const usePayoutsHistory = (options: { page: number, limit: number, select: any }) => {
    const { page = 1, limit = 10, select } = options;

    return useGetTransactionsHistoryQuery(useMemo(() => ({
        page,
        limit
    }), [
        page,
        limit
    ]), {
        selectFromResult: (result) => select(result)
    });
};

export default usePayoutsHistory;
