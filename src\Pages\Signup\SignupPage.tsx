import { Trans } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import womanSopping from '/images/young-woman.png';
import { LinkText, Translate } from 'components/translate';
import { SimpleText } from 'components/typography';
import SignupForm from 'features/Signup/form/SignupForm';
import { getRedirect } from '@/features/Signup/SignupSlice';
import { useEffect } from 'react';
import { Logo } from '@/components/logo';

const SignupPage = () => {
  const redirect = useSelector(getRedirect);
  const navigate = useNavigate();

  // navigate to redirect route
  useEffect(() => {
    if (redirect) {
      navigate(redirect);
    }
  }, [redirect, navigate]);

  return (
    <div className="mt-10 signup-page">
      <div className="flex md:justify-end md:pr-20 pl-8">
        <div className="w-28">
          <Link to="/">
            <Logo className="w-100" />
          </Link>
        </div>
      </div>
      <div>
        <div className="md:flex w-full gap-4 justify-between">
          <div className="w-full md:w-1/2 p-10 md:p-20">
            <div className="text-center">
              <div className="mb-5">
                <SimpleText component="h1" className="font-bold text-2xl mb-3">
                  <Translate msgId="auth.signup" />
                </SimpleText>
                <SimpleText>
                  <Translate msgId="auth.signupSub" />
                </SimpleText>
              </div>
              <SignupForm />
              <div className="flex justify-center gap-3 mb-10">
                <SimpleText>
                  <Translate msgId="auth.accountAlready" />
                </SimpleText>
                <Link to="/signin">
                  <SimpleText className="text-raven-link">
                    <Translate msgId="auth.login" />
                  </SimpleText>
                </Link>
              </div>
              <SimpleText component="small">
                <Trans
                  i18nKey="auth.agreeToTerms"
                  components={{
                    link1: (
                      <LinkText to="http://localhost:5173/terms" title="Terms">
                        Terms of Service
                      </LinkText>
                    ),
                    link2: (
                      <LinkText
                        to="http://localhost:5173/privacy-policy"
                        title="Privacy Policy"
                      >
                        Privacy Policy
                      </LinkText>
                    ),
                  }}
                />
              </SimpleText>
            </div>
          </div>
          <div className="hidden md:block shrink">
            <div className="relative h-hull flex flex-col justify-between">
              <div className="relative top-72">
                <img src={womanSopping} alt="woman shopping" />
              </div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="677"
                height="746"
                viewBox="0 0 677 746"
                fill="none"
              >
                <path
                  d="M153.663 84.4082C408.604 84.4081 865.197 -81.3771 937.864 51.7352C1001.79 168.827 934.977 789.675 917.969 937.425C915.878 955.591 900.507 969 882.222 969H178.38C163.474 969 150.163 960.001 144.99 946.021C93.5484 807.01 -158.823 84.4083 153.663 84.4082Z"
                  fill="#11E342"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignupPage;
