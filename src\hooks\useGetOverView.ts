import { useGetOverviewQuery } from "@/services/endpoints/getOverView";

const useOverview = (options: { select: any }) => {
    const { select } = options;

    // pass the select funtion from the component to select the pieces of data we want
    return useGetOverviewQuery(undefined, {
        selectFromResult: (data) => {
            return select(data)
        }
    });
};

export default useOverview;
