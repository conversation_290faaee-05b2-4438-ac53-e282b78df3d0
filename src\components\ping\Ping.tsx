import { FC, ReactNode } from 'react';

interface PingProps {
  position:
    | 'top-right'
    | 'top-left'
    | 'bottom-right'
    | 'bottom-left'
    | 'center-right';
  children: ReactNode;
  className?: string;
  active: boolean;
}

const positions = {
  'top-right': '-top-1 -right-1',
  'top-left': '-top-1 -left-1',
  'bottom-right': '-bottom-1 -right-1',
  'bottom-left': '-bottom-1 -left-1',
  'center-right': 'top-0 bottom-0 right-3.5 h-full flex items-center',
};

const Ping: FC<PingProps> = ({
  children,
  className,
  position = 'top-right',
  active = true,
}) => {
  const rootClass = `absolute z-10 flex ${positions[position]} ${className}`;

  return (
    <div className="relative">
      {active && (
        <span className={rootClass}>
          <div className="flex h-3 w-3 relative">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-amber-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-3 w-3 bg-amber-500"></span>
          </div>
        </span>
      )}
      {children}
    </div>
  );
};

export default Ping;
