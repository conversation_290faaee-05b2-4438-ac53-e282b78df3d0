import { Button } from '@/components/button'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'
import { CheckCircle } from 'heroicons-react'

const PayoutCalculator = () => {
  return (
    <div>
      <section className='py-5 lg:py-28'>
        <section className=' container mx-auto '>
          <SimpleText className=" text-center font-semibold lg:w-1/3 mx-auto leading-tight text-4xl  ">
            <Translate msgId="landing.payoutTitle" />
          </SimpleText>

          <div className='space-y-10 lg:w-3/5 mx-auto px-5 lg:px-0  pt-6 md:pt-10'>
            <SimpleText className=" text-center lg:w-1/2 mx-auto">
              <Translate msgId="landing.payoutDes" />
            </SimpleText>

            <div className="mt-6 flex-col md:flex-row gap-3 flex md:items-center justify-center">
              <Button
                variant="darkPrimary"
                className="mr-5 rounded"
                type="button"
              >
                <Translate msgId="landing.getStarted" />
              </Button>
              <Button
                variant="light"
                className="mr-5 !bg-raven-dark-900 !text-white border-2 !border-raven-green-800 rounded "
                type="button"
              >
                <Translate msgId="landing.contactSales" />
              </Button>
            </div>
          </div>

          <div className="max-w-2xl mx-auto mt-10 border rounded-xl p-10">
            <div className="bg-[#191919] pb-5 rounded">
              <SimpleText className="flex items-center px-5 gap-2 text-gray-300 py-2">
                <Translate msgId="dashboard.poweredBy" />
                <Button
                  variant="transparent"
                  className="!px-0 text-raven-green-800 font-bold "
                >
                  <Translate msgId="R" />
                </Button>
                <Button
                  variant="transparent"
                  className="!px-0 font-bold "
                >
                  <Translate msgId="landing.ravenhive" />
                </Button>
              </SimpleText>

              <div className="bg-black/40 px-5 py-5 space-y-5 w-full">
                <SimpleText className='uppercase'>
                  <Translate msgId='landing.payoutTxt1' />
                </SimpleText>
                <Button variant='darkPrimary' className='rounded-xl w-full text-left text-xl !px-3 font-bold'>
                  <Translate msgId="GHS 100.00" />
                </Button>
              </div>

              <div className="p-5 shadow-lg">
                <div className="flex justify-between items-center">
                  <div className="">
                    <SimpleText className='uppercase text-sm'>
                      <Translate msgId="landing.payoutTxt2" />
                    </SimpleText>
                    <SimpleText className='uppercase text-xl font-bold'>
                      <Translate msgId="GHS 98.02" />
                    </SimpleText>
                  </div>
                  <div className="text-right">
                    <SimpleText className='uppercase text-sm'>
                      <Translate msgId="landing.payoutTxt3" />
                    </SimpleText>
                    <SimpleText className='uppercase text-raven-green-800 text-xl font-bold'>
                      <Translate msgId="GHS 1.98" />
                    </SimpleText>
                  </div>
                </div>
              </div>

              <div className="p-5 space-y-5">
                <div className="flex justify-between items-center">
                  <SimpleText className=' text-sm'>
                    <Translate msgId="landing.payoutTxt4" />
                  </SimpleText>
                  <CheckCircle className='text-raven-green-800' />
                </div>
                <div className="flex justify-between items-center">
                  <SimpleText className=' text-sm'>
                    <Translate msgId="landing.payoutTxt5" />
                  </SimpleText>
                  <CheckCircle className='text-raven-green-800' />
                </div>
                <div className="flex justify-between items-center">
                  <SimpleText className=' text-sm'>
                    <Translate msgId="landing.payoutTxt6" />
                  </SimpleText>
                  <CheckCircle className='text-raven-green-800' />
                </div>
                <div className="flex justify-between items-center">
                  <SimpleText className=' text-sm'>
                    <Translate msgId="landing.payoutTxt7" />
                  </SimpleText>
                  <CheckCircle className='text-raven-green-800' />
                </div>
                <div className="flex justify-between items-center">
                  <SimpleText className=' text-sm'>
                    <Translate msgId="landing.payoutTxt8" />
                  </SimpleText>
                  <CheckCircle className='text-raven-green-800' />
                </div>
              </div>
            </div>
          </div>
        </section>
      </section>
    </div>
  )
}

export default PayoutCalculator