import { FC, MutableRefObject, ReactNode, useEffect, useRef } from 'react';
import Button from '../Button';
import { ChevronDown, ChevronUp } from 'heroicons-react';
import { Translate } from '@/components/translate';



export interface ActionDropDownProps {
  open: boolean;
  disabled?: boolean;
  label: string;
  onToggle: () => void;
  children: ReactNode;
}

const ActionsDropdown:FC<ActionDropDownProps> = ({ label, children, open, onToggle, disabled=false }) => {
    const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const mutableRef = ref as MutableRefObject<HTMLDivElement | null>;

    const handleClickOutside = (event: any) => {
      if (
        mutableRef.current &&
        !mutableRef.current.contains(event.target) &&
        open
      ) {
        onToggle();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ref]);


  return (
    <div ref={ref} className="relative inline-block text-left">
      <div>
        <Button
          variant='light'
          type="button"
          className="inline-flex items-center gap-3 justify-center w-full rounded-md border border-gray-300 shadow-sm  bg-white  hover:bg-gray-50 focus:outline-none"
          onClick={onToggle}
          disabled={disabled}
        >
          <Translate msgId={label} />
          {!open && <ChevronDown />}
          {open && <ChevronUp />}
        </Button>
      </div>

      {open && (
        <div className="origin-top-right absolute right-0 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
          <div className="py-1">
            {children}
          </div>
        </div>
      )}
    </div>
  );
};

export default ActionsDropdown ;
