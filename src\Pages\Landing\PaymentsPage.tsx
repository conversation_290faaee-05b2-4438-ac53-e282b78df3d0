import { Header } from '@/components/header';
import Footer from '@/features/footer/Footer';
import Developers from '@/features/PaymentsLanding/developers/Developers';
import Empower from '@/features/PaymentsLanding/empower/Empower';
import GrowRevenue from '@/features/PaymentsLanding/grow-revenue/GrowRevenue';
import Hero from '@/features/PaymentsLanding/hero/Hero';
import HowItWorks from '@/features/PaymentsLanding/how-it-works/HowItWorks';
import Payout from '@/features/PaymentsLanding/payout/Payout';
import PlatFormUsers from '@/features/PaymentsLanding/platform-users/PlatFormUsers';
import Terminal from '@/features/PaymentsLanding/terminal/Terminal';
import Transaction from '@/features/PaymentsLanding/transaction/Transaction';

const PaymentsPage = () => {
  return (
    <>
      <Header />
      <main>
        <div className="body">
          <Hero />
        </div>
        <div className="body">
          <PlatFormUsers />
        </div>
        <div className="body">
          <HowItWorks />
        </div>
        <div className="body">
          <Empower />
        </div>
        <div className="body">
          <Terminal />
        </div>
        <div className="body">
          <Developers />
        </div>
        <div className="body">
          <GrowRevenue />
        </div>
        <div className="body">
          <Payout />
        </div>
        <div className="body">
          <Transaction />
        </div>
        <Footer />
      </main>
    </>
  );
};

export default PaymentsPage;
