import React, { useMemo, useCallback, FC, ReactNode, MouseEvent } from 'react';
import { Link } from 'react-router-dom';
import useMatchPath from '@/hooks/useMatchPath';
import useSidebar from '@/hooks/useSidebar';
import { ChevronRight, ChevronDown } from 'heroicons-react';
import clsx from '@/features/utils/clsx';

interface DashboardNavigationProps {
  item?: boolean;
  subnav?: boolean;
  // eslint-disable-next-line no-unused-vars
  onClick?: (event: React.MouseEvent) => void;
  preventDefault?: boolean;
  icon?: React.ElementType;
  className?: string;
  href?: string;
  nestedHref?: string;
  depth?: string;
  children: ReactNode;
  open?: boolean;
}

const DashboardNavigation: FC<DashboardNavigationProps> = ({
  item,
  href,
  depth,
  subnav,
  onClick,
  children,
  className,
  preventDefault,
  icon: Icon,
  nestedHref = 'none',
  open,
  ...rest
}) => {
  const active = useMatchPath(href);
  const nestedActive = useMatchPath(nestedHref);
  const focus = useMemo(
    () => (active || nestedActive) && !subnav,
    [nestedActive, active, subnav]
  );
  const [sidebarActive, sidebarActions] = useSidebar();

  const Component = useMemo(
    () => (!href ? 'p' : (props: any) => <Link to={href} {...props} />),
    [href]
  );

  const handleClick = useCallback(
    (event: MouseEvent) => {
      if (onClick) {
        onClick(event);
      }

      if (subnav) {
        return;
      }

      if (preventDefault) {
        return;
      }

      if (sidebarActive) {
        (
          sidebarActions as {
            on: () => void;
            off: () => void;
            toggle: () => void;
          }
        ).off();
      }
    },
    [subnav, onClick, sidebarActive, sidebarActions, preventDefault]
  );

  const chevron = useMemo(() => {
    if (!subnav) {
      return null;
    }

    const Icon = (active && open) || open ? ChevronDown : ChevronRight;

    return <Icon className="text-gray-700 ml-auto" />;
  }, [subnav, active, open]);

  if (!item) {
    return (
      <nav className={clsx('flex-1 px-3 space-y-1', className)}>{children}</nav>
    );
  }

  return (
    <Component
      className={clsx(
        'group flex cursor-pointer items-center px-2 py-2 font-medium rounded-md text-sm',
        {
          'bg-raven-green-800 text-white': focus,
          'text-raven-green-100 hover:bg-raven-green-500 hover:bg-opacity-75':
            !focus,
          'pl-11': depth === '1',
          'h-10': !Icon,
        },
        className
      )}
      onClick={handleClick}
      {...rest}
    >
      {Icon ? <Icon className="mr-3 h-6 w-6 text-gray-700" /> : null}
      {children}
      {chevron}
    </Component>
  );
};

export default DashboardNavigation;
