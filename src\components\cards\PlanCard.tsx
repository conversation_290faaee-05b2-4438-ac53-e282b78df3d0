import { FC } from 'react';
import { CheckCircle } from 'heroicons-react';
import { ButtonVariants } from '@/models/button-props.interface';
import { BadgeColor } from '@/models/badge-props';
import { Badge } from 'components/badge';
import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import { Button } from '../button';
import { Link } from 'react-router-dom';
import { TextSecondary } from '../typography/Text';
import clsx from '@/features/utils/clsx';

interface PlanCardProps {
  price: string;
  features: Record<string, string>[];
  buttonVariant: ButtonVariants;
  buttonLink: string;
  badgeColor: BadgeColor;
  tier: string;
  freeTier?: boolean;
}
const PlanCard: FC<PlanCardProps> = ({
  price,
  features,
  buttonLink,
  buttonVariant,
  badgeColor,
  tier,
  freeTier,
}) => {
  return (
    <div className="bg-white dark:bg-gray-900 border rounded-3xl py-8 px-6">
      <div className="flex items-center justify-end">
        <Badge className="py-2 px-5" size="lg" color={badgeColor}>
          <SimpleText className="text-md">
            <Translate msgId={tier} />
          </SimpleText>
        </Badge>
      </div>
      <SimpleText className={clsx('text-3xl mt-3', { 'font-bold': freeTier })}>
        {!freeTier ? (
          <>
            {price}
            / <Translate msgId="home.month" /> Limit
          </>
        ) : (
          <Translate msgId={price} />
        )}
      </SimpleText>
      <SimpleText className="text-raven-green-800 text-2xl mt-3">
        <Translate msgId="home.features" />
      </SimpleText>
      <ul className="space-y-6 mt-10">
        {features.map((feature, ind) => (
          <li key={ind} className="flex gap-4">
            <div className="shrink-0">
              <CheckCircle className="h-5 w-5 mt-1 text-raven-green-800" />
            </div>
            <div>
              <SimpleText className="font-bold">
                <Translate msgId={feature.title} />
              </SimpleText>
              <TextSecondary size="md">
                <Translate msgId={feature.description} />
              </TextSecondary>
            </div>
          </li>
        ))}
      </ul>
      <div className="mt-10 w-full">
        <Button
          component={Link}
          to={buttonLink}
          variant={buttonVariant}
          className="rounded-full w-full block text-center"
        >
          <Translate msgId="home.getStarted" />
        </Button>
      </div>
    </div>
  );
};

export default PlanCard;
