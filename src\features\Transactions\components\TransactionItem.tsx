import { FC } from 'react';
import { Skeleton } from 'components/skeleton';
import { TableRow, TableColumn } from 'components/Table';
import { TextSecondary } from 'components/typography/Text';
import clsx from '@/features/utils/clsx';
import { Link } from 'react-router-dom';
import formatCurrencyUtil from '@/features/utils/formatCurrency';
interface TransactionItemProps {
  id?: string;
  type?: string;
  status?: string;
  amount?: number;
  timestamp?: string;
  name?: string;
  accountNumber?: number;
  photo?: string;
  loading?: boolean;
}

const TransactionItem: FC<TransactionItemProps> = ({
  timestamp,
  type,
  status,
  accountNumber,
  amount,
  id,
  name,
  loading = false,
}) => (
  <TableRow nowrap>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="dark">
          {loading ? 'Name' : <input type='checkbox' className=' checked:border-gray-500 checked:bg-gray-500 border border-gray-500 rounded-md' />}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <Link to={`/dashboard/transactions/${id}`}>
          <TextSecondary size="sm" shade="dark">
            {loading ? 'Name' : name}
          </TextSecondary>
        </Link>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="light" transform="capitalize">
          {loading ? 'AccountNumber' : accountNumber}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="light" transform="capitalize">
          {loading ? 'Type' : type}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="dark">
          {loading ? 'Amount' : formatCurrencyUtil(amount as number)}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="dark">
          {loading ? 'Timestamp' : timestamp}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="dark" className={clsx(' p-1 rounded-lg text-center text-sm',{
          'bg-raven-green-50 text-raven-green-800': status === 'Completed',
          'bg-yellow-50 text-yellow-500': status === 'Pending',
        })}>
          {loading ? 'Status' : status}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="dark">
          {loading ? 'Status' : (status == 'Completed' ? 
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-red-600">
              <path strokeLinecap="round" strokeLinejoin="round" d="m19.5 4.5-15 15m0 0h11.25m-11.25 0V8.25" />
            </svg>
          : 
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-raven-green-800">
              <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 19.5 15-15m0 0H8.25m11.25 0v11.25" />
            </svg>
          )}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
  </TableRow>
);

export default TransactionItem;
