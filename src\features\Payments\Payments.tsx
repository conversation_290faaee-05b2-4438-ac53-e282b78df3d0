import { Card, CardContent, CardHeader } from '@/components/Card';
import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import PaymentItem from './components/PaymentItem';
import ScheduleBillModal from './components/ScheduleBillModal';
import useGetPayments from './hooks/useGetPayments';
import { Payment } from '@/models/payment-option';
import PaymentLoading from './components/PaymentLoading';
import { Collection } from 'heroicons-react';
import { SearchBar } from '@/components/search';
import { TabSwitch } from '@/components/TabSwitch';
import { Skeleton } from '@/components/skeleton';
import { DashboardContent, DashboardGrid } from '@/layouts/dashboard';
import DashboardHeading from '@/layouts/dashboard/DashboardHeading';
import Overview from '../Dashboard/Overview/Overview';
import { FC, ReactNode } from 'react';
import GetPaidModal from './components/GetPaidModal';
import DepositModal from './components/DepositModal';
import NoData from './components/NoData';
import WithdrawalModal from './components/WithdrawalModal';

interface PaymentUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: { payments: Payment[] };
}

interface PaymentProps {
  onGetPaid?: boolean;
  title?: ReactNode;
}

const Payments: FC<PaymentProps> = ({ onGetPaid }) => {
  const { loading, payments } = useGetPayments({
    select: ({ isLoading, isUninitialized, data }: PaymentUtils) => ({
      loading: isLoading || isUninitialized,
      payments: data?.payments || [],
    }),
  });

  const tabs = [
    { id: 'paid-bills', label: 'dashboard.paidBill' },
    { id: 'schedule-bills', label: 'dashboard.scheduleBill' },
  ];
  const getPaidTabs = [
    { id: 'payments', label: 'dashboard.payments' },
    { id: 'request', label: 'dashboard.request' },
  ];

  return (
    <Skeleton active={false}>
      <DashboardContent>
        <DashboardHeading className="mb-5">
          <Translate msgId="dashboard.wallet" />
        </DashboardHeading>
        <DashboardGrid
          className="grid-flow-row-dense"
          gap="gap-3 md:gap-4"
          columns="grid-cols-1"
        >
          <DashboardGrid
            item
            span="md:col-span-2 col-span-3 md:col-start-1 md:col-end-3"
          >
            <Overview showSavings />
          </DashboardGrid>
        </DashboardGrid>

        <DashboardGrid
          className="my-5"
          gap="gap-3 md:gap-5"
          columns="grid-cols-1"
        >
          <div className=" space-y-5">
            <div className="flex gap-3 flex-col lg:flex-row lg:items-center justify-between">
              <SimpleText className=" text-2xl font-semibold">
                <Translate msgId="dashboard.payments" />
              </SimpleText>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ">
                <DepositModal />
                <WithdrawalModal />
                {onGetPaid ? <GetPaidModal /> : <ScheduleBillModal />}
              </div>
            </div>

            <Card variant="outlined" className="w-full bg-white rounded-lg">
              <CardHeader
                className="bg-white"
                primary={
                  onGetPaid ? (
                    <Translate msgId="dashboard.getPaid" />
                  ) : (
                    <Translate msgId="dashboard.pay" />
                  )
                }
              />
              <CardContent className="bg-white space-y-5" gutterTop>
                <div className="flex flex-col-reverse lg:flex-row items-center gap-5 justify-between w-full">
                  <div className="w-full">
                    {onGetPaid ? (
                      <TabSwitch tabs={getPaidTabs} defaultTab="payments" />
                    ) : (
                      <TabSwitch tabs={tabs} defaultTab="paid-bills" />
                    )}
                  </div>

                  <div className="flex flex-col lg:flex-row gap-5 w-full lg:w-auto">
                    <div className="flex-1  lg:w-80">
                      <SearchBar />
                    </div>
                    <Button
                      variant="light"
                      className="flex gap-x-2 flex-row w-full lg:w-auto border rounded-lg  "
                    >
                      <Collection className=" text-gray-300" />
                      <Translate msgId="dashboard.filter" />
                    </Button>
                  </div>
                </div>

                {loading && (
                  <div className=" grid  grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                    <PaymentLoading loading={loading} />
                    <PaymentLoading loading={loading} />
                    <PaymentLoading loading={loading} />
                    <PaymentLoading loading={loading} />
                    <PaymentLoading loading={loading} />
                    <PaymentLoading loading={loading} />
                    <PaymentLoading loading={loading} />
                    <PaymentLoading loading={loading} />
                  </div>
                )}

                {payments.length > 0 ? (
                  <div className=" grid  grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                    {payments.map((payment: Payment) => (
                      <PaymentItem key={payment.id} payment={payment} />
                    ))}
                  </div>
                ) : (
                  !loading && (
                    <NoData
                      title={
                        onGetPaid
                        ? 'dashboard.collectPaymentFaster'
                          : 'dashboard.makePaymentFaster'
                      }
                      description={
                        onGetPaid
                        ? 'dashboard.collectPayNoCode'
                          : 'dashboard.makePayNoCode'
                      }
                    >
                      {onGetPaid ? <GetPaidModal /> : <ScheduleBillModal />}
                    </NoData>
                  )
                )}
              </CardContent>
            </Card>
          </div>
        </DashboardGrid>
      </DashboardContent>
    </Skeleton>
  );
};

export default Payments;
