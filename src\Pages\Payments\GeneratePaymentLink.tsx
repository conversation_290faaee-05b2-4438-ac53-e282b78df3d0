import { TabSwitch } from '@/components/TabSwitch';
import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import LinkGeneratedModal from '@/features/Payments/components/LinkGeneratedModal';
import GeneralLinkForm from '@/features/Payments/components/forms/GeneralLinkForm';
import SpecificUserLinkForm from '@/features/Payments/components/forms/SpecificUserLinkForm';
import { ArrowLeft } from 'heroicons-react';
import { ChangeEvent, useState } from 'react';
import { Link } from 'react-router-dom';

const GeneratePaymentLink = () => {
  const [isComplete, setIsComplete] = useState(false);
  const [file, setFile] = useState<string | null>(null);
  const [currentTab, setCurrentTab] = useState<string>('general-link');

  const handleSelectFile = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files && e.target.files[0];
    if (file) setFile(URL.createObjectURL(file));
  };

  const tabs = [
    { id: 'general-link', label: 'dashboard.generalLink' },
    { id: 'specific-user', label: 'dashboard.specificUser' },
  ];

  return (
    <div className="max-w-7xl mx-auto space-y-10 justify-center p-5 h-screen w-screen">
      <Button
        component={Link}
        variant="transparent"
        to="/select-payment-link-plan"
        className="!p-0 lg:mt-10 text-center w-fit flex justify-between items-center gap-5 rounded-md mb-2"
      >
        <ArrowLeft />
        <Translate msgId="dashboard.back" />
      </Button>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 lg:gap-20 ">
        <div className="h-full ">
          <SimpleText component="h1" className="font-semibold text-2xl mb-5">
            <Translate msgId="dashboard.paymentDetails" />
          </SimpleText>
          <div className="mb-5">
            <TabSwitch
              tabs={tabs}
              defaultTab="general-link"
              setCurrentTab={setCurrentTab}
            />
          </div>
          {currentTab === 'general-link' && <GeneralLinkForm />}
          {currentTab === 'specific-user' && <SpecificUserLinkForm />}
        </div>

        <label
          htmlFor="upload-invoice"
          className=" lg:min-h-[80vh] cursor-pointer order-first lg:order-last flex items-center justify-center flex-col border-[3px] rounded-md border-dotted bg-gray-50 h-full"
        >
          {file ? (
            <img src={file} className="w-full h-full object-cover" />
          ) : (
            <div className="p-5 lg:p-10 flex flex-col items-center justify-center">
              <img
                src="/images/cloud-upload.png"
                alt="upload"
                className=" w-14 h-14"
              />
              <SimpleText component="h1" className="mt-1 text-lg">
                <Translate msgId="dashboard.uploadProductImage" />
              </SimpleText>
              <SimpleText
                component="h1"
                className="mt-1 text-sm text-raven-green-800 cursor-pointer underline"
              >
                <Translate msgId="dashboard.browse" />
              </SimpleText>
            </div>
          )}
          <input
            type="file"
            hidden
            id="upload-invoice"
            onChange={handleSelectFile}
          />
        </label>
      </div>
      <LinkGeneratedModal open={isComplete} setOpen={setIsComplete} />
    </div>
  );
};

export default GeneratePaymentLink;
