import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Button } from 'components/button';
import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import backArrow from '/icons/back-arrow.svg';
import { reset } from '@/features/Signup/SignupSlice';

const BackButton = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  return (
    <div>
      <Button
        className="flex gap-3 items-center px-10 py-3 dark:text-white"
        variant="transparent"
        title={t('general.goBack')}
        onClick={() => {
          dispatch(reset());
          navigate(-1);
        }}
      >
        <div>
          <img className="w-10" src={backArrow} alt="back" />
        </div>
        <SimpleText className="text-2xl">
          <Translate msgId="general.back" />
        </SimpleText>
      </Button>
    </div>
  );
};

export default BackButton;
