import { Avatar } from '@/components/avatar'
import { Button } from '@/components/button'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'
import { FC } from 'react'

interface CardItemProps {
  name: string;
  number: number;
  logo: string
}
const CardItem: FC<CardItemProps> = ({name, number, logo}) => {
  return (
    <div className="mb-3 form-group flex-col flex gap-5 lg:flex-row items-center w-full py-5 border-b-2">
      <div className=" lg:w-[30%] self-start  flex gap-x-5">
        <Avatar size="xl" radius="full" src={logo || '/images/profile.png'} alt="Profie Photo" />
        <div>
          <SimpleText component="h1" className="text-gray-300 cursor-pointer">
            {name}
          </SimpleText>
          <SimpleText component="h1" className="cursor-pointer">
            {number}
          </SimpleText>
        </div>
      </div>
      <div className=" flex justify-end gap-5 w-full lg:w-[478px] ">
        <Button variant="light" className="self-end !py-2  transition-all border rounded-lg  !text-gray-300">
          <Translate msgId="dashboard.update" />
        </Button>
        <Button variant="light" className=" !py-2  transition-all border rounded-lg  !text-gray-300">
          <Translate msgId="dashboard.remove" />
        </Button>
      </div>
    </div>
  )
}

export default CardItem