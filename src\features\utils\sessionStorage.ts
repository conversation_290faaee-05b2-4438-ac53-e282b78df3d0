export const getSessionStorageItem = (item: string) => {
    if (!sessionStorage.getItem(item)) return null

    const value = sessionStorage.getItem(item) || '{}'
    if (typeof value === 'string') return value
    return JSON.parse(value)
}

export const setSessionStorageItem = (name: string, value: string | { [key: string]: any }) => {
    sessionStorage.setItem(name, JSON.stringify(value))
}

export const removeSessionStorageItem = (name: string) => {
    sessionStorage.removeItem(name)
}