import { defineConfig } from 'vite';
import path from 'path';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src/'),
      components: `${path.resolve(__dirname, './src/components')}`,
      features: `${path.resolve(__dirname, './src/features')}`,
      assets: `${path.resolve(__dirname, './src/assets')}`,
      Pages: `${path.resolve(__dirname, './src/Pages')}`
    }
  },
  server: {
    host: true
  }
});
