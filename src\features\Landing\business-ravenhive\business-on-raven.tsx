import { But<PERSON> } from '@/components/button';
import { Translate } from 'components/translate';
import { Link } from 'react-router-dom';
import { SimpleText } from '@/components/typography';

export default function BusinessOnRaven() {
  return (
    <div className="flex justify-between items-center gap-x-10 md:pb-20 md:px-10">
      <div className="w-[300px]">
        <SimpleText
          component="h2"
          className="font-semibold text-3xl md:text-5xl mb-10"
        >
          <Translate msgId="home.businessOnRaven" />
        </SimpleText>
        <SimpleText component="p">
          <Translate msgId="home.businessOnRavenMsg" />
        </SimpleText>

        <Button
          variant="darkPrimary"
          className="text-white mt-10"
          type="button"
          component={Link}
          to="/"
        >
          <Translate msgId="home.joinNow" />
        </Button>
      </div>
      <video
        src="/videos/payment-hero.mp4"
        className="w-[45%] h-[500px]"
        controls
      />
    </div>
  );
}
