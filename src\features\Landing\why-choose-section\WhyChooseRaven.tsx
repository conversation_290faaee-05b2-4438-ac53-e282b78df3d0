import FeatureCard from '@/components/cards/FeatureCard';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import reasons from '@/data/reasons.json';
import { TextSecondary } from '@/components/typography/Text';
import { Trans } from 'react-i18next';

const WhyChooseRaven = () => {
  return (
    <div className="mt-20 md:py-20 md:px-10">
      <div className="mb-10 space-y-4 text-center">
        <SimpleText
          component="h2"
          className="font-semibold text-3xl md:text-5xl"
        >
          <Trans
            i18nKey="home.sectionTitle5"
            components={{
              style: <span className="text-raven-green-800 leading-tight" />,
            }}
          />
        </SimpleText>
        <TextSecondary size="lg" shade="light">
          <Translate msgId="home.sectionDesc5" />
        </TextSecondary>
      </div>
      <div className="grid gap-3 md:gap-10 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {reasons.map((feature, ind) => (
          <FeatureCard
            key={ind}
            title={feature.title}
            description={feature.description}
            name={feature.name}
            icon={feature.icon}
            noButton={feature.noButton}
            color={feature.color}
          />
        ))}
      </div>
    </div>
  );
};

export default WhyChooseRaven;
