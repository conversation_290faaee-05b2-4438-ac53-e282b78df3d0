import { Button } from "@/components/button"
import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"
import CardItem from "@/features/Settings/components/CardItem"
import useGetAccounts from "@/features/Settings/hooks/useGetAccounts"
import { Account as AccountType } from "@/models/settings-types"

interface AccountsUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: { accounts: AccountType[] };
}
const Accounts = () => {
  const { accounts } = useGetAccounts({
    select: ({ isLoading, isUninitialized, data }: AccountsUtils) => ({
      loading: isLoading || isUninitialized,
      accounts: data?.accounts || [],
    }),
  });

  const cards = accounts.filter((account: AccountType) => account.type === 'card');
  const otherCards = accounts.filter((account: AccountType) => account.type === 'others');
  const bankCards = accounts.filter((account: AccountType) => account.type === 'bank');

  return (
    <div>
      <div className=" mt-10">
        <div className=" py-5 border-b-2 flex w-full flex-col lg:flex-row gap-5 lg:items-center lg:justify-between">
          <SimpleText component="h1" className="text-xl">
            <Translate msgId="dashboard.bankCard" />
          </SimpleText>
          <div className="group w-full lg:w-auto">
            <Button variant="light" className=" w-full transition-all border rounded-lg  !text-gray-800 group-hover:bg-gray-900 group-hover:!text-white">
              + <Translate msgId="dashboard.addAccount" />
            </Button>
          </div>
        </div>

        {bankCards.map((card:AccountType) => (
          <CardItem key={card.id} name={card.name} number={card.number} logo={card.logo}/>
        ))}
      </div>
      
      <div className=" mt-10">
        <div className=" py-5 border-b-2 flex w-full flex-col lg:flex-row gap-5 lg:items-center lg:justify-between">
          <SimpleText component="h1" className="text-xl">
            <Translate msgId="dashboard.card" />
          </SimpleText>
        </div>

        {cards.map((card: AccountType) => (
          <CardItem key={card.id} name={card.name} number={card.number} logo={card.logo}/>
        ))}
      </div>
      
      <div className=" mt-10">
        <div className=" py-5 border-b-2 flex w-full flex-col lg:flex-row gap-5 lg:items-center lg:justify-between">
          <SimpleText component="h1" className="text-xl">
            <Translate msgId="dashboard.otherAccounts" />
          </SimpleText>
        </div>

        {otherCards.map((card: AccountType) => (
          <CardItem key={card.id} name={card.name} number={card.number} logo={card.logo}/>
        ))}
      </div>

      <div className="flex w-full flex-col lg:flex-row gap-5 items-center justify-between mt-5">
        <Button variant="darkPrimary" className=" w-full lg:w-[250px] rounded-lg">
          <Translate msgId="dashboard.updateAccount" />
        </Button>
        <div className="group w-full lg:w-[200px]">
          <Button variant="light" className=" w-full transition-all rounded-lg border-raven-green-800 !text-raven-green-800 group-hover:bg-raven-green-800 group-hover:!text-white">
            <Translate msgId="dashboard.cancel" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Accounts

