import { useEffect, useRef } from 'react';
import { annotate } from 'rough-notation';
import { Trans } from 'react-i18next';

const AnnotatedText = () => {
  const elementRef = useRef(null);

  useEffect(() => {
    if (elementRef.current) {
      const annotation = annotate(elementRef.current, {
        type: 'circle',
        color: 'yellow',
        padding: 10,
        iterations: 1
      });
      annotation.show();
    }
  }, []);
  return (
    <Trans
      i18nKey="home.heroTitleLast"
      components={{
        style2: (
          <span
            ref={elementRef}
            className="text-raven-green-800 leading-tight -z-50"
          />
        )
      }}
    />
  );
};

export default AnnotatedText;
