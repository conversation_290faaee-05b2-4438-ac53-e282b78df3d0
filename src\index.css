@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Poppins font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
* {
  scroll-behavior: smooth;
}
/* Color variables */
:root {
  --transparent-black: rgba(0, 0, 0, 0.1);
  --transparent-blacker: rgba(0, 0, 0, 0.2);
  --raven-green: #11e342;
  --raven-green-dark-light: #128542;
  --raven-green-dark: #324740;
  --raven-black: #191919;
}

body {
  font-family: 'Poppins', sans-serif;
  padding: 0;
  overflow-x: hidden;
  @apply text-raven-dark-900 dark:bg-gray-900 dark:text-white;
}

ul {
  list-style: none;
}

.pricing {
  @apply text-white bg-black dark:text-white;
}

footer.footer {
  background-color: var(--raven-black);
}

.footer-circle {
  border-radius: 100%;
  background: rgba(17, 227, 66, 0.1);
  width: 34.4375rem;
  height: 36.375rem;
  filter: blur(98.4903335571289px);
}

a {
  color: inherit; /* Reset color */
  text-decoration: none; /* Reset underline */
  background: none; /* Reset background */
  border: none; /* Reset border */
  margin: 0; /* Reset margin */
  padding: 0; /* Reset padding */
  font: inherit; /* Reset font */
  vertical-align: baseline; /* Reset vertical align */
  outline: none; /* Reset outline */
}

.dark-blur,
.z-5,
.no-card-cover {
  z-index: 5;
}

.z-full {
  z-index: 999;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type='number'] {
  appearance: texfield;
  -moz-appearance: textfield;
}

.btn:not(.btn-transparent) {
  @apply py-3;
}

.btn-primary {
  @apply lg:px-10 px-4 bg-raven-green-500 text-white dark:bg-raven-green-800;
}

.btn-darkPrimary {
  @apply px-10 bg-raven-green-800 text-white dark:bg-raven-green-800;
}

.btn-light {
  @apply px-10 bg-white text-gray-900 border rounded-lg;
}
.btn-light:disabled {
  @apply text-gray-400;
}
.btn-dark {
  @apply px-10 bg-gray-800 text-white dark:bg-gray-700 dark:border-gray-300;
}

.btn-transparent {
  @apply bg-transparent px-10;
}
/* just for support documentation link */
.btn-transparent.support_docs {
  padding-right: 0 !important;
  padding-left: 0 !important;
  width: auto !important;
  text-align: center;
}

.btn-gold {
  @apply px-10 bg-[#FFDA93] dark:bg-[#E3C539] dark:text-black;
}

.btn-tiny {
  @apply px-2.5 py-1.5 text-xs leading-4 rounded;
}

.card {
  box-shadow: 0px 4px 70px 0px rgba(0, 0, 0, 0.25);
}

.body,
nav {
  padding: 0 15rem;
}

input.peer:checked + label .radio-border,
.radio-ring {
  border-color: var(--raven-green-dark-light);
}
input.peer:checked + label .radio-button,
.radio-dot {
  background-color: var(--raven-green-dark-light);
}
input.peer:checked + label .radio-text {
  color: var(--raven-green-dark-light);
}

/* For inputs on error */
.error-field,
.error-field:focus {
  background-color: ivory;
  border: none;
  outline: 2px solid red;
  border-radius: 5px;
}

/* Error boundary styles */
/* error-image-overlay */
.error-image-overlay {
  height: 60vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

/* error-image */
.error-image {
  display: inline-block;
  width: 150px;
  height: 150px;
}

/* error-image background image */
.error-image::before {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
}

/* ErrorImageText */
.ErrorImageText {
  margin-top: 30px;
}

.form-input,
.country-button {
  padding: 0.8rem;
  border-radius: 0.625rem;
  background: #f8f8f8;
  border-color: #eee;
}

.featured-partners {
  background: linear-gradient(
    180deg,
    rgba(235, 255, 251, 0.2) 0%,
    rgba(236, 255, 251, 0.2) 100%
  );
  box-shadow: 0px 4px 70px 0px var(--transparent-black);
  backdrop-filter: blur(8px);
}

/* hiding scrollbar */
/* For Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
  scrollbar-width: none; /* For Firefox */
}

.atm-card-rotated {
  width: 30.85588rem;
  height: 18.5135rem;
  transform: rotate(26.747deg);
}

.card-blurred {
  flex-shrink: 0;
  border: 1.078px solid rgba(255, 255, 255, 0.6);
  box-shadow: 43.10986px 21.55493px 30.45706px 0px var(--transparent-blacker);
  backdrop-filter: blur(10.777463912963867px);
}

.atm-card-rotated.light-green-card {
  background: linear-gradient(
    108deg,
    var(--raven-green) 2.44%,
    var(--raven-green-dark) 98.72%
  );
}

.atm-card-rotated.dark-green-card {
  background: linear-gradient(
    108deg,
    #191919 3.59%,
    rgba(255, 255, 255, 0.2) 98.72%
  );
}

.signup-page {
  color: #191919;
  @apply dark:text-white;
}

.h-screen-hack {
  height: 100vh;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 60% 40%;
}

.grid-item > div:first-child:not(.border-none),
.overview-item {
  border: 0.975px solid rgba(196, 196, 196, 0.2);
}

.overview-icon-container {
  background-color: #128542;
}

/* For mobile and below */
@media (max-width: 768px) {
  .body,
  nav {
    padding: 1rem;
  }
}

/* For tablets to desktops */
@media (min-width: 768.5px) and (max-width: 1540px) {
  .body,
  nav {
    padding: 0 5rem;
  }
}

.last-section-bg {
  border-radius: 1.875rem;
  background: radial-gradient(
    72.35% 61.51% at 60.63% 39.42%,
    #191919 55.54%,
    #143e26 100%
  );
  background-blend-mode: multiply;
}

.light-gray {
  background-color: rgba(196, 196, 196, 0.3);
  opacity: 0.7;
}

.light-red {
  background-color: #ff7576;
  opacity: 0.7;
}

.light-blue {
  background-color: #80e0e5;
  opacity: 0.7;
}

.light-gold {
  background-color: #e3c053;
  opacity: 0.6;
}

/* Dark theme */
.bg-white {
  @apply dark:bg-gray-800 dark:text-white;
}

.bg-slate-50,
.bg-gray-100 {
  @apply dark:bg-gray-900;
}

.bg-raven-green-50 {
  @apply dark:bg-black;
}

.text-gray-900,
.text-gray-700 {
  @apply dark:text-white;
}

.text-gray-500 {
  @apply dark:text-gray-200;
}
.border-t,
.border-b {
  @apply dark:border-gray-700 !important;
}

.border {
  @apply dark:border-gray-700;
}

.raven-outline {
  @apply outline-2 outline outline-gray-100  hover:outline-raven-green-800 dark:outline-gray-700;
}

.divide-gray-200 {
  @apply dark:divide-gray-600;
}

/* loading spinner */
div.spinner-container {
  height: calc(100vh - 10px);
}

div.spinner {
  position: relative;
  width: 100%;
  height: 100%;
  display: inline-block;
  margin: auto;
  padding: 10px;
  border-radius: 10px;
}

div.spinner div {
  width: 3px;
  height: 10px;
  position: absolute;
  left: 49%;
  top: 49%;
  opacity: 0;
  border-radius: 50px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  animation: fade 1s linear infinite;
  -webkit-border-radius: 50px;
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  -webkit-animation: fade 1s linear infinite;
  @apply bg-gray-500 dark:bg-gray-300;
}

@keyframes fade {
  from {
    opacity: 1;
  }
  to {
    opacity: 0.25;
  }
}
@-webkit-keyframes fade {
  from {
    opacity: 1;
  }
  to {
    opacity: 0.25;
  }
}

div.spinner div.bar1 {
  transform: rotate(0deg) translate(0, -130%);
  animation-delay: 0s;
  -webkit-transform: rotate(0deg) translate(0, -130%);
  -webkit-animation-delay: 0s;
}

div.spinner div.bar2 {
  transform: rotate(30deg) translate(0, -130%);
  animation-delay: -0.9167s;
  -webkit-transform: rotate(30deg) translate(0, -130%);
  -webkit-animation-delay: -0.9167s;
}

div.spinner div.bar3 {
  -webkit-transform: rotate(60deg) translate(0, -130%);
  transform: rotate(60deg) translate(0, -130%);
  -webkit-animation-delay: -0.833s;
  animation-delay: -0.833s;
}
div.spinner div.bar4 {
  -webkit-transform: rotate(90deg) translate(0, -130%);
  transform: rotate(90deg) translate(0, -130%);
  -webkit-animation-delay: -0.7497s;
  animation-delay: -0.7497s;
}
div.spinner div.bar5 {
  -webkit-transform: rotate(120deg) translate(0, -130%);
  transform: rotate(120deg) translate(0, -130%);
  -webkit-animation-delay: -0.667s;
  animation-delay: -0.667s;
}
div.spinner div.bar6 {
  -webkit-transform: rotate(150deg) translate(0, -130%);
  transform: rotate(150deg) translate(0, -130%);
  -webkit-animation-delay: -0.5837s;
  animation-delay: -0.5837s;
}
div.spinner div.bar7 {
  -webkit-transform: rotate(180deg) translate(0, -130%);
  transform: rotate(180deg) translate(0, -130%);
  -webkit-animation-delay: -0.5s;
  animation-delay: -0.5s;
}
div.spinner div.bar8 {
  -webkit-transform: rotate(210deg) translate(0, -130%);
  transform: rotate(210deg) translate(0, -130%);
  -webkit-animation-delay: -0.4167s;
  animation-delay: -0.4167s;
}
div.spinner div.bar9 {
  -webkit-transform: rotate(240deg) translate(0, -130%);
  transform: rotate(240deg) translate(0, -130%);
  -webkit-animation-delay: -0.333s;
  animation-delay: -0.333s;
}
div.spinner div.bar10 {
  -webkit-transform: rotate(270deg) translate(0, -130%);
  transform: rotate(270deg) translate(0, -130%);
  -webkit-animation-delay: -0.2497s;
  animation-delay: -0.2497s;
}
div.spinner div.bar11 {
  -webkit-transform: rotate(300deg) translate(0, -130%);
  transform: rotate(300deg) translate(0, -130%);
  -webkit-animation-delay: -0.167s;
  animation-delay: -0.167s;
}
div.spinner div.bar12 {
  -webkit-transform: rotate(330deg) translate(0, -130%);
  transform: rotate(330deg) translate(0, -130%);
  -webkit-animation-delay: -0.0833s;
  animation-delay: -0.0833s;
}
