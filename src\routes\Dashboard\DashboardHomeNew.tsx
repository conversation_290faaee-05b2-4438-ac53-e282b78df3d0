import RavenCardTwo from '@/components/atm-card/RavenCardTwo';
import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
// import { Translate } from '@/components/translate';
import EarningOverviewChart from '@/features/Dashboard/Chart/EarningOverviewChart';
import OverviewA from '@/features/Dashboard/Overview/OverviewA';
import RecentTransactions from '@/features/Dashboard/RecentTransactions/RecentTransactions';
import QuickPayments from '@/features/Payments/QuickPayments';
import formatCurrencyUtil from '@/features/utils/formatCurrency';
import { DashboardContent, DashboardGrid } from '@/layouts/dashboard';
// import DashboardHeading from '@/layouts/dashboard/DashboardHeading';
import { Skeleton } from 'components/skeleton';
import { ChevronLeft, ChevronRight, DotsVertical, Plus, Refresh } from 'heroicons-react';
import { Link } from 'react-router-dom';
import mtnLogo from '/images/mtn.png';

const PaymentPreview = () => {
  return (
    <div className="bg-white py-5">
      <div className="mb-5 px-5 ">
        <SimpleText component="h3" className="font-semibold">
          <Translate msgId="dashboard.paymentMethods" />
        </SimpleText>
      </div>

      <div className=" flex justify-between items-center w-full">
        <ChevronLeft width={40} height={40} />
        <div className=" flex-1">
          <Skeleton active={false} className="mb-5">
            <div className="mb-5 w-full relative">
              <RavenCardTwo />
            </div>
          </Skeleton>

        </div>
        <ChevronRight width={40} height={40} />
      </div>
      <div className="px-5 w-full">

        <div className="flex items-center justify-center gap-2 mb-3 w-full shadow-sm bg-[#F3FEF6CC] rounded-md p-1">
          <div className="flex items-center gap-2 w-full">
            <div className="h-10 w-10 rounded-full">
              <img src={mtnLogo} className=' bg-contain' />
            </div>
            <div >
              <h1>MTN Mobile Money</h1>
              <p className="text-gray-400 text-sm">+************ 00</p>
            </div>
          </div>
          <DotsVertical className='mx-auto' />
        </div>
        <Button
          component={Link}
          to="/dashboard/payments/payment-methods"
          variant="darkPrimary"
          className="rounded-lg w-full inline-block text-center text-sm"
        >
          <Translate msgId="dashboard.addPayment" />
        </Button>
      </div>
    </div>
  )
}
const DashboardHome = () => {
  return (
    <Skeleton active={false}>
      <DashboardContent>
        {/* <DashboardHeading className="mb-5">
          <Translate msgId="dashboard.dashboard" />
        </DashboardHeading> */}
        <DashboardGrid
          className="grid-flow-row-dense"
          gap="gap-3 md:gap-4"
          columns="grid-cols-3"
        >
          <DashboardGrid
            item
            span="md:col-span-2 col-span-3 md:col-start-1 md:col-end-3"
          >
            <dl className="className={`grid lg:grid-cols-3">
              <h1 className="text-gray-400 lg:ml-10 text-sm font-bold">Hi, Joseph</h1>
              <div className="flex gap-3 items-center">
                <h1 className='text-3xl font-bold text-black'>
                  {formatCurrencyUtil(11901.00)}
                </h1>
                <Refresh width={20} className='text-raven-green-800' />
              </div>
              <div className="flex justify-between mt-20 items-center mb-3">
                <h1 className='text-gray-400'>Total Balance as at today</h1>
                <div className="flex items-center gap-2">
                  <div className=" rounded-md w-6 h-6 flex items-center justify-center bg-gray-400">
                    <Plus color='white' width={20} />
                  </div>
                  <Button variant='dark' className='rounded-md text-xs !p-1.5 !px-2'>Add funds</Button>
                </div>
              </div>
            </dl>

            <OverviewA />
          </DashboardGrid>
          <DashboardGrid
            item
            span="md:col-span-1 col-span-3"
            className="md:row-start-1 md:row-end-3 md:col-start-3 md:col-end-4 space-y-5"
          >
            <PaymentPreview />
            <QuickPayments />
          </DashboardGrid>
          <DashboardGrid
            item
            span="md:col-span-2 col-span-3 md:col-start-1 md:col-end-3"
            className="rounded"
          >
            <EarningOverviewChart />
          </DashboardGrid>
          {/* <DashboardGrid
            item
            span="md:col-span-1 col-span-3 md:col-start-3 md:col-end-4"
          >
            <QuickPayments />
          </DashboardGrid> */}
          <DashboardGrid
            item
            span="md:col-span-2 col-span-3 md:col-start-1 md:col-end-3"
          >
            <RecentTransactions />
          </DashboardGrid>
        </DashboardGrid>
      </DashboardContent>
    </Skeleton>
  );
};

export default DashboardHome;
