import { useMemo, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { push } from 'connected-react-router';

interface Options {
    redirect: boolean
}

const useRequireSession = (options: Options) => {
    const {
        redirect = true,
    } = options;

    const { session, required, forbidden, authorized } = JSON.parse(sessionStorage.getItem('session')!)

    const dispatch = useDispatch();


    useEffect(() => {
        if (session) {
            return;
        }

        if (redirect) {
            dispatch(push(`/singin?redirect=${window?.location?.pathname ?? ''}`));
        }
    }, [dispatch, redirect, session]);

    const loading = useMemo(() => {
        return authorized && !session;
    }, [
        session,
        authorized,
    ]);

    return useMemo(() => ([
        loading,
        required,
        forbidden,
    ]), [
        loading,
        required,
        forbidden
    ]);
};

export default useRequireSession;
