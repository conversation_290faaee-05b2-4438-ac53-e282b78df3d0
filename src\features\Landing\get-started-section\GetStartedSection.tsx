import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import phoneStraight from '/images/phone-transfer.png';
import phoneSlant from '/images/phone-transactions.png';
import steps from '@/data/starting-steps.json';
import { CheckCircle } from 'heroicons-react';
import { Button } from '@/components/button';
import { Link } from 'react-router-dom';
import { Trans } from 'react-i18next';

const GetStartedSection = () => {
  return (
    <div className="mt-20 py-20 bg-[#D3F1DA] dark:bg-green-900">
      <div className="body">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
          <div className="space-y-10">
            <div>
              <SimpleText component="h2" className="font-semibold text-4xl">
                <Trans
                  i18nKey="home.startedInMinutues"
                  components={{
                    style1: (
                      <span className="text-raven-green-800 leading-tight" />
                    ),
                  }}
                />
              </SimpleText>
            </div>
            <div>
              <SimpleText>
                <Translate msgId="home.createAndStart" />
              </SimpleText>
            </div>
            <div>
              <ul className="space-y-4">
                {steps.map((step, index) => (
                  <li key={index} className="flex gap-4">
                    <div className="shrink-0">
                      <CheckCircle className="h-5 w-5 mt-1 text-raven-green-800" />
                    </div>
                    <div>
                      <SimpleText>
                        <Translate msgId={step.step} />
                      </SimpleText>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
            <div className="w-full py-5">
              <Button
                variant="darkPrimary"
                className="rounded"
                component={Link}
                to="/signup"
              >
                <Translate msgId="home.createNow" />
              </Button>
            </div>
          </div>
          <div className="relative">
            <div className="flex items-center justify-center md:justify-end">
              <div className="relative z-10">
                <img
                  src={phoneStraight}
                  alt="phone showing transfer successful"
                />
              </div>
              <div className="absolute z-0 bottom-10 right-36">
                <img src={phoneSlant} alt="slanted phone design" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GetStartedSection;
