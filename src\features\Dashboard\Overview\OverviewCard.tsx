import { Translate } from '@/components/translate';
import clsx from '@/features/utils/clsx';
import { Skeleton } from 'components/skeleton';
import { FC } from 'react';

interface OverviewCardProps {
  loading: boolean;
  name: string;
  stat: string | number;
  className?: string;
  primaryTextClass?: string;
  secondaryTextClass?: string;
  icon?: string;
  iconAlt?: string;
}
const OverviewCard: FC<OverviewCardProps> = ({
  loading,
  name,
  stat = '-',
  className = 'bg-white',
  primaryTextClass = 'text-gray-900',
  secondaryTextClass = 'text-gray-500',
  icon,
  iconAlt,
}) => {
  return (
    <Skeleton active={loading}>
      <div
        className={clsx(
          'px-4 py-5 rounded-lg overflow-hidden sm:p-6 cursor-default overview-item',
          className
        )}
      >
        {icon && (
          <div className="flex w-10 h-10 rounded-full overview-icon-container justify-center items-center mb-4">
            <img className="w-5" src={icon} alt={iconAlt} />
          </div>
        )}
        <dt
          className={clsx(
            'mt-1 mb-2 text-sm font-medium truncate',
            secondaryTextClass
          )}
        >
          <Translate msgId={name} />
        </dt>
        <dd
          title={`${stat}`}
          className={clsx('text-2xl font-semibold truncate', primaryTextClass)}
        >
          {stat}
        </dd>
      </div>
    </Skeleton>
  );
};

export default OverviewCard;
