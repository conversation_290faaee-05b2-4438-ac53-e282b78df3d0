import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"

const Preferences = () => {
  // const payload = sessionStorage.getItem('@payload')
  // const user = JSON.parse(payload as string)
  
  return (
    <div>
        <div className="mb-3 form-group sm:block lg:flex w-full py-5">
          <SimpleText className=" text-lg lg:w-[40%]">
            <Translate msgId="dashboard.paymentChannels" />
          </SimpleText>
          <div className="w-full space-y-2">
            <div className="flex w-full gap-5 items-center">
              <input
                className=" !rounded-md !p-3 dark:bg-gray-800 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="checkbox"
                value={''}
              />
              <SimpleText className="text-gray-300 text-lg ">
                <Translate msgId="dashboard.allowCardPayment" />
              </SimpleText>
            </div>
            <div className="flex w-full gap-5 items-center">
              <input
                className=" !rounded-md !p-3 dark:bg-gray-800 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="checkbox"
                value={''}
              />
              <SimpleText className="text-gray-300 text-lg ">
                <Translate msgId="dashboard.allowBankPayment" />
              </SimpleText>
            </div>
            <div className="flex w-full gap-5 items-center">
              <input
                className=" !rounded-md !p-3 dark:bg-gray-800 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="checkbox"
                value={''}
              />
              <SimpleText className="text-gray-300 text-lg ">
                <Translate msgId="dashboard.allowLocalPayment" />
              </SimpleText>
            </div>

          </div>
        </div>
        
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5">
          <SimpleText className=" text-lg lg:w-[40%]">
            <Translate msgId="dashboard.emailReceipt" />
          </SimpleText>
          <div className="flex w-full gap-5 items-center">
            <input
              className=" !rounded-md !p-3 dark:bg-gray-800 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="checkbox"
              value={''}
            />
            <SimpleText className="text-gray-300 text-lg ">
              <Translate msgId="dashboard.emailReceiptToCustomers" />
            </SimpleText>
          </div>
        </div>
        
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5">
          <SimpleText className=" text-lg lg:w-[40%]">
            <Translate msgId="dashboard.transactionEmails" />
          </SimpleText>
          <div className="flex w-full gap-5 items-center">
            <input
              className=" !rounded-md !p-3 dark:bg-gray-800 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="checkbox"
              value={''}
            />
            <SimpleText className="text-gray-300 text-lg ">
              <Translate msgId="dashboard.emailMe" />
            </SimpleText>
          </div>
        </div>
        
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5">
          <SimpleText className=" text-lg lg:w-[40%]">
            <Translate msgId="dashboard.transferEmails" />
          </SimpleText>
          <div className="flex w-full gap-5 items-center">
            <input
              className=" !rounded-md !p-3 dark:bg-gray-800 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="checkbox"
              value={''}
            />
            <SimpleText className="text-gray-300 text-lg ">
              <Translate msgId="dashboard.emailMe" />
            </SimpleText>
          </div>
        </div>
        
        
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5">
          <SimpleText className=" text-lg lg:w-[40%]">
            <Translate msgId="dashboard.transferEmailToReceipient" />
          </SimpleText>
          <div className="flex w-full gap-5 items-center">
            <input
              className=" !rounded-md !p-3 dark:bg-gray-800 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="checkbox"
              value={''}
            />
            <SimpleText className="text-gray-300 text-lg ">
              <Translate msgId="dashboard.sendEmailNotification" />
            </SimpleText>
          </div>
        </div>
        
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5">
          <SimpleText className=" text-lg lg:w-[40%]">
            <Translate msgId="dashboard.internationalPayments" />
          </SimpleText>
          <div className="flex w-full gap-5 items-center">
            <input
              className=" !rounded-md !p-3 dark:bg-gray-800 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="checkbox"
              value={''}
            />
            <SimpleText className="text-gray-300 text-lg ">
              <Translate msgId="dashboard.emailMe" />
            </SimpleText>
          </div>
        </div>
        
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5">
          <SimpleText className=" text-lg lg:w-[40%]">
            <Translate msgId="dashboard.paymentSchedule" />
          </SimpleText>
          <div className="flex w-full gap-2 items-center">
          
            <SimpleText className="text-gray-300 text-lg ">
              <Translate msgId="dashboard.mannualSettlement" />
            </SimpleText>
            <SimpleText className="text-raven-green-800 text-lg ">
              <Translate msgId="dashboard.change" />
            </SimpleText>
          </div>
        </div>
        
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5">
          <SimpleText className=" text-lg lg:w-[40%]">
            <Translate msgId="dashboard.transferConfermation" />
          </SimpleText>
          <div className="flex w-full gap-2 items-center">
            <SimpleText className="text-gray-300 text-lg ">
              <Translate msgId="dashboard.otpConfirmation" />
            </SimpleText>
            <SimpleText className="text-raven-green-800 text-lg ">
              <Translate msgId="dashboard.enable" />
            </SimpleText>
          </div>
        </div>
        

    </div>
  )
}

export default Preferences