import Cookies from 'js-cookie';

// Set a cookie
export const setToken = (token: string): void => {
    const secureFlag: boolean = process.env.NODE_ENV === 'production';
    Cookies.set('token', token, { expires: 7, secure: secureFlag });
};

// Get token from cookie
export const getToken = (): string | undefined => {
    return Cookies.get('token') ?? '';
};

// set a cookie
export const setCookie = (name: string, value: string): void => {
    const secureFlag: boolean = process.env.NODE_ENV === 'production';
    Cookies.set(name, value, { expires: 7, secure: secureFlag });
}

// Get a cookie
export const getCookie = (cookie: string): string | undefined => {
    return Cookies.get(cookie) ?? '';
};

// Remove a cookie
export const removeCookie = (cookie: string): void => {
    Cookies.remove(cookie);
};

// Remove token cookie
export const removeToken = (): void => {
    Cookies.remove('token');
};

