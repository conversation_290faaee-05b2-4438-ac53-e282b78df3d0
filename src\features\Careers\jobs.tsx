import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { XCircle } from 'heroicons-react';
export type jobSelected = {
  position: string;
  status: string;
  location: string;
};
interface jobsProps {
  onCloseJobs: () => void;
  //eslint-disable-next-line no-unused-vars
  handleApply: (value: jobSelected) => void;
}
export default function Jobs({ onCloseJobs, handleApply }: jobsProps) {
  return (
    <div className="bg-white p-5 pt-0  rounded-lg w-full md:h-[550px] h-[calc(100svh-40px)] overflow-y-scroll ">
      {/* fading edge */}
      <div className="sticky top-0 bg-white pt-2 flex items-center justify-between">
        <SimpleText className="mb-2 text-xl ">
          <Translate msgId="careers.applications" />
        </SimpleText>
        <XCircle
          className="text-red-400 border-2 border-gray-800 p-1 rounded-full w-8 h-8"
          onClick={onCloseJobs}
        />
      </div>

      <div>
        <SimpleText className="text-raven-green-800 mb-1">
          <Translate msgId="careers.engineering" />
        </SimpleText>
        {Array.from({ length: 3 }).map((_, index) => (
          <div className="bg-[#f8f8f8] flex md:flex-row flex-col gap-x-6 border-2 rounded-lg dark:bg-gray-700 dark:border-gray-600 p-2 mb-4">
            <div className="flex gap-x-6">
              <div className="md:min-w-[300px]">
                <SimpleText className=" text-gray-400">
                  <Translate msgId="careers.position" />
                </SimpleText>
                <SimpleText className=" ">
                  <Translate
                    msgId={`careers.${
                      index == 1 ? 'dataAnalyst' : 'softwareLead'
                    }`}
                  />
                </SimpleText>
              </div>
              <div className="">
                <SimpleText className=" text-gray-400 ">
                  <Translate msgId="careers.status" />
                </SimpleText>
                <SimpleText className="">
                  <Translate msgId="careers.onsite" />
                </SimpleText>
              </div>
              <div>
                <SimpleText className=" text-gray-400 ">
                  <Translate msgId="careers.location" />
                </SimpleText>
                <SimpleText className="">
                  <Translate
                    msgId={`careers.${index === 0 ? 'accra' : 'sfo'}`}
                  />
                </SimpleText>
              </div>
            </div>
            <Button
              variant="primary"
              className="rounded-lg"
              onClick={() =>
                handleApply({
                  position:
                    index == 1
                      ? 'Data Analyst Engineering Lead'
                      : 'Software Engineering Lead',
                  status: 'Onsite',
                  location: index == 0 ? 'Accra' : 'S F O'
                } as jobSelected)
              }
            >
              <Translate msgId="careers.apply" />
            </Button>
          </div>
        ))}
      </div>
      <div>
        <SimpleText className="text-raven-green-800 mb-1">
          <Translate msgId="careers.sales" />
        </SimpleText>
        {Array.from({ length: 2 }).map((_, index) => (
          <div className="bg-[#f8f8f8] flex md:flex-row flex-col gap-x-6 border-2 rounded-lg dark:bg-gray-700 dark:border-gray-600 p-2 mb-4">
            <div className="flex gap-x-6">
              <div className="md:min-w-[300px]">
                <SimpleText className=" text-gray-400">
                  <Translate msgId="careers.position" />
                </SimpleText>
                <SimpleText className=" ">
                  <Translate msgId="careers.customerOnboarding" />
                </SimpleText>
              </div>
              <div className="">
                <SimpleText className=" text-gray-400 ">
                  <Translate msgId="careers.status" />
                </SimpleText>
                <SimpleText className="">
                  <Translate msgId="careers.remote" />
                </SimpleText>
              </div>
              <div>
                <SimpleText className=" text-gray-400 ">
                  <Translate msgId="careers.location" />
                </SimpleText>
                <SimpleText className="">
                  <Translate
                    msgId={`careers.${index == 1 ? 'london' : 'accra'}`}
                  />
                </SimpleText>
              </div>
            </div>
            <Button
              variant="primary"
              className="rounded-lg"
              onClick={() =>
                handleApply({
                  position: 'Customer Onboarding Manager',
                  status: 'Remote',
                  location: index == 0 ? 'Accra' : 'London'
                } as jobSelected)
              }
            >
              <Translate msgId="careers.apply" />
            </Button>
          </div>
        ))}
      </div>
      <div>
        <SimpleText className="text-raven-green-800 mb-1">
          <Translate msgId="careers.legal" />
        </SimpleText>
        <div className="bg-[#f8f8f8] flex md:flex-row flex-col gap-x-6 border-2 rounded-lg dark:bg-gray-700 dark:border-gray-600 p-2 mb-4">
          <div className="flex gap-x-6">
            <div className="md:min-w-[300px]">
              <SimpleText className=" text-gray-400">
                <Translate msgId="careers.position" />
              </SimpleText>
              <SimpleText className=" ">
                <Translate msgId="careers.legalService" />
              </SimpleText>
            </div>
            <div className="">
              <SimpleText className=" text-gray-400 ">
                <Translate msgId="careers.status" />
              </SimpleText>
              <SimpleText className="">
                <Translate msgId="careers.hybrid" />
              </SimpleText>
            </div>
            <div>
              <SimpleText className=" text-gray-400 ">
                <Translate msgId="careers.location" />
              </SimpleText>
              <SimpleText className="">
                <Translate msgId="careers.london" />
              </SimpleText>
            </div>
          </div>
          <Button
            variant="primary"
            className="rounded-lg"
            onClick={() =>
              handleApply({
                position: 'Legal Service',
                status: 'Onsite',
                location: 'London'
              } as jobSelected)
            }
          >
            <Translate msgId="careers.apply" />
          </Button>
        </div>
      </div>
    </div>
  );
}
