import { Button } from '@/components/button';
import { Modal } from '@/components/modal';
import { Translate } from '@/components/translate';
import { SimpleText, Text } from '@/components/typography';
import clsx from '@/features/utils/clsx';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import masterCard from '/icons/mastercard-logo.png';
import momoIcon from '/icons/momo-icon.png';
import ravenLogo from '/images/logo-header.png';
import visa from '/icons/visa-vector.svg';
import bank from '/icons/merchant-account.png';
import { PaymentOptionsType } from '@/models/payment-option';
import { useDispatch, useSelector } from 'react-redux';
import { getOption, setOption } from '../paymentsSlice';
import CountrySelector from '@/components/selector/CountrySelector';
import { COUNTRIES } from '@/data/countries';
import { SelectMenuOption } from '@/data/types';
import SelectedDepositForm from './SelectedDepositForm';
import { ArrowSmDown } from 'heroicons-react';

// type FormValues = {
//   cardNumber: string
//   cardType: string
//   cvv: number
//   expiredDate: string
// }

type FormValues = {
  phone: string;
  fullName: string;
  amount: number;
  cardType: string;
};
interface PaymentOptionType {
  name: string;
  value: PaymentOptionsType;
  icons: string[];
  id: number;
}

const paymentOptions: PaymentOptionType[] = [
  {
    id: 1,
    name: 'dashboard.card',
    value: 'card',
    icons: [masterCard, visa, ravenLogo],
  },
  {
    id: 2,
    name: 'dashboard.bankPayment',
    value: 'bank_payment',
    icons: [bank],
  },
  {
    id: 3,
    name: 'dashboard.momo',
    value: 'mobile_money',
    icons: [momoIcon],
  },
];

const DepositModal = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>();

  const dispatch = useDispatch();
  const selectedOption = useSelector(getOption);

  const [isOpen, setIsOpen] = useState(false);

  const [open, setOpen] = useState(false);

  const handleDeposit = handleSubmit((data) => {
    // Handle deposit
    console.log(data);
  });

  const [country, setCountry] = useState('GH');

  const handleChange = (value: string) => {
    dispatch(setOption(value as PaymentOptionsType));
  };

  useEffect(() => {
    dispatch(setOption('mobile_money'));
  }, [dispatch]);

  return (
    <>
      <Button
        variant="light"
        className="w-full flex items-center lg:w-auto rounded-lg"
        onClick={() => setIsOpen(true)}
      >
        <ArrowSmDown />
        <Translate msgId="dashboard.deposit" />
      </Button>
      <Modal
        primary={<Translate msgId="dashboard.depositMoney" />}
        className=" lg:w-96"
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      >
        <div className="p-2 space-y-5">
          <div className="flex gap-5">
            <div className=" w-full ">
              <SimpleText className=" text-gray-300 ">
                <Translate msgId="dashboard.amount" />
              </SimpleText>
              <input
                className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="number"
                placeholder="GHS 000.00"
                {...register('amount', { required: 'amount is required' })}
              />
              {errors.amount && (
                <SimpleText className=" text-xs text-red-500">
                  <p>{errors.amount?.message}</p>
                </SimpleText>
              )}
            </div>
            <div className=" w-full">
              <SimpleText className=" text-gray-300 ">
                <Translate msgId="dashboard.currency" />
              </SimpleText>
              <CountrySelector
                id={'countries'}
                open={open}
                onToggle={() => setOpen(!open)}
                onChange={(event) => setCountry(event)}
                selectedValue={
                  COUNTRIES.find(
                    (option) => option.value === country
                  ) as SelectMenuOption
                }
              />
            </div>
          </div>
          <div className=" w-full ">
            <SimpleText className=" text-gray-300 ">
              <Translate msgId="dashboard.paymentMethod" />
            </SimpleText>

            <div className="grid grid-cols-3 gap-3">
              {paymentOptions.map((option) => (
                <div
                  className={clsx('border-2 p-3 rounded-md min-h-20', {
                    ' border-raven-green-800': selectedOption == option.value,
                  })}
                  onClick={() => handleChange(option.value)}
                >
                  {option.icons.map((icon) => (
                    <div className="inline-flex justify-between pr-1">
                      <img
                        key={icon}
                        className={clsx({
                          'xl:h-3 h-2': option.value === 'card',
                          'xl:w-5 w-3': option.value !== 'card',
                        })}
                        src={icon}
                        alt={`${option.name} icon`}
                      />
                    </div>
                  ))}
                  <Text size="sm" shade="dark" className="">
                    <Translate msgId={option.name} />
                  </Text>
                </div>
              ))}
            </div>
          </div>
          <SelectedDepositForm />

          <Button
            variant="darkPrimary"
            className="w-full rounded-md mb-2"
            onClick={handleDeposit}
          >
            <Translate msgId="dashboard.deposit" />
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default DepositModal;
