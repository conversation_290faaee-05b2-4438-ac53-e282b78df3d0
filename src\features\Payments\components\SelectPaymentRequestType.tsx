import { But<PERSON> } from "@/components/button"
import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"
import { ChevronRight } from "heroicons-react"
import { Dispatch, FC, SetStateAction } from "react";

interface SelectPaymentRequestTypeProps {
  requestType: string;
  setRequestType: Dispatch<SetStateAction<string>>;
}

const SelectPaymentRequestType:FC<SelectPaymentRequestTypeProps> = ({ requestType, setRequestType }) => {

  const handleSelectRequestType = (type: string)=> setRequestType(type);

  return (
    <div className=" space-y-5">
      <div className=' flex items-center justify-center flex-col'>
        <SimpleText component='h1' className="text-lg text-center leading-6 font-medium text-gray-900">
          <Translate msgId='dashboard.howTo' />
        </SimpleText>
        <SimpleText component='h1' className="mt-1 text-sm text-center text-gray-500">
          <Translate msgId='dashboard.selectHowTo' />
        </SimpleText>
      </div>

      <Button variant="light" className={`${requestType === 'payment-link' ? ' border-raven-green-800 !border-[3px]': ''} py-2 !px-3 flex items-center text-left justify-between rounded-md`} onClick={()=> handleSelectRequestType('payment-link')}>
        <div>
          <SimpleText component="h1" className=" text-base">
            <Translate msgId='dashboard.generatePaymentLink' />
          </SimpleText>
          <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
            <Translate msgId='dashboard.sendMoneyTo' />
          </SimpleText>
        </div>
        <ChevronRight className=' h-8 w-8' />
      </Button>

      <Button variant="light" className={`${requestType === 'payment-request' ? ' border-raven-green-800 !border-[3px]': ''} py-2 !px-3 text-left flex items-center justify-between rounded-md`} onClick={()=> handleSelectRequestType('payment-request')}>
        <div>
          <SimpleText component="h1" className=" text-base">
            <Translate msgId='dashboard.paymentRequest' />
          </SimpleText>
          <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
            <Translate msgId='dashboard.sendMoneyTo' />
          </SimpleText>
        </div>
        <ChevronRight className=' h-8 w-8' />
      </Button>
    </div>
  )
}

export default SelectPaymentRequestType