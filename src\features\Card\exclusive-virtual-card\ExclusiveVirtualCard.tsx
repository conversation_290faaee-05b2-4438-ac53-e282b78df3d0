import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import VitualCardPhone from '/images/virtual-card-phone.png';
import FeatureItem from '../components/FeatureItem';
import ex1 from '/images/ex1.png';
import ex2 from '/images/ex2.png';
import ex3 from '/images/ex3.png';

const ExclusiveVirtualCard = () => {
  return (
    <section className="py-10 p-5">
      <div className="lg:pt-28 flex flex-col items-center justify-center lg:container mx-auto space-y-5 ">
        <SimpleText
          component="h1"
          className="text-gray-400 leading-8  text-center  font-extrabold "
        >
          <Translate msgId="card.ravenCards" />
        </SimpleText>
        <SimpleText className="lg:w-4/5 text-center font-semibold text-raven-green-800 text-4xl lg:text-5xl ">
          <Translate msgId="card.exclusiveTitle" />
        </SimpleText>
        <img
          src={VitualCardPhone}
          alt="Phone showing raven app"
          className="shrink-0 w-[300px] object-contain"
        />
      </div>

      <div className="grid md:grid-cols-2 grid-cols-1 lg:grid-cols-3 gap-5 py-10">
        <FeatureItem
          title="card.payWithPhone"
          icon={ex1}
          descriptoin="card.payWithPhoneDesc"
        />
        <FeatureItem
          title="card.digitalService"
          icon={ex2}
          descriptoin="card.digitalServiceDesc"
        />
        <FeatureItem
          title="card.yourActivities"
          icon={ex3}
          descriptoin="card.yourActivitiesDesc"
        />
      </div>
    </section>
  );
};

export default ExclusiveVirtualCard;
