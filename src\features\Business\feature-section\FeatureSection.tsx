import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import CheckoutCard from '../components/CheckoutCard';
import PayoutCard from '../components/PayoutCard';
import InvoiceCard from '../components/InvoiceCard';

const FeatureSection = () => {
  return (
    <div className="mt-20 md:py-20 md:px-10 gap-5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      <div>
        <div className="h-[320px] rounded-md bg-business-page-feature-item bg-cover bg-center p-10">
          <CheckoutCard />
        </div>
        <div className="mt-5">
          <SimpleText component="h3" className="text-raven-green-800 text-xl">
            <Translate msgId={'business.onlineCheckout'} />
          </SimpleText>
          <SimpleText className="font-light">
            <Translate msgId={'business.onlineCheckoutDesc'} />
          </SimpleText>
        </div>
      </div>
      <div>
        <div className="h-[320px] flex items-center justify-center ">
          <div className="h-[250px] w-full bg-business-page-feature-item bg-cover bg-center rounded-md">
            <PayoutCard />
          </div>
        </div>

        <div className="mt-5">
          <SimpleText component="h3" className="text-raven-green-800 text-xl">
            <Translate msgId={'business.payout'} />
          </SimpleText>
          <SimpleText className="font-light">
            <Translate msgId={'business.payoutDesc'} />
          </SimpleText>
        </div>
      </div>
      <div>
        <div className="h-[320px] flex items-center justify-center ">
          <div className=" h-[250px] w-full bg-business-page-feature-item bg-cover bg-center rounded-md px-10">
            <InvoiceCard />
          </div>
        </div>

        <div className="mt-5">
          <SimpleText component="h3" className="text-raven-green-800 text-xl">
            <Translate msgId={'business.realTimeAccounting'} />
          </SimpleText>
          <SimpleText className="font-light">
            <Translate msgId={'business.realTimeAccountingDesc'} />
          </SimpleText>
        </div>
      </div>
    </div>
  );
};

export default FeatureSection;
