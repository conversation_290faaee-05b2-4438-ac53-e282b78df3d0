import { Avatar } from '@/components/avatar'
import { Switch } from '@/components/switch';
import { SimpleText } from '@/components/typography'
import { FC, useState } from 'react';

interface IntegrationItemProps {
  id: string;
  link: string;
  name: string;
  isActive: boolean;
  logo: string;
  description: string;
}

const IntegrationItem: FC<IntegrationItemProps> = ({id, link, logo, name, isActive, description}) => {
  const [isChecked, setIsChecked] = useState(isActive);

  return (
    <div className="border rounded-md">
      <div className="p-3 space-y-2">
        <div className="flex justify-between items-center">
          <Avatar size="md" radius="full" src={logo} alt={name} />
          <SimpleText component="h1" className=" text-gray-300 text-sm flex gap-2 cursor-pointer">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
              <path strokeLinecap="round" strokeLinejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
            </svg>
            {link}
          </SimpleText>
        </div>
        <SimpleText component="h1" className="font-semibold text-lg py-1">
          {name}
        </SimpleText>
        <SimpleText component="h1" className=" text-gray-300 text-sm">
          {description}
        </SimpleText>
      </div>

      <div className="flex justify-between items-center border-t p-3">
        <div className="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="text-gray-300 w-4 h-4">
            <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75" />
          </svg>

          <SimpleText component="h1" className=" text-gray-300 text-sm">
            Manage
          </SimpleText>
        </div>
        <Switch id={id} checked={isChecked} onChange={()=> setIsChecked(!isChecked)} />
      </div>
    </div>
  )
}

export default IntegrationItem