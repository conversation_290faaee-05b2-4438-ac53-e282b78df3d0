import { createSelector, createSlice } from "@reduxjs/toolkit";
import { RootState } from "@/store/store";
import { Transaction } from "@/models/transactions";

interface InitialState {
    transactions: Transaction[];
}

const initialState: InitialState = {
    transactions: []
}

export const transactionSlice = createSlice({
    name: 'transaction',
    initialState,
    reducers: {
        setTransactions: (state, action: { type: string, payload: Transaction[]})=>{
            state.transactions = action.payload
        },
        setRefunds: (state, action: { type: string, payload: Transaction[]})=>{
            state.transactions = action.payload
        }
    }
})

const { actions, name } = transactionSlice;

export const { setTransactions, setRefunds } = actions

const getSlice = (state: RootState) => state[name]

export const getTransactions = createSelector(getSlice, (slice) => slice.transactions || []);