import { ElementType, FC } from 'react';
import ravenLogo from '/images/logo-header.png';
import { Link } from 'react-router-dom';

interface FixedRouteComponentProps {
  [key: string]: any;
  component: ElementType;
}

const FixedRouteComponent: FC<FixedRouteComponentProps> = ({
  component: Component,
  ...props
}) => {
  return (
    <div className="min-h-screen max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="min-h-screen max-w-xl mx-auto flex flex-col">
        <div className="flex justify-center pt-10">
          <Link to="/">
            <img
              src={ravenLogo}
              alt="raven logo"
              title="Click to go home"
              className="w-100"
            />
          </Link>
        </div>
        <div className="pt-20">
          <Component {...props} />
        </div>
      </div>
    </div>
  );
};

export default FixedRouteComponent;
