import React, { ReactNode, useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface ModalProps {
  isOpen: boolean;
  onClose?: () => void;
  className?: string;
  children: ReactNode;
  primary?: ReactNode;
  secondary?: ReactNode;
  title?: ReactNode;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  children,
  className,
  primary,
  secondary,
}) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const closeModal = () => {
    setIsModalOpen(false);
    onClose && onClose();
  };

  useEffect(() => {
    setIsModalOpen(isOpen);
  }, [isOpen, onClose]);

  return (
    <>
      {isModalOpen && (
        <motion.div
          className="fixed inset-0 overflow-hidden top-0 left-0 right-0 bottom-0 z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center justify-center w-screen h-screen overflow-hidden lg:px-4 lg:py-6 py-4 text-center">
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
            >
              <div className="absolute z-10 inset-0  bg-gray-900 bg-opacity-20 backdrop-blur-md transition-all duration-500"></div>
            </div>
            <motion.div
              className={` z-20 pb-5 min-h-[50%] overflow-hidden max-h-[90%] inline-block align-center bg-white rounded-lg text-left  shadow-xl transform transition-all duration-500 sm:align-center min-w-96 max-w-4xl ${className}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className=" p-4 flex justify-between items-center mx-3">
                <div>
                  <h3 className="text-xl leading-6 font-semibold text-gray-900">
                    {primary}
                  </h3>
                  {secondary && (
                    <span className="mt-1 text-sm text-gray-500">
                      {secondary}
                    </span>
                  )}
                </div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-6 h-6 cursor-pointer"
                  onClick={closeModal}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 18 18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <div className=" max-h-[550px] overflow-auto bg-white px-4 pb-4 sm:px-6 sm:pb-4">
                {children}
              </div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </>
  );
};

export default Modal;
