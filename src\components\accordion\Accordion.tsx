import AccordionItem from './AccordionItem';
import { FC, ReactNode } from 'react';

type DataItem = {
  title: string;
  content: ReactNode | string;
};

interface AccordionProps {
  data: DataItem[];
  itemClass?: string;
}

const Accordion: FC<AccordionProps> = ({ data, itemClass }) => {
  return (
    <div>
      {data.map((item, index) => (
        <AccordionItem
          key={index}
          className={itemClass}
          title={item.title}
          content={item.content}
        />
      ))}
    </div>
  );
};

export default Accordion;
