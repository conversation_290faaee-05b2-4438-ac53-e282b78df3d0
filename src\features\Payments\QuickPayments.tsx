import { Button } from '@/components/button';
import { SimpleText } from 'components/typography';
import { Translate } from '@/components/translate';
import flash from '/icons/yellow-lightning.png';

const QuickPayments = () => {
  return (
    // TODO pull payment information from server
    <div className="bg-white pb-5">
      <div className="mb-5 p-5">
        <SimpleText component="h3" className="font-semibold">
          <Translate msgId="dashboard.quickPayments" />
        </SimpleText>
      </div>
      <div className="mb-3 flex justify-center p-5">
        <div className="text-center">
          <div className="flex justify-center mb-3">
            <img src={flash} alt="thunderbolt" className="self-start" />
          </div>
          <div className="mb-4">
            <SimpleText>
              <Translate msgId="dashboard.noData" />
            </SimpleText>
          </div>
          <div>
            <SimpleText className="text-gray-300">
              <Translate msgId="dashboard.paysDisplayedHere" />
            </SimpleText>
          </div>
        </div>
      </div>
      <div className="px-16 w-full">
        <Button variant="dark" className="w-full rounded-lg">
          <SimpleText className="text-sm">
            <Translate msgId="dashboard.sendMoney" />
          </SimpleText>
        </Button>
      </div>
    </div>
  );
};

export default QuickPayments;
