
interface DoConversionOptions {
    rates: { [key: string]: number } | null;
    base: string;
    receiving: string;
}

export const doConversion = (options: DoConversionOptions) => {

    const { rates, base, receiving } = options

    if (!rates) return null

    // get the rate in dollar for both base and receiving currencies
    const dollarToBase = rates[base]
    const dollarToReceiving = rates[receiving]

    console.log(dollarToReceiving, dollarToBase, 'jkhguguig')

    const baseToReceived = dollarToReceiving / dollarToBase

    return baseToReceived

}