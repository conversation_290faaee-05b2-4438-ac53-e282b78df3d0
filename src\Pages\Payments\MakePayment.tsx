import { Translate } from '@/components/translate';
import { SimpleText, Text } from '@/components/typography';
import LinkGeneratedModal from '@/features/Payments/components/LinkGeneratedModal';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { PaymentOptionsType } from '@/models/payment-option';
import { COUNTRIES } from '@/data/countries';
import CountrySelector from '@/components/selector/CountrySelector';
import { SelectMenuOption } from '@/data/types';
import formatCurrencyUtil from '@/features/utils/formatCurrency';
import masterCard from '/icons/mastercard-logo.png';
import momoIcon from '/icons/momo-icon.png';
import ravenLogo from '/images/logo-header.png';
import visa from '/icons/visa-vector.svg';
import bank from '/icons/merchant-account.png';
import clsx from '@/features/utils/clsx';
import { useDispatch, useSelector } from 'react-redux';
import { getOption, setOption } from '@/features/Payments/paymentsSlice';
import SelectedPaymentForm from '@/features/Payments/components/SelectedPaymentForm';
import { LockClosed } from 'heroicons-react';
import { Button } from '@/components/button';
import { Link } from 'react-router-dom';

type FormValues = {
  phone: string;
  fullName: string;
  amount: number;
};
interface PaymentOptionType {
  name: string;
  value: PaymentOptionsType;
  icons: string[];
  id: number;
}

const paymentOptions: PaymentOptionType[] = [
  {
    id: 1,
    name: 'dashboard.card',
    value: 'card',
    icons: [masterCard, visa, ravenLogo],
  },
  {
    id: 2,
    name: 'dashboard.bankPayment',
    value: 'bank_payment',
    icons: [bank],
  },
  {
    id: 3,
    name: 'dashboard.momo',
    value: 'mobile_money',
    icons: [momoIcon],
  },
];

const MakePayment = () => {
  const dispatch = useDispatch();
  const selectedOption = useSelector(getOption);

  const {
    register,
    formState: { errors },
  } = useForm<FormValues>();

  const [country, setCountry] = useState('GH');
  const [isOpen, setIsOpen] = useState(false);

  const [isComplete, setIsComplete] = useState(false);

  const handleChange = (value: string) => {
    dispatch(setOption(value as PaymentOptionsType));
  };

  useEffect(() => {
    dispatch(setOption('mobile_money'));
  }, [dispatch]);

  return (
    <div className="max-w-6xl mx-auto space-y-8 justify-center p-5 h-screen w-screen">
      <div className="flex justify-end">
        <img
          src="/images/logo-header.png"
          alt="upload"
          className=" object-contain h-10 w-20"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 lg:gap-20 ">
        <div className="h-full">
          <SimpleText className=" text-gray-300 ">
            <Translate msgId="dashboard.amountToPay" />
          </SimpleText>
          <SimpleText component="h1" className="font-semibold text-4xl mb-5">
            {formatCurrencyUtil(99)}
          </SimpleText>

          <div className="space-y-5">
            <div className="flex gap-5">
              <div className=" w-full ">
                <SimpleText className=" text-gray-300 ">
                  <Translate msgId="dashboard.amount" />
                </SimpleText>
                <input
                  className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                  type="text"
                  placeholder="GHS 000.00"
                  {...register('amount', { required: 'amount is required' })}
                />
                {errors.amount && (
                  <SimpleText className=" text-xs text-red-500">
                    <p>{errors.amount?.message}</p>
                  </SimpleText>
                )}
              </div>
              <div className=" w-full ">
                <SimpleText className=" text-gray-300 ">
                  <Translate msgId="dashboard.currency" />
                </SimpleText>
                <CountrySelector
                  id={'countries'}
                  open={isOpen}
                  onToggle={() => setIsOpen(!isOpen)}
                  onChange={(event) => setCountry(event)}
                  selectedValue={
                    COUNTRIES.find(
                      (option) => option.value === country
                    ) as SelectMenuOption
                  }
                />
              </div>
            </div>

            <img
              src="/images/shoe.png"
              alt="upload"
              className=" object-contain"
            />
          </div>
        </div>

        <div>
          <div className="space-y-5 cursor-pointer p-5 border-[2px] rounded-md  bg-gray-50 ">
            <SimpleText component="h1" className="font-semibold text-2xl mb-5">
              <Translate msgId="dashboard.paymentDetails" />
            </SimpleText>

            <div className=" w-full ">
              <SimpleText className=" text-gray-300 ">
                <Translate msgId="dashboard.paymentMethod" />
              </SimpleText>

              <div className="grid grid-cols-3 gap-3">
                {paymentOptions.map((option) => (
                  <div
                    className={clsx('border-2 p-3 cursor-pointer rounded-md min-h-20', {
                      ' border-raven-green-800': selectedOption == option.value,
                    })}
                    onClick={() => handleChange(option.value)}
                  >
                    {option.icons.map((icon) => (
                      <div className="inline-flex justify-between pr-1">
                        <img
                          key={icon}
                          className={clsx({
                            'xl:h-3 h-2': option.value === 'card',
                            'xl:w-5 w-3': option.value !== 'card',
                          })}
                          src={icon}
                          alt={`${option.name} icon`}
                        />
                      </div>
                    ))}
                    <Text size="sm" shade="dark" className="">
                      <Translate msgId={option.name} />
                    </Text>
                  </div>
                ))}
              </div>
            </div>
            {<SelectedPaymentForm />}
          </div>
          <SimpleText className="flex gap-2 justify-center text-gray-300 py-5 text-center">
            <LockClosed className="h-6 w-6 text-gray-500" />
            <Translate msgId="dashboard.poweredBy" />
            <Button
              variant="transparent"
              component={Link}
              className="!px-0 text-raven-green-800 font-bold "
            >
              <Translate msgId="dashboard.raven" />
            </Button>
          </SimpleText>
        </div>
      </div>
      <LinkGeneratedModal open={isComplete} setOpen={setIsComplete} />
    </div>
  );
};

export default MakePayment;
