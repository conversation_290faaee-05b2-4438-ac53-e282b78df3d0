import { FC } from 'react';
import { Skeleton } from 'components/skeleton';
import { TableRow, TableColumn } from 'components/Table';
import { TextSecondary } from 'components/typography/Text';
import { DotsVertical } from 'heroicons-react';
interface UserItemProps {
  id?: number;
  status?: string;
  joined?: string;
  name?: string;
  email?: string;
  role?: string;
  avatar?: string;
  loading?: boolean;
}

const UserItem: FC<UserItemProps> = ({
  joined,
  status,
  role,
  name,
  email,
  loading = false,
}) => (
  <TableRow nowrap>
    <TableColumn>
      <Skeleton active={loading}>
        <div className="flex gap-3">
          <TextSecondary size="sm" shade="dark">
            {loading ? 'Name' : <input type='checkbox' className=' checked:border-gray-500 checked:bg-gray-500 border border-gray-500 rounded-md' />}
          </TextSecondary>
          <TextSecondary size="sm" shade="dark">
            {loading ? 'Name' : name}
          </TextSecondary>
        </div>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="light" transform="capitalize">
          {loading ? 'Email' : email}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="dark" transform='capitalize'>
          {loading ? 'Status' : status}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn>
      <Skeleton active={loading}>
        <TextSecondary size="sm" shade="light" transform="capitalize">
          {loading ? 'Role' : role}
        </TextSecondary>
      </Skeleton>
    </TableColumn>
    <TableColumn justify='end' align='end' >
      <Skeleton active={loading}>
        <div className="flex gap-3">
          <TextSecondary size="sm" shade="dark">
            {loading ? 'Name' : joined}
          </TextSecondary>
          <TextSecondary size="sm" shade="dark">
            <DotsVertical />
          </TextSecondary>
        </div>
      </Skeleton>
    </TableColumn>
  </TableRow>
);

export default UserItem;
