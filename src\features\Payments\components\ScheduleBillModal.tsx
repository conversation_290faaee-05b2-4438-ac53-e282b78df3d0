import { Button } from '@/components/button'
import { Modal } from '@/components/modal'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'
import  { useState } from 'react'

const ScheduleBillModal = () => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div>
      <div>
        <Button variant="darkPrimary" className="w-full lg:w-auto rounded-lg" onClick={()=>setIsOpen(true)}>
          + <Translate msgId="dashboard.scheduleBill" />
        </Button>
      </div>
      <Modal className=' w-96' isOpen={isOpen} onClose={()=>setIsOpen(false)}>
        <div className="p-2 space-y-5">
          <SimpleText component="h1" className=" text-center text-sm lg:text-lg ">
            <Translate msgId="dashboard.scheduleBill" />
          </SimpleText>
          <Button variant='darkPrimary' className='w-full rounded-md mb-2'>
            <Translate msgId='dashboard.next' />
          </Button>
        </div>
      </Modal>
    </div>
  )
}

export default ScheduleBillModal