import { z } from 'zod';

export const signupSchema = z.object({
  firstName: z
    .string()
    .min(1, `'auth.noFirstName'`)
    .min(2, 'auth.shortName')
    .max(50, 'auth.longName'),
    
  otherNames: z
    .string()
    .max(70, 'auth.longName')
    .optional()
    .or(z.literal('')), // Allow empty string
    
  lastName: z
    .string()
    .min(1, 'auth.noLastName')
    .min(2, 'auth.shortName')
    .max(50, 'auth.longName'),
    
  email: z
    .string()
    .min(1, 'auth.enterCompanyEmail')
    .email('auth.enterValidEmail'),
    
  phoneNumber: z
    .string()
    .regex(
      /^(?:\+?1\s?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/,
      'auth.enterValidPhone'
    ),
    
  country: z
    .string()
    .min(1, 'auth.selectCountry'),
    
  password: z
    .string()
    .min(1, 'auth.enterPassword')
    .min(8, 'auth.shortPassword')
    .max(50, 'auth.longPassword')
    .regex(/\d/, 'auth.passwordPatternNumber')     // At least one number
    .regex(/[A-Z]/, 'auth.passwordPatternUppercase'), // At least one uppercase letter
    
  confirmPassword: z
    .string()
    .min(1, 'auth.enterPasswordConfirm')
}).refine((data) => {
  return data.password === data.confirmPassword;
}, {
  message: 'auth.noMatchPassword',
  path: ['confirmPassword'], // Error shows on confirmPassword field
});

export type SignupFormData = z.infer<typeof signupSchema>;
