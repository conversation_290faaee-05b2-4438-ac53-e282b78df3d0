import { useMemo, useState, useEffect } from 'react';

type Sizes = 'sm' | 'md' | 'lg' | 'xl' | '2xl'

const screens = {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
};

const useMediaQuery = (size: Sizes) => {
    const mediaQueryString = useMemo(
        () => `(min-width: ${screens[size]})`,
        [size]
    );
    const mediaQuery = useMemo(() => window.matchMedia(mediaQueryString), [
        mediaQueryString,
    ]);
    const [matches, setMatches] = useState(mediaQuery.matches);

    useEffect(() => {
        // eslint-disable-next-line no-unused-vars
        const onMediaQueryChange = (event: { matches: boolean | ((prevState: boolean) => boolean);[key: string]: any }) => {
            if (matches !== event.matches) {
                setMatches(event.matches);
            }
        };

        mediaQuery.addListener(onMediaQueryChange);

        if (matches !== mediaQuery.matches) {
            setMatches(mediaQuery.matches);
        }

        return () => {
            mediaQuery.removeListener(onMediaQueryChange);
        };
    }, [matches, mediaQuery]);

    return matches;
};

export default useMediaQuery;
