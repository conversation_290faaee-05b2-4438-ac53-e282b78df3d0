import { SimpleText } from '@/components/typography';
import { Translate, LinkText } from '@/components/translate';
import { Trans } from 'react-i18next';
import { Button } from '@/components/button';
import ActionButtons from './actionButtons';
import { FormLabel } from '@/components/form';
export default function ContactForm() {
  return (
    <div className="relative md:mr-6">
      <section className="absolute -top-12 md:-top-6 md:-right-16 -right-6">
        <ActionButtons />
      </section>
      <form className="flex flex-col gap-8 md:mt-5">
        <div className="text-center flex flex-col gap-y-3">
          <SimpleText component="h2" className="text-2xl">
            <Translate msgId="support.sendMessage" />
          </SimpleText>
          <p>
            <SimpleText>
              <Translate msgId="support.hearFromYou" />
            </SimpleText>
            <span>
              <a href="mailto:<EMAIL>" className="text-raven-link">
                {' '}
                <SimpleText>
                  <Translate msgId="support.supportMail" />
                </SimpleText>
              </a>
            </span>
          </p>
        </div>
        <div className="flex gap-x-2">
          <section className="flex flex-col w-1/2">
            <FormLabel
              required
              text="auth.firstName"
              name="first_name"
              className="mb-[-4px]"
            />
            <input
              type="text"
              name="first_name"
              id="first_name"
              placeholder="Kofi"
              className="bg-[#c4c4c4] border-0 rounded-lg w-full dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 mt-3 form-control form-input"
              required
            />
          </section>
          <section className="flex flex-col w-1/2">
            <FormLabel
              required
              text="auth.lastName"
              name="last_name"
              className="mb-[-4px]"
            />

            <input
              type="text"
              name="last_name"
              id="last_name"
              placeholder="Obeng"
              className="bg-[#c4c4c4] border-0 rounded-lg  w-full dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 mt-3 form-control form-input"
              required
            />
          </section>
        </div>
        <section className="flex flex-col">
          <FormLabel
            required
            text="auth.email"
            name="email"
            className="mb-[-4px]"
          />
          <input
            type="email"
            name="email"
            id="email"
            className="bg-[#c4c4c4] border-0 rounded-lg h-[45px] dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 mt-3 form-control form-input"
            placeholder="<EMAIL>
                  "
            required
          />
        </section>
        <section className="flex flex-col">
          <FormLabel
            required
            text="auth.message"
            name="message"
            className="mb-[-4px]"
          />
          <textarea
            name="message"
            id="message"
            className="bg-[#c4c4c4] h-[100px] border-0 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 mt-3 resize-none overflow-auto hide-scrollbar form-control form-input"
            required
          ></textarea>
        </section>
        <SimpleText component="small" className="text-center">
          <Trans
            i18nKey="auth.agreeToTerms"
            components={{
              link1: (
                <LinkText to="http://localhost:5173/terms" title="Terms">
                  <Translate msgId="auth.termsOfService" />
                </LinkText>
              ),
              link2: (
                <LinkText
                  to="http://localhost:5173/privacy-policy"
                  title="Privacy Policy"
                >
                  <Translate msgId="home.privacyPolicy" />
                </LinkText>
              )
            }}
          />
        </SimpleText>
        <Button type="submit" variant="primary" className="w-full rounded-lg">
          <SimpleText>
            <Translate msgId="auth.send" />
          </SimpleText>
        </Button>
      </form>
    </div>
  );
}
