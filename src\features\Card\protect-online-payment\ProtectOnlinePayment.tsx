import { Button } from '@/components/button';
import { SimpleText } from '@/components/typography';
import { Translate } from 'components/translate';
import { useState } from 'react';
import { stepsToProtectPayments } from '@/data/card-page';

const ProtectOnlinePayment = () => {
  const [activeTab, setActiveTab] = useState(0)

  return (
    <section>
      <div className="my-24 container mx-auto bg-gray-100 space-y-10 rounded-lg ">
        <div className="flex flex-col md:flex-row items-start gap-5 p-5 md:p-10">
          <div className="flex-1">
            <SimpleText
              component="h1"
              className="text-gray-400 leading-8  font-extrabold "
            >
              <Translate msgId="card.ravenCards" />
            </SimpleText>
            <SimpleText
              className="lg:w-2/3 text-raven-green-800 text-3xl lg:text-4xl font-semibold "
            >
              <Translate msgId="card.protectPayments" />
            </SimpleText>

          </div>
          <Button variant='darkPrimary' className='shadow-md rounded-md'>
            <Translate msgId="card.openAnAccount" />
          </Button>
        </div>
        <div className={`grid grid-flow-row md:grid-flow-col grid-cols-${stepsToProtectPayments.length}`}>
          {stepsToProtectPayments.map((_, index) => (
            <Button onClick={() => setActiveTab(index)} variant={`${activeTab == index ? "darkPrimary" : 'transparent'}`} className="p-4 text-center ">
              <Translate msgId={'card.step' + (index + 1)} />
            </Button>
          ))}
        </div>

        <div className="flex flex-col md:flex-row px-5 lg:px-10">
          <div className="flex-1 p-4 lg:p-7">
            <SimpleText
              component="h1"
              className=" text-lg"
            >
              <Translate msgId={stepsToProtectPayments[activeTab].description} />
            </SimpleText>
          </div>
          <img
            src={stepsToProtectPayments[activeTab].image}
            alt="Phone showing raven app"
            className="shrink-0 object-contain w-full md:w-72"
          />
        </div>
      </div>
    </section>
  );
};

export default ProtectOnlinePayment;
