import { FC } from 'react';
import { Translate } from '@/components/translate';
import clsx from '@/features/utils/clsx';

interface FormLabelProps {
  name: string;
  className?: string;
  text: string;
  required?: boolean;
}
const FormLabel: FC<FormLabelProps> = ({ text, name, required = false, className }) => {
  return (
    <label
      htmlFor={name}
      className={clsx(
        'block mb-4 text-sm font-medium text-gray-500 dark:text-white',
        className
      )}
    >
      <Translate msgId={text} />
      {required && <span className="text-red-500 ml-1"> *</span>}
    </label>
  );
};

export default FormLabel;
