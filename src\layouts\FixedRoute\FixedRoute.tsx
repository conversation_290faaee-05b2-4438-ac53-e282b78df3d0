import { ElementType, FC } from 'react';
import { Route } from 'react-router-dom';
import useAuthorizedRedirect from '@/hooks/useAuthorizedRedirect';
import FixedRouteComponent from './FixedRouteComponent';

interface FixedRouteProps {
  component: ElementType;
  redirectAuthorized: string;
}

const FixedRoute: FC<FixedRouteProps> = ({
  component,
  redirectAuthorized,
  ...rest
}) => {
  useAuthorizedRedirect(redirectAuthorized);

  return (
    <Route
      {...rest}
      Component={(props) => (
        <FixedRouteComponent {...props} component={component} />
      )}
    />
  );
};

export default FixedRoute;
