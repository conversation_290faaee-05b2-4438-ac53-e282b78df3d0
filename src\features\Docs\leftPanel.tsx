import PanelItem from './components/panelItem';

const sectionIds = [
  'Home',
  'Core_API',
  'Payments',
  'Transaction_FX',
  'Payouts',
  'Global_Treasury',
  'Payments_for_Platforms',
  'Lazer',
  'Developer_Tools',
  'Trust_Centre'
];
export default function LeftPanel() {
  return (
    <div className="fixed h-full w-96 border-r-[1px] border-gray-800 pl-20  pr-10 pt-10">
      {sectionIds.map((docSection, index) => (
        <PanelItem key={docSection} index={index} targetId={`/${docSection}`} />
      ))}
    </div>
  );
}
