import clsx from '@/features/utils/clsx';
import { FC, ReactNode } from 'react';

interface CardProps {
  className?: string;
  children: ReactNode;
  dividers?: boolean;
  variant: 'inset' | 'outlined';
}

const Card: FC<CardProps> = ({
  className,
  children,
  dividers = true,
  variant,
}) => {
  const rootClass = clsx(
    'rounded-lg',
    {
      'divide-y divide-gray-200': dividers,
      'bg-white shadow': !variant,
      'bg-gray-50': variant === 'inset',
      'border border-gray-200': variant === 'outlined',
    },
    className
  );

  return <div className={rootClass}>{children}</div>;
};

export default Card;
