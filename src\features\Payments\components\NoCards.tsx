import { Link } from 'react-router-dom';
import RavenCard from '@/components/atm-card/RavenCard';
import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { Text } from '@/components/typography';

const NoCards = () => {
  return (
    <>
      <div className="mb-5 relative">
        <div className="z-0">
          <RavenCard />
        </div>
        <div className="bg-black opacity-80 blur h-52 mb-5 rounded-lg text-white px-6 py-5 overflow-hidden absolute top-0 right-0 w-full dark-blur"></div>
        <div className="flex w-full flex-col justify-center items-center h-full absolute top-0 no-card-cover">
          <div>
            <Text shade="white" size="sm" className="text-center">
              <Translate msgId="dashboard.addCardHere" />
            </Text>
          </div>
          <div className="text-center mt-3">
            <Button
              variant="light"
              component={Link}
              className="inline-block text-sm"
              to="/dashboard/payments/payment-methods"
            >
              <Translate msgId="dashboard.addCard" />
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default NoCards;
