import FeatureCard from '@/components/cards/FeatureCard';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import features from '@/data/features.json';
import { ButtonVariants } from '@/models/button-props.interface';

const Features = () => {
  return (
    <div className="md:py-20 md:px-10">
      <div className="flex items-end justify-between">
        <div className="space-y-6 font-semibold">
          <SimpleText component="small">
            <Translate msgId="home.uniqueFeatures" />
          </SimpleText>
          <SimpleText className="text-4xl text-raven-green-800 md:w-2/3">
            <Translate msgId="home.FeatureTitle" />
          </SimpleText>
        </div>
        <div className="hidden md:block">
          <SimpleText>
            <Translate msgId="home.unparalledAccess" />
          </SimpleText>
        </div>
      </div>
      <div className="grid gap-5 md:gap-10 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mt-10">
        {features.map((feature, ind) => (
          <FeatureCard
            key={ind}
            title={feature.title}
            description={feature.description}
            buttonText={feature.buttonText}
            name={feature.name}
            icon={feature.icon}
            variant={feature.variant as ButtonVariants}
          />
        ))}
      </div>
    </div>
  );
};

export default Features;
