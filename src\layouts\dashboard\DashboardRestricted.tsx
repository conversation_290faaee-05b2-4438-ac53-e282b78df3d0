import DashboardSection from './DashboardSection';
import DashboardBanner from './DashboardBanner';
import { FC, ReactNode } from 'react';

interface DashboardRestrictedProps {
  children: ReactNode;
  actionHref?: string;
  actionTitle?: string;
}

const DashboardRestricted: FC<DashboardRestrictedProps> = ({
  children,
  actionHref = '/dashboard',
  actionTitle = 'Go Home',
}) => {
  return (
    <DashboardSection>
      <DashboardBanner action={actionTitle} href={actionHref}>
        {children ||
          'You do not have the permissions required to access this page.'}
      </DashboardBanner>
    </DashboardSection>
  );
};

export default DashboardRestricted;
