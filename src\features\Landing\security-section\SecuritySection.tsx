import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { Trans } from 'react-i18next';
import SecurityPoint from './SecurityPoint';

const SecuritySection = () => {
  return (
    <div className="md:pb-20 mt-20 md:py-20 md:px-10">
      <div className="flex items-center justify-between flex-wrap gap-4">
        <SimpleText
          component="h2"
          className="font-semibold text-3xl md:text-5xl"
        >
          <Trans
            i18nKey="home.laserSecurity"
            components={{
              style: <span className="text-raven-green-800 leading-tight" />,
            }}
          />
        </SimpleText>
        <div className="md:w-1/3">
          <SimpleText>
            <Translate msgId="home.onTheClock" />
          </SimpleText>
        </div>
      </div>
      <div className="space-y-10">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-10 items-center">
          <div className="space-y-10 mt-10">
            <SecurityPoint
              title="home.securityTitle1"
              description="home.securityDesc1"
            />
          </div>
          <div className="border-l border-gray-500 h-20 m-auto" />
          <div className="space-y-10 mt-10">
            <SecurityPoint
              title="home.securityTitle3"
              description="home.securityDesc3"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-10 items-center">
          <div className="space-y-10 mt-10">
            <SecurityPoint
              title="home.securityTitle2"
              description="home.securityDesc2"
            />
          </div>
          <div className="border-l border-gray-500 h-20 m-auto" />
          <div className="space-y-10 mt-10">
            <SecurityPoint
              title="home.securityTitle4"
              description="home.securityDesc4"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecuritySection;
