import { Button } from "@/components/button"
import { Header } from "@/components/header"
import Footer from "@/features/footer/Footer"

const TermsAndConditions = () => {
  return (
    <div className="">
      <Header />

      <div className="max-w-[1020px] px-10 mx-auto h-screen flex items-center ">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 place-items-center">
          <img
            width={200}
            height={100}
            src={'/images/t&c.png'}
            style={{ objectFit: "contain" }}
            alt="terms and conditions"
            className="  rounded-3xl"
          />

          <div className=" flex-1  py-5">
            <div className="space-y-3">
              <h1 className="font-bold text-5xl">Terms & Conditions</h1>
              <h4 className="text-sm font-light">last updated: 24th June, 2024</h4>
              <p className="text-sm font-light">Join us in 2024 where we gather to explore the expansive world of creat design and the subtle influence of AI. In this year’s event, AI serves as a catalyst, adding a touch of innovation to our discussions, but not overshadowing our primary focus on creative design. Our mission is to ‘Amplify Creativity,’ delving into the art of design, craftsmanship, and the power of human expression. but not overshadowing our primary focus on creative design. Our mission is to ‘Amplify Creativity,’ delving into the art of design, craftsmanship, and the power of human expression. </p>
              <Button variant="darkPrimary" className="rounded">Download now</Button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}

export default TermsAndConditions