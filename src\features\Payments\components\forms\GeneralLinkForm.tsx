import CountrySelector from '@/components/selector/CountrySelector';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { COUNTRIES } from '@/data/countries';
import { SelectMenuOption } from '@/data/types';
import { GeneralLinkFormValues } from '@/models/payment-option';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import LinkGeneratedModal from '../LinkGeneratedModal';
import { Button } from '@/components/button';
import { FormLabel } from '@/components/form';

const GeneralLinkForm = () => {
  const [country, setCountry] = useState('GH');
  const [isOpen, setIsOpen] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<GeneralLinkFormValues>();

  const [isComplete, setIsComplete] = useState(false);

  const handleGeneratePaymentLink: SubmitHandler<GeneralLinkFormValues> = (
    data
  ) => {
    // Handle link generation
    console.log(data);
    setIsComplete(true);
  };

  return (
    <form
      onSubmit={handleSubmit(handleGeneratePaymentLink)}
      className="space-y-2"
    >
      <div className="flex gap-5">
        <div className=" w-full ">
          <FormLabel name='amount' text='dashboard.amount' required />
          <input
            className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
            type="number"
            placeholder="GHS 000.00"
            {...register('amount', { required: true })}
          />
          {errors.amount && (
            <SimpleText className=" text-xs text-red-500">
              <Translate msgId="dashboard.amountRequired" />
            </SimpleText>
          )}
        </div>

        <div className=" w-full ">
          <FormLabel name='currency' text='dashboard.currency' />
          <CountrySelector
            id={'countries'}
            open={isOpen}
            onToggle={() => setIsOpen(!isOpen)}
            onChange={(event) => setCountry(event)}
            selectedValue={
              COUNTRIES.find(
                (option) => option.value === country
              ) as SelectMenuOption
            }
          />
        </div>
      </div>
      <div className="mt-3 space-x-2 inline-flex ">
        <input
          id="makeFixed"
          type="checkbox"
          className=" !p-2 !rounded-md  dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
        />
        <FormLabel name='makeFixed' text='dashboard.makePaymentFixed' />
      </div>
      <div className=" w-full ">
        <FormLabel name='description' text='dashboard.description' />
        <textarea
          className=" h-28 w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
          placeholder="Description"
          {...register('description')}
        />
      </div>

      <Button variant="darkPrimary" className="rounded-lg w-full mt-5">
        <Translate msgId="dashboard.createLink" />
      </Button>
      <LinkGeneratedModal open={isComplete} setOpen={setIsComplete} />
    </form>
  );
};

export default GeneralLinkForm;
