import clsx from '@/features/utils/clsx';
import { FC, ReactNode } from 'react';

interface NotificationTitleProps {
  className?: string;
  children: ReactNode;
}

const NotificationTitle: FC<NotificationTitleProps> = ({
  className = '',
  children,
}) => {
  const rootClass = clsx('text-sm font-medium text-gray-900', className);

  return <h3 className={rootClass}>{children}</h3>;
};

export default NotificationTitle;
