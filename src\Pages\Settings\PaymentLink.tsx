import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"
import { Button } from "@/components/button"

const PaymentLink = () => {
  return (
    <div>
      <div className=" border-b-2">
        <SimpleText component="h1" className="text-xl">
          <Translate msgId="dashboard.paymentLinks" />
        </SimpleText>
      </div>

      <div className="flex w-full flex-col lg:flex-row gap-5 items-center justify-between mt-5">
        <Button variant="darkPrimary" className=" w-full lg:w-[250px] rounded-lg">
          <Translate msgId="dashboard.updateProfile" />
        </Button>
        <div className="group w-full lg:w-[200px]">
          <Button variant="light" className=" w-full transition-all rounded-lg border-raven-green-800 !text-raven-green-800 group-hover:bg-raven-green-800 group-hover:!text-white">
            <Translate msgId="dashboard.cancel" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default PaymentLink