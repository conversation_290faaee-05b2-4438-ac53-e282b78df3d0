import { isCancel, AxiosError } from "axios";


/**
 * Type guard to determine if a given error is an instance of AxiosError.
 *
 * @param error - The error to check.
 * @returns - `true` if the error is an instance of AxiosError, otherwise `false`.
 */
function isAxiosError(error: any): error is AxiosError {
    return error && error.isAxiosError;
}

/**
 * Formats and retrieves the errors based on their type.
 * 
 * - If the error was due to a canceled request, a generic message is returned.
 * - If it's an AxiosError, the data from the error response or an error object is returned.
 * - For all other errors, a generic "unknown error" message is returned.
 *
 * @param error - The error caught from Axios or any other error.
 * @returns - An object containing the error(s) in a formatted manner.
 */
const getErrors = (error: unknown | AxiosError): Record<'errors', Record<'message', string>[]> | unknown => {
    // Checking if the error is due to a cancel
    if (isCancel(error)) {
        const message = 'There was an error sending your resquest. Please try again later.'
        return {
            errors: [
                {
                    message,
                },
            ]
        };
    }

    if (isAxiosError(error)) {
        return error.response?.data || { errors: [{ message: error.message }] }
    }

    // Return generic object for other types of errors
    return {
        errors: [{
            message: 'An unknown error occurred.',
        }]
    };
};

export default getErrors;
