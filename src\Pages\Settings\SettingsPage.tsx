import { Translate } from "@/components/translate"
import clsx from "@/features/utils/clsx"
import { DashboardContent, DashboardGrid } from "@/layouts/dashboard"
import DashboardHeading from "@/layouts/dashboard/DashboardHeading"
import { Link, Outlet, useLocation } from "react-router-dom"

const SettingsPage = () => {
  const location = useLocation();

  return (
    <DashboardContent>
      <DashboardHeading>
        <Translate msgId="dashboard.settings" />
      </DashboardHeading>
      <DashboardGrid className="mt-5 p-10 bg-white" columns="grid-cols-1">
        <DashboardGrid columns="grid-cols-1" span="col-span-1" gap="gap-3 md:gap-5">
          <div className="inline-flex border-b pb-3 gap-7 flex-wrap md:flex-nowrap">
            {tabs.map(tab => (
              <Link
                key={tab.to}
                to={tab.to}
                className={clsx({
                  'text-white bg-black px-4 font-medium  py-1 rounded-md': location.pathname == tab.to,
                  'text-gray-300 px-4 py-1 font-medium': false,
                })}
              >
                <Translate msgId={tab.label} />
              </Link>
            ))}
          </div>
        </DashboardGrid>

        <DashboardGrid
          gap="gap-3 md:gap-5"
          columns="grid-cols-1"
        >
          <Outlet />
        </DashboardGrid>
      </DashboardGrid>
    </DashboardContent>
  )
}

export default SettingsPage

const tabs = [
  {to: '/dashboard/settings/profile', label: 'dashboard.profile'},
  {to: '/dashboard/settings/security', label: 'dashboard.security'},
  {to: '/dashboard/settings/accounts', label: 'dashboard.accounts'},
  {to: '/dashboard/settings/payment-link', label: 'dashboard.paymentLink'},
  {to: '/dashboard/settings/preferences', label: 'dashboard.preferences'},
  {to: '/dashboard/settings/api', label: 'dashboard.api'},
  {to: '/dashboard/settings/integration', label: 'dashboard.integration'}
]