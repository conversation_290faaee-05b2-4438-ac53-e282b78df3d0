import { useCallback } from 'react';
import { Button } from 'components/button';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { loginUser, getLoading, getError } from './LoginSlice';
import { AppDispatch } from 'store/store';
import { SigninFormData } from '@/models/signin-form-data';
import { resolver } from 'features/utils/signin-form.resolver';

const SigninForm = () => {
  const dispatch: AppDispatch = useDispatch();
  const loading = useSelector(getLoading);
  const error = useSelector(getError);

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<SigninFormData>({ resolver });

  /**
   * Callback function for form submission.
   *
   * This function dispatches actions to initiate the login process.
   *
   * @param {SigninFormData} data - The form data submitted by the user.
   */
  const onSubmit = useCallback(
    (data: SigninFormData) => {
      dispatch(loginUser({ data }));
    },

    [dispatch]
  );

  return (
    <form className="text-left" onSubmit={handleSubmit(onSubmit)}>
      {!!error && (
        <div className="bg-red-50 dark:bg-red-900 px-3 py-1 mb-3">
          <p className="text-red-500 dark:text-red-200 text-center m-0">
            {error.errors.map((err, index) => (
              <span key={index}>{err.message}</span>
            ))}
          </p>
        </div>
      )}
      <div className="mb-3 form-group">
        <label className="form-label">Email</label>
        <input
          className={`dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 form-control form-input w-full${
            errors.email || error?.errors.some((err) => err.field === 'email')
              ? ' error-field'
              : ''
          }`}
          type="email"
          {...register('email')}
          aria-label="Enter email"
          placeholder="Enter your email"
        />
        {errors.email && (
          <span className="text-red-500">{errors.email?.message}</span>
        )}
      </div>
      <div className="mb-3 form-group">
        <label className="form-label">Password</label>
        <input
          className={`dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 form-control form-input w-full${
            errors.password ||
            error?.errors.some((err) => err.field === 'password')
              ? ' error-field'
              : ''
          }`}
          type="password"
          {...register('password')}
          aria-label="Create a password"
          aria-describedby="passwordHelpBlock"
          placeholder="Enter your password"
        />
        {errors.password && (
          <span className="text-red-500">{errors.password?.message}</span>
        )}
      </div>
      <div className="text-center mt-5 mb-6">
        <Button
          type="submit"
          variant="primary"
          loading={loading}
          disabled={loading}
          className="w-full"
        >
          <span>Sign In</span>
        </Button>
      </div>
    </form>
  );
};

export default SigninForm;
