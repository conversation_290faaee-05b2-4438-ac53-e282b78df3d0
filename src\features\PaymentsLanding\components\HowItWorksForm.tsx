import { Button } from "@/components/button";
import { FormLabel } from "@/components/form";
import InputSelector, { InputList } from "@/components/selector/InputSelector";
import { Translate } from "@/components/translate";
import { useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";

type FormValues = {
  phone: string;
  fullName: string;
  amount: number;
  email: string;
};


const HowItWorksForm = () => {
const [open, setOpen] = useState(false)
const [value, setValue] = useState('')
const list = [{title: '3 Months', value:'3'}, {title: '6 Months', value:'6'}, {title: '1 Year', value:'12'}]
  const {
    register,
    handleSubmit,
  } = useForm<FormValues>();


  const handlePayment: SubmitHandler<FormValues> = (data) => {
    // Handle Payment
    console.log(data);
  };

  return (
    <div>
      <form onSubmit={handleSubmit(handlePayment)}>
        <div className="space-y-5 cursor-pointer  w-full ">
          <div className=" w-full ">
            <FormLabel
              name="phoneNumber"
              text="dashboard.phoneNumber"
              required
            />
            <input
              className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="text"
              placeholder="Enter your phone number"
              {...register('phone', { required: true })}
            />
          </div>
          <div className=" w-full ">
            <FormLabel
              name="email"
              text="dashboard.emailAddress"
              required
            />
            <input
              className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="text"
              placeholder="Enter your Email Address"
              {...register('email', { required: true })}
            />
          </div>
          <div className=" w-full ">
            <FormLabel
              name="email"
              text="dashboard.planDuration"
              required
            />
            <InputSelector onChange={(val)=>setValue(val)} onToggle={()=>setOpen(!open)} open={open} id="name" selectedValue={list.find(
                (option) => option.value === value
              ) as InputList} list={list} />
          </div>
          <div className=" w-full ">
            <FormLabel
              name="amount"
              text="dashboard.amount"
              required
            />
            <input
              className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
              type="text"
              placeholder="amount"
              {...register('amount', { required: true })}
            />
          </div>

          <Button variant='darkPrimary' className='rounded-full w-full'>
            <Translate msgId="landing.proceedToPayment" />
          </Button>

        </div>
      </form>
    </div>
  );
}

export default HowItWorksForm