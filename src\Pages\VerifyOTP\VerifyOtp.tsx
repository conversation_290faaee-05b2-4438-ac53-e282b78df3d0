import { useEffect, useState } from 'react';
import { SimpleText } from 'components/typography';
import { Translate } from '@/components/translate';
import { BackButton, Button } from 'components/button';
import mails from '/images/email-marketing.png';
import { getSessionItem } from '@/features/utils/get-session-item';
import OtpInput from '@/components/input/OtpInput';
import { useDispatch, useSelector } from 'react-redux';
import {
  getOTPSuccess,
  handleOTPConfirm,
  sendUserDetails,
} from '@/features/Signup/SignupSlice';
import { AppDispatch } from '@/store/store';
import { Link } from 'react-router-dom';

const VerifyOtp = () => {
  const auth = getSessionItem('auth');
  const dispatch: AppDispatch = useDispatch();
  const otpSuccess = useSelector(getOTPSuccess);

  const [otp, setOtp] = useState<string>('');

  const handleConfirm = (e: MouseEvent, userOtp: string) => {
    e.preventDefault();
    if (!userOtp || userOtp.length !== 4) return;
    dispatch(handleOTPConfirm({ otp: userOtp }));
  };

  useEffect(() => {
    if (otpSuccess) {
      dispatch(sendUserDetails());
    }
  }, [otpSuccess, dispatch]);

  return (
    <div className="md:px-20 pt-10">
      <BackButton />
      <main className="md:flex md:items-center md:justify-between">
        <div className="md:px-10 pt-20 px-10 md:pt-0">
          <form>
            <div>
              <SimpleText component="h1" className="font-bold text-3xl mb-4">
                <Translate msgId="auth.confirmVerify" />
              </SimpleText>
              {!!JSON.parse(auth!) && (
                <SimpleText className="text-gray-500 md:w-2/3">
                  <Translate
                    msgId="auth.emailOtpSent"
                    value={{ email: JSON.parse(auth!)?.email }}
                  />
                </SimpleText>
              )}
            </div>
            <div className="my-10">
              <div>
                <OtpInput
                  inputStyle={
                    'w-12 h-20 rounded form-input font-bold text-3xl dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
                  }
                  inputType="text"
                  value={otp}
                  onChange={setOtp}
                  numInputs={4}
                  renderSeparator={<span className="mr-4" />}
                  renderInput={(props) => <input {...props} />}
                />
              </div>
            </div>
            <div className="mb-5">
              <Button
                variant="transparent"
                className="text-raven-link"
                type="button"
              >
                <SimpleText component="p">
                  <Translate msgId="auth.resendCode" />
                </SimpleText>
              </Button>
            </div>
            <div className="mb-5">
              <Button
                variant="darkPrimary"
                onClick={(e: MouseEvent) => handleConfirm(e, otp)}
                component={Link}
                to="/dashboard"
                className="block rounded py-3 px-4 text-center"
              >
                <SimpleText component="p">
                  <Translate msgId="auth.confirm" />
                </SimpleText>
              </Button>
            </div>
          </form>
        </div>
        <div className="hidden md:block shrink">
          <div className="relative h-hull">
            <div className="relative">
              <img src={mails} alt="various messaging icons" />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default VerifyOtp;
