import { useMemo } from 'react';
import { useGetTransactionsQuery } from '@/services/endpoints/transactions';

const useGetTransactions = (options: { page: number, limit: number, select: any }) => {
    const { page = 1, limit = 10, select } = options;

    return useGetTransactionsQuery(useMemo(() => ({
        page,
        limit
    }), [
        page,
        limit
    ]), {
        selectFromResult: (result) => select(result)
    });
};

export default useGetTransactions;
