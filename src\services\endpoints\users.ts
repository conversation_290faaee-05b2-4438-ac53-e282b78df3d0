import rtkQueryBase from "../api/rtk-query-base";


const extendedAPI = rtkQueryBase.injectEndpoints({
    overrideExisting: true,
    endpoints: (builder) => ({
        getUsers: builder.query({
            query: (params) => ({
                url: '/api/admin/users/',
                params
            })
        }),
        getUser: builder.query({
            query: (params) => `/api/admin/users/${params.id}`
        }),
    })
});

export const {  useGetUsersQuery, useGetUserQuery } = extendedAPI;

export default extendedAPI;
