import { FC } from 'react';
import { UserHeading } from 'components/userHeading';
import useSessionHasRole from '@/hooks/useSessionHasRole';
import useIsAuthorized from '@/hooks/useIsAuthorized';
import useIsMerchant from '@/hooks/useIsMerchant';
import clsx from 'features/utils/clsx';

interface DashboardUserCardProps {
  className?: string;
}

const DashboardUserCard: FC<DashboardUserCardProps> = ({ className }) => {
  // sessionStorage.getItem('session')
  const { username, avatar } = {
    username: 'David',
    avatar: null,
  };
  const isAdmin = useSessionHasRole('admin');
  const isMerchant = useIsMerchant();
  const isAuthorized = useIsAuthorized();

  return (
    <UserHeading
      className={clsx('p-6', className)}
      title={isAuthorized ? username : 'Welcome'}
      subtitle={
        isAuthorized
          ? isAdmin
            ? 'Admin'
            : isMerchant
            ? 'Merchant'
            : 'Individual'
          : 'Login to continue'
      }
      avatar={avatar}
    />
  );
};

export default DashboardUserCard;
