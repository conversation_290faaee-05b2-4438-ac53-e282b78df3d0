import { FC } from 'react';
import { Skeleton } from '@/components/skeleton';
import { Avatar } from '@/components/avatar';
import { SimpleText } from '@/components/typography';

interface CustomersLoadingProps {
  loading: boolean;
}
const CustomersLoading: FC<CustomersLoadingProps> = ({ loading }) => {
  return (
    <Skeleton active={loading}>
      <div className="p-1 outline outline-2 outline-gray-100 hover:outline-raven-green-800 rounded-lg">
        <div className="flex items-center gap-5">
          <Avatar radius="full" size="lg" src={null} />
          <SimpleText truncate className="text-sm md:text-lg font-semibold">
            Loading
          </SimpleText>
        </div>
      </div>
    </Skeleton>
  );
};

export default CustomersLoading;
