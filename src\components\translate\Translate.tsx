import { FC } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Interface for TranslateProps
 * @interface
 * @property {string} msgId - The message ID for the translation string.
 */
interface TranslateProps {
  msgId: string;
  value?: { [key: string]: string };
}

/**
 * A React Functional Component that translates the given message ID into its localized string.
 *
 * @component
 * @param {TranslateProps} props - Props for Translate component
 * @param {string} props.msgId - The message ID for the translation string.
 * @param {{[key: string]: string}} props.value - The value to be passed into translation string.
 * @returns {JSX.Element} The localized string wrapped in a span element.
 *
 * @example
 * <Translate msgId="greeting" />
 */
const Translate: FC<TranslateProps> = ({
  msgId,
  value,
}: TranslateProps): JSX.Element => {
  /**
   * Using the useTranslation hook from react-i18next to get the translation function.
   * @function
   */
  const { t } = useTranslation();

  /**
   * Render the translated string.
   * @returns {JSX.Element} Translated string
   */
  return <>{value ? t(msgId, value) : t(msgId)}</>;
};

export default Translate;
