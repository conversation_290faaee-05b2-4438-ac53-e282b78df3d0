import { SimpleText } from '@/components/typography';
import noData from '/images/empty.png';
import { Translate } from '@/components/translate';
import { FC, ReactNode } from 'react';

interface NoDataProps {
  children?: ReactNode
  title?: string
  description?: string
}

const NoData: FC<NoDataProps> = ({children, title, description}) => {
  return (
    <div className="px-5 space-y-3 py-3 flex flex-col items-center justify-center">
      <img src={noData} alt="no data "/>

      <SimpleText className="text-sm font-medium text-gray-900 text-center">
        <Translate msgId={title || "dashboard.noData"} />
      </SimpleText>
      {description && (
        <SimpleText className="text-center  text-sm text-gray-500 md:w-72">
          <Translate msgId={description} />
        </SimpleText>
      )}

      {children}
    </div>
  )
}

export default NoData