import { FC } from 'react';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';

interface SecurityPointProps {
  title: string;
  description: string;
}
const SecurityPoint: FC<SecurityPointProps> = ({ title, description }) => {
  return (
    <div className="flex items-start gap-10">
      <div className="w-5 h-5 rounded-full mt-1 bg-transparent border inline-flex items-center justify-center radio-ring">
        <div className="w-3 h-3 rounded-full bg-gray-200 radio-dot" />
      </div>
      <div className="space-y-3">
        <SimpleText component="h3" className="font-semibold text-xl">
          <Translate msgId={title} />
        </SimpleText>
        <SimpleText>
          <Translate msgId={description} />
        </SimpleText>
      </div>
    </div>
  );
};

export default SecurityPoint;
