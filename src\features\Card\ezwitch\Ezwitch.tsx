import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { Button } from '@/components/button';
import { allCards } from '@/data/card-page';
import { useEffect, useState } from 'react';

const Ezwitch = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    let currentIndex = 0;
    const intervalId = setInterval(() => {
      setActiveIndex(currentIndex);
      currentIndex = currentIndex === allCards.length - 1 ? 0 : currentIndex + 1
    }, 3000);

    return () => clearInterval(intervalId);
  }, []);

  return (
    <section className="mt-20 lg:container lg:mx-auto md:py-10 p-5 md:px-10  bg-gray-100 rounded-lg ">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
        <div className="relative w-full px-5 md:px-12 rounded-3xl">
          <img
            src={allCards[activeIndex].image}
            alt="Phone showing raven app"
            className="shrink-0 object-contain w-96 h-96"
          />
          <div className="flex gap-3 justify-center md:w-96 mt-2">
            {allCards.map((_, index) => (
              <div key={index} className={`h-3 w-3 rounded-full p-3 ${index === activeIndex ? 'bg-raven-green-800' : 'bg-gray-400'}`}></div>
            ))}
          </div>
        </div>
        <div className="flex flex-col justify-center lg:space-y-10 order-first lg:order-last w-full dark:text-white text-gray-800  md:pr-16 mb-10 md:mb-0">
          <SimpleText className=" text-raven-green-800 text-3xl ">
            <Translate msgId={allCards[activeIndex].title} />
          </SimpleText>
          <SimpleText component="h1" className="text-gray-400  ">
            <Translate msgId={allCards[activeIndex].description} />
          </SimpleText>

          <div className="">
            <Button
              variant="darkPrimary"
              className="mt-5 rounded"
              type="button"
            >
              <Translate msgId="card.getCard" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Ezwitch;

