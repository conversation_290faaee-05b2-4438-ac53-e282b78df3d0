import { useMemo } from 'react';
import { useGetRefundsQuery } from '@/services/endpoints/transactions';

const useGetRefunds = (options: { page: number, limit: number, select: any }) => {
    const { page = 1, limit = 10, select } = options;

    return useGetRefundsQuery(useMemo(() => ({
        page,
        limit
    }), [
        page,
        limit
    ]), {
        selectFromResult: (result) => select(result)
    });
};

export default useGetRefunds;
