import { useEffect, useState } from 'react';
import QRCode from 'react-qr-code';
import { Translate } from '../translate';
import { SimpleText } from '../typography';
import GoogleButton from '../download-buttons/GoogleButton';
import AppleButton from '../download-buttons/AppleButton';
export default function AppDownloadQrCode() {
  const [qrcodeValue, setQrCodeValue] = useState<string>('');
  const playStoreUrl =
    'https://play.google.com/store/apps/details?id=com.example.app';

  const appStoreUrl = 'https://apps.apple.com/app/id123456789';
  const userOs = navigator.userAgent;

  useEffect(() => {
    const getOs = () => {
      if (/andorid/i.test(userOs)) return 'android';
      if (/iPhone/.test(userOs)) return 'ios';
    };
    const link = getOs() == 'android' ? playStoreUrl : appStoreUrl;
    setQrCodeValue(link);
  }, [userOs]);
  return (
    <div className="hidden absolute md:top-full -top-[calc(100vh/3)] md:-left-20 -left-16 opacity-0 group-hover:opacity-100 group-hover:block transition-all duration-200">
      <section className="w-full h-6"></section>
      <div className="group-hover:flex flex-col justify-center items-center gap-4  bg-raven-green-50 shadow-lg w-80 rounded-2xl p-5 text-center pb-0">
        <SimpleText
          component="h3"
          className="text-raven-green-800 text-3xl font-bold"
        >
          <Translate msgId="home.qrGetApp" />
        </SimpleText>
        <SimpleText component="small">
          <Translate msgId="home.scan" />
        </SimpleText>
        <div className="border-2 border-raven-green-800 border-opacity-30 p-3 rounded-xl">
          <QRCode value={qrcodeValue} size={128} />
        </div>
        <SimpleText>
          <Translate msgId="home.orClickDownload" />
        </SimpleText>
        <div className="flex justify-between items-center -mt-6">
          <AppleButton href={appStoreUrl} className="w-80 h-12" />
          <GoogleButton href={playStoreUrl} className="w-96 h-16" />
        </div>
      </div>
    </div>
  );
}
