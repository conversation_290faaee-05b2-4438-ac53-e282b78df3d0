import clsx from '@/features/utils/clsx';
import { FC, ReactNode } from 'react';

interface CardHeaderProps {
  className?: string;
  overline?: ReactNode;
  primary: ReactNode;
  secondary?: ReactNode;
}

const CardHeader: FC<CardHeaderProps> = ({
  className,
  overline,
  primary,
  secondary,
}) => {
  const rootClass = clsx('px-4 py-5 sm:px-6', className);

  return (
    <div className={rootClass}>
      <span>
        {overline && (
          <p className="pb-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            {overline}
          </p>
        )}
        <h3 className="text-lg leading-6 font-medium text-gray-900">
          {primary}
        </h3>
        {secondary && (
          <span className="mt-1 text-sm text-gray-500">{secondary}</span>
        )}
      </span>
    </div>
  );
};

export default CardHeader;
