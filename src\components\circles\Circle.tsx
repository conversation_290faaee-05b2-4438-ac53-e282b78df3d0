import { CSSProperties, FC } from 'react';

interface CircleProps {
  top: string;
  left: string;
  diameter: string;
  opacity: number;
  color: string;
}

const Circle: FC<CircleProps> = ({ top, left, diameter, opacity, color }) => {
  const styles: CSSProperties = {
    position: 'absolute',
    top,
    left,
    width: diameter,
    height: diameter,
    borderRadius: '50%',
    background: `radial-gradient(circle, ${color}, transparent)`,
    opacity,
    zIndex: -1,
  };

  return <div style={styles}></div>;
};

export default Circle;
