import { Clipboard<PERSON>heck } from 'heroicons-react';
import { SimpleText } from '@/components/typography';
import { Trans } from 'react-i18next';
function Actions() {
  const actionsData = [
    {
      title: 'enterprise.action1Title',
      subTitle: 'enterprise.action1SubTitle',

      description: 'enterprise.item1Description',
      listItems: [
        {
          title: 'enterprise.listItem1Title1',
          description: 'enterprise.listItem1Description1'
        },
        {
          title: 'enterprise.listItem1Title2',
          description: 'enterprise.listItem1Description2'
        },
        {
          title: 'enterprise.listItem1Title3',
          description: 'enterprise.listItem1Description3'
        }
      ]
    },
    {
      title: 'enterprise.action2Title',
      subTitle: 'enterprise.action2SubTitle',
      description: 'enterprise.item2Description',
      listItems: [
        {
          title: 'enterprise.listItem2Title1',
          description: 'enterprise.listItem2Description1'
        },
        {
          title: 'enterprise.listItem2Title2',
          description: 'enterprise.listItem2Description2'
        },
        {
          title: 'enterprise.listItem2Title3',
          description: 'enterprise.listItem2Description3'
        }
      ]
    },
    {
      title: 'enterprise.action3Title',
      subTitle: 'enterprise.action3SubTitle',
      description: 'enterprise.item3Description',
      listItems: [
        {
          title: 'enterprise.listItem3Title1',
          description: 'enterprise.listItem3Description1'
        },
        {
          title: 'enterprise.listItem3Title2',
          description: 'enterprise.listItem3Description2'
        },
        {
          title: 'enterprise.listItem3Title3',
          description: 'enterprise.listItem3Description3'
        },
        {
          title: 'enterprise.listItem3Title4',
          description: 'enterprise.listItem3Description4'
        }
      ]
    }
  ];
  return (
    <div>
      {actionsData.map(({ title, subTitle, description, listItems }, idx) => (
        <div
          key={idx}
          className="shadow-[rgba(0,_0,_0,_0.2)_10px_-90px_130px_-7px] rounded-tr-3xl rounded-tl-3xl overflow-hidden grid grid-cols-1 z-10 lg:grid-cols-2"
        >
          <div
            className={`${
              idx == 1 ? 'bg-raven-green-50' : 'bg-[#37DA7A1]'
            }  col-span-1 flex flex-col justify-between p-20 space-y-6 `}
          >
            <SimpleText
              component="h2"
              className="leading-relaxed  font-semibold text-xl md:text-3xl"
            >
              {/* title here */}
              <Trans i18nKey={title} />
            </SimpleText>
            <SimpleText component="h2" className=" md:text-3xl font-semibold">
              {/* subTitle */}
              <Trans i18nKey={subTitle} />
            </SimpleText>
            <SimpleText component="h2" className="">
              {/* description */}
              <Trans i18nKey={description} />
            </SimpleText>
          </div>
          <div
            className={`${
              idx == 0 || idx == 2 ? 'bg-raven-green-50' : 'bg-[#37DA7A1]'
            }   px-16 py-10 lg:py-16 col-span-1 space-y-10`}
          >
            {listItems.map(({ title, description }, idx) => (
              <div className="flex gap-5" key={idx}>
                <div className="w-20 h-20 text-2xl">
                  <ClipboardCheck height={30} width={30} />
                </div>
                <div className="space-y-5">
                  <SimpleText
                    component="h2"
                    className=" md:text-xl font-semibold"
                  >
                    {/* title */}
                    <Trans i18nKey={title} />
                  </SimpleText>
                  <SimpleText component="h2" className="">
                    {/* description */}
                    <Trans i18nKey={description} />
                  </SimpleText>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

export default Actions;
