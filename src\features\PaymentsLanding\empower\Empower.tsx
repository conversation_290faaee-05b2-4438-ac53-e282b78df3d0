import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import EmpowerImage from '/images/empower.png';
import { Cog, DocumentDownload, Key, Users } from 'heroicons-react';

const Empower = () => {
  return (
    <section className="mt-20 md:py-10 md:px-10">
      <div className="mb-8 space-y-5 text-center">
        <SimpleText className='text-raven-green-800 text-4xl font-bold'>
          <Translate msgId="landing.empowerTitle" />
        </SimpleText>
        <SimpleText className='text-raven-green-800 text-2xl'>
          <Translate msgId="landing.empowerSub" />
        </SimpleText>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
        <div className=" w-full p-5 md:p-12 rounded-3xl">
          <img
            src={EmpowerImage}
            alt="Phone showing raven app"
            className="shrink-0 object-contain"
          />
        </div>
        <div className="space-y-3 order-first lg:order-last w-full dark:text-white text-gray-800 flex items-center justify-center md:pr-16 mb-10 md:mb-0">
          <div className='bg-gray-100 gap-5 flex flex-col rounded-lg px-10 py-5'>
            <div className="flex  gap-5 ">
              <Key className='w-10 h-10 text-raven-green-800 ' />
              <SimpleText>
                <Translate msgId="landing.empowerList1" />
              </SimpleText>
            </div>
            <div className="flex  gap-5 ">
              <DocumentDownload className='w-10 h-10 text-raven-green-800' />
              <SimpleText>
                <Translate msgId="landing.empowerList2" />
              </SimpleText>
            </div>
            <div className="flex  gap-5 ">
              <Cog className='w-10 h-10 text-raven-green-800' />
              <SimpleText>
                <Translate msgId="landing.empowerList3" />
              </SimpleText>
            </div>
            <div className="flex  gap-5 ">
              <Users className='w-10 h-10 text-raven-green-800' />
              <SimpleText>
                <Translate msgId="landing.empowerList4" />
              </SimpleText>
            </div>
            <div className="flex  gap-5 ">
              <div className="">
              <Cog className='w-10 h-10 text-raven-green-800' />
                
              </div>
              <SimpleText>
                <Translate msgId="landing.empowerList5" />
              </SimpleText>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Empower;
