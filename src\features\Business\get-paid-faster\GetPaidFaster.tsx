import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import BusinessHero from '/images/business-getpaid.png';
import { Button } from '@/components/button';
import GetPaidFasterCard from '../components/GetPaidFasterCard';
import { Link } from 'react-router-dom';

const GetPaidFaster = () => {
  return (
    <section className="mt-20 md:py-10 md:px-10">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
        <div className="relative w-full p-5 md:p-12 rounded-3xl">
          <img
            src={BusinessHero}
            alt="Phone showing raven app"
            className="shrink-0 object-contain"
          />
          <div className=" border absolute -bottom-10 rounded-xl bg-white md:-right-10 z-50">
            <GetPaidFasterCard />
          </div>
        </div>
        <div className="space-y-3 order-first lg:order-last w-full dark:text-white text-gray-800 flex items-center justify-center md:pr-16 mb-10 md:mb-0">
          <div>
            <SimpleText
              component="h2"
              className="md:text-6xl mb-3 text-3xl font-semibold leading-normal"
            >
              <Translate msgId="business.getPaidFaster" />
            </SimpleText>
            <SimpleText>
              <Translate msgId="business.getPaidFasterDesc" />
            </SimpleText>

            <Button
              component={Link}
              to='/payments'
              variant="darkPrimary"
              className="mt-5 rounded"
              type="button"
            >
              <Translate msgId="business.getPaidFasterButton" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GetPaidFaster;
