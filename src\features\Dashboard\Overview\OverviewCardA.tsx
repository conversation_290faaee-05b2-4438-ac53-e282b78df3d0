import { Translate } from '@/components/translate';
import clsx from '@/features/utils/clsx';
import { Skeleton } from 'components/skeleton';
import { InformationCircle } from 'heroicons-react';
import { FC } from 'react';

interface OverviewCardProps {
  loading: boolean;
  name: string;
  stat: string | number;
  className?: string;
  primaryTextClass?: string;
  secondaryTextClass?: string;
  iconAlt?: string;
}
const OverviewCardA: FC<OverviewCardProps> = ({
  loading,
  name,
  stat = '-',
  className = 'bg-white',
  primaryTextClass = 'text-gray-900',
  secondaryTextClass = 'text-gray-500',
}) => {
  return (
    <Skeleton active={loading}>
      <div
        className={clsx(
          'px-4 py-3 rounded-2xl bg-[#F3FEF6CC] shadow-sm overflow-hidden sm:p-6 cursor-default overview-item',
          className
        )}
      >
        <dt
          className={clsx(
            'mt-1 mb-2 text-sm font-medium truncate flex justify-between items-center',
            secondaryTextClass
          )}
        >
          <Translate msgId={name} />
          <InformationCircle width={20} />
        </dt>
        <dd
          title={`${stat}`}
          className={clsx('text-lg font-semibold truncate', primaryTextClass)}
        >
          {stat}
        </dd>
      </div>
    </Skeleton>
  );
};

export default OverviewCardA;
