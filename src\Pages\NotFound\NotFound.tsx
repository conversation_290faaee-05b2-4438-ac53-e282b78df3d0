import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import { Button } from '@/components/button';
import { Link } from 'react-router-dom';
export default function NotFound() {
  return (
    <div className="w-screen h-screen flex flex-col items-center justify-center gap-3 dark:bg-[#114225] bg-white">
      <SimpleText className="text-5xl dark:text-white text-raven-green-800">
        404
      </SimpleText>
      <SimpleText>
        <Translate msgId="notFound.landingNotFound" />
      </SimpleText>
      <Button variant="primary" className="rounded-lg" component={Link} to="/">
        <Translate msgId="notFound.visitLanding" />
      </Button>
    </div>
  );
}
