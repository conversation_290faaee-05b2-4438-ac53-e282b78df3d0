import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';

const PayoutCard = () => {
  return (
    <div className='relative h-full flex items-center justify-center'>
      <div className="absolute w-[105%] top-8">
        <div className="px-4 py-2 border bg-white rounded-lg mb-3 text-sm ">
          <SimpleText component="h3" className=" text-gray-400 mb-2">
            <Translate msgId={'business.bank'} />
          </SimpleText>
          <SimpleText component="h3" className=" ">
            <Translate msgId={'business.raven'} />
          </SimpleText>
        </div>

        <div className="px-4 py-2 border bg-white rounded-lg mb-5 text-sm">
          <SimpleText component="h3" className=" text-gray-400 mb-2">
            <Translate msgId={'business.accountNumber'} />
          </SimpleText>
          <SimpleText component="h3" className=" ">
            **********
          </SimpleText>
        </div>
      </div>

      <div className="justify-center flex items-center absolute w-full bottom-3">
        <Button variant="darkPrimary" className="rounded-lg !py-2">
            <Translate msgId={'business.pay'} />
        </Button>
      </div>
    </div>
  );
};

export default PayoutCard;
