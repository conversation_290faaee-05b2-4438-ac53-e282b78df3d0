import { FC, Fragment } from 'react';
import { Text } from 'components/typography';
import { Link } from 'react-router-dom';
import { ChevronRight, Logout, Login } from 'heroicons-react';
import useSignOut from '@/hooks/useSignout';
import useMenu from '@/hooks/useMenu';
import useIsAuthorized from '@/hooks/useIsAuthorized';
import clsx from '@/features/utils/clsx';
import { MenuAction } from '@/models/menu-actions.interface';

interface DashboardUserNavigationProps {
  className?: string;
}

const DashboardUserNavigation: FC<DashboardUserNavigationProps> = ({
  className,
}) => {
  const signOut = useSignOut();
  const [, menuActions] = useMenu();
  const isAuthorized = useIsAuthorized();

  return (
    <div className={clsx('flex flex-col divide-y divide-gray-200', className)}>
      {isAuthorized ? (
        <Fragment>
          <Text
            size="md"
            to="/dashboard/settings"
            onClick={(menuActions as MenuAction).off}
            component={Link}
            className="flex items-center px-6 py-3 hover:bg-cool-gray-100 transition duration-150"
          >
            Settings
            <ChevronRight className="ml-auto text-gray-300" />
          </Text>
          <Text
            size="md"
            onClick={signOut}
            className="flex items-center cursor-pointer px-6 py-3 hover:bg-cool-gray-100 transition duration-150"
          >
            Log Out
            <Logout className="ml-auto text-gray-300" />
          </Text>
        </Fragment>
      ) : (
        <Text
          size="md"
          to="/login"
          onClick={(menuActions as MenuAction).off}
          component={Link}
          className="flex items-center px-6 py-3 hover:bg-cool-gray-100 transition duration-150"
        >
          Login
          <Login className="ml-auto text-gray-300" />
        </Text>
      )}
    </div>
  );
};

export default DashboardUserNavigation;
