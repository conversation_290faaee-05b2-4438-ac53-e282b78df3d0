import React from 'react';
import ReactDOM from 'react-dom/client';
import './i18n.ts';
import App from './App.tsx';
import './index.css';
import { Provider } from 'react-redux';
import { store } from './store/store.ts';
import { worker } from './mock.ts';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
);

// start mocking api calls till server is ready
// if (process.env.NODE_ENV === 'development') {
worker.start();
// }
