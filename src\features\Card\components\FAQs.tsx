import { Accordion } from "@/components/accordion";
import { Translate } from "@/components/translate";
import { SimpleText } from "@/components/typography";

const FAQs = () => {
  return (
    <section className='py-5 lg:py-28'>
      <section className=' container mx-auto '>
        <SimpleText className=" text-center font-semibold text-raven-green-800 text-4xl lg:text-5xl ">
          <Translate msgId="card.faqTitle" />
        </SimpleText>

        <div className='space-y-10 lg:w-3/5 mx-auto px-5 lg:px-0  pt-12 md:pt-20'>
          <Accordion data={items} itemClass="bg-gray-100 " />
        </div>
      </section>
    </section>
  );
};

export default FAQs;

const items = [
  {
    title: "card.faqT1",
    content: "card.faqDesc",
  },
  {
    title: "card.faqT2",
    content: "card.faqDesc",
  },
  {
    title: "card.faqT3",
    content: "card.faqDesc",
  },
  {
    title: "card.faqT4",
    content: "card.faqDesc",
  }
];
