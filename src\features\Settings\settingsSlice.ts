import { RootState } from "@/store/store";
import { createSelector, createSlice } from "@reduxjs/toolkit"

interface InitailState {
  integrations: Record<string, unknown>[]
  accounts: Record<string, unknown>[]
}

const initialState: InitailState = {
  integrations: [],
  accounts: []
}

export const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setIntegration: (state, action) => {
      state.integrations = action.payload
    },
    setAccounts: (state, action) => {
      state.accounts = action.payload
    },
  }
})


const { actions, name } = settingsSlice;

export const { setIntegration, setAccounts } = actions

const getSlice = (state: RootState) => state[name]

export const getIntegrations = createSelector(getSlice, (slice) => slice.integrations)

export const getAccounts = createSelector(getSlice, (slice) => slice.accounts)