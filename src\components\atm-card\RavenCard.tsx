import mastercardLogo from '/icons/mastercard-logo.png';
import { SimpleText } from 'components/typography';
import { Translate } from '@/components/translate';

const RavenCard = () => {
  return (
    <div className="bg-darkGreen-500 h-52 rounded-lg text-white px-6 py-5 overflow-hidden relative">
      <div className="flex flex-col justify-between h-full">
        <div className="flex justify-between items-center relative z-5">
          <div className="icon">
            <img src={mastercardLogo} alt="mastercard logo" className="w-12" />
          </div>
          <div className="icon">raven</div>
        </div>
        <div className="relative z-5">
          <div>
            <SimpleText component="small" className="text-[0.6rem]">
              <Translate msgId="dashboard.cardNumber" />
            </SimpleText>
          </div>
          <div>
            <SimpleText component="small">**** **** **** 3554</SimpleText>
          </div>
        </div>
        <div className="flex items-center relative z-5 justify-between">
          <div>
            <div>
              <SimpleText component="small" className="text-[0.6rem]">
                <Translate msgId="dashboard.cardHolder" />
              </SimpleText>
            </div>
            <div>
              <SimpleText component="small">DAVID QUARTZ</SimpleText>
            </div>
          </div>
          <div>
            <div>
              <SimpleText component="small" className="text-[0.6rem]">
                <Translate msgId="dashboard.expiresOn" />
              </SimpleText>
            </div>
            <div>
              <SimpleText component="small">24/06</SimpleText>
            </div>
          </div>
        </div>
      </div>
      <div className="absolute -bottom-40 rounded-full -right-28 w-72 h-72 bg-darkGreen-400 z-0" />
      <div className="absolute -top-48 rounded-full -left-28 w-72 h-72 bg-dirtRed-500 z-0" />
    </div>
  );
};

export default RavenCard;
