import Jobs from './jobs';
import CareerApplicationForm from './form';
import { useState } from 'react';
import { jobSelected } from './jobs';
interface OverlayProps {
  child: 'form' | 'jobs' | null;
  displayOverlay: boolean;
  onClose: () => void;
}

export default function Overlay({
  child,
  displayOverlay = false,
  onClose
}: OverlayProps) {
  const [valueFromJobs, setValueFromJobs] = useState<jobSelected | null>(null);
  const [applyDispatched, setApplyDispatched] = useState<boolean>(false);

  const handleOverlayClose = () => {
    setValueFromJobs(null);
    onClose();
    setApplyDispatched(false);
  };
  let component;
  if (child)
    component =
      child === 'form' || applyDispatched ? (
        <CareerApplicationForm
          onCloseForm={handleOverlayClose}
          valueFromJobs={valueFromJobs}
        />
      ) : (
        <Jobs
          onCloseJobs={handleOverlayClose}
          handleApply={(value) => {
            setValueFromJobs(value);
            alert(JSON.stringify(value));
            setApplyDispatched(true);
          }}
        />
      );

  return (
    <div
      className={`w-full h-full duration-200 p-3 backdrop-blur-lg transition-all cursor-pointer z-[99999] fixed top-0 left-0 flex items-center justify-center ${
        displayOverlay
          ? 'pointer-events-auto opacity-100 visible'
          : 'pointer-events-none opacity-0 invisible'
      }`}
      onClick={onClose}
    >
      <div
        className={`w-full h-full absolute inset-0 transition-colors duration-200 ${
          displayOverlay
            ? 'bg-[rgba(61,61,61,0.4)] dark:bg-[rgba(0,0,0,0.4)]'
            : ''
        } hover:bg-[rgba(61,61,61,0.1)] dark:hover:bg-[rgba(0,0,0,0.1)]`}
      />

      <div
        className="relative pointer-events-auto  "
        onClick={(event) => event.stopPropagation()}
      >
        {component}
      </div>
    </div>
  );
}
