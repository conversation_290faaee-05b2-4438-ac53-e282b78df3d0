import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import HowItWorksForm from '../components/HowItWorksForm';

const HowItWorks = () => {
  return (
    <section className="mt-20 md:py-10 md:px-10">
      <div className="mb-8 space-y-5 text-center">
        <SimpleText className='mb-5'>
          <Translate msgId="landing.howItWorks" />
        </SimpleText>
        <SimpleText className='text-raven-green-800 text-4xl font-bold'>
          <Translate msgId="landing.howItWorksTitle" />
        </SimpleText>
        <SimpleText className='text-raven-green-800 text-2xl'>
          <Translate msgId="landing.howItWorksSub" />
        </SimpleText>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
        <div className=" w-full space-y-5 p-5 md:p-12 rounded-3xl">
          <div className="relative p-2 px-3 rounded bg-gray-100">
            <SimpleText className='text-raven-green-800 font-bold'>
              <Translate msgId="landing.howItWorksT1" />
            </SimpleText>
            <SimpleText className=''>
              <Translate msgId="landing.howItWorksD1" />
            </SimpleText>

            <div className="w-6 h-6 bg-raven-green-800 text-white font-semibold flex items-center justify-center rounded-full p-1 absolute -left-3 -top-2">
              1
            </div>
          </div>
          <div className="relative p-2 px-3 rounded bg-gray-100">
            <SimpleText className='text-raven-green-800 font-bold'>
              <Translate msgId="landing.howItWorksT2" />
            </SimpleText>
            <SimpleText className=''>
              <Translate msgId="landing.howItWorksD2" />
            </SimpleText>
            <div className="w-6 h-6 bg-raven-green-800 text-white font-semibold flex items-center justify-center rounded-full p-1 absolute -left-3 -top-2">
              2
            </div>
          </div>
          <div className="relative p-2 px-3 rounded bg-gray-100">
            <SimpleText className='text-raven-green-800 font-bold'>
              <Translate msgId="landing.howItWorksT3" />
            </SimpleText>
            <SimpleText className=''>
              <Translate msgId="landing.howItWorksD3" />
            </SimpleText>
            <div className="w-6 h-6 bg-raven-green-800 text-white font-semibold flex items-center justify-center rounded-full p-1 absolute -left-3 -top-2">
              3
            </div>
          </div>
        </div>
        <div className="space-y-3 order-first lg:order-last w-full dark:text-white text-gray-800  md:px-16 mb-10 md:mb-0">
          <HowItWorksForm />
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
