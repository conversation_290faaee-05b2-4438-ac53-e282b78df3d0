import { FC, Fragment, ReactNode } from 'react';

import DashboardSidebarDesktop from './DashboardSidebarDesktop';
import DashboardSidebarMobile from './DashboardSidebarMobile';

interface DashboardSidebarProps {
  children: ReactNode;
  disableUserCard?: boolean;
  disableSecondaryNavigation?: boolean;
}
const DashboardSidebar: FC<DashboardSidebarProps> = ({
  children,
  disableUserCard = false,
  disableSecondaryNavigation = false,
}) => (
  <Fragment>
    <DashboardSidebarMobile
      disableUserCard={disableUserCard}
      disableSecondaryNavigation={disableSecondaryNavigation}
    >
      {children}
    </DashboardSidebarMobile>
    <DashboardSidebarDesktop>{children}</DashboardSidebarDesktop>
  </Fragment>
);

export default DashboardSidebar;
