import { SimpleText } from '@/components/typography';
import { Translate } from 'components/translate';

const MomoToCard = () => {
  return (
    <section>
      <div className="my-24 container text-center mx-auto bg-gray-100 px-5 py-10 lg:p-20 rounded-lg space-y-5">
        <SimpleText
          component="h1"
          className="text-gray-400 leading-8  text-center  font-extrabold "
        >
          <Translate msgId="card.ravenCards" />
        </SimpleText>
        <SimpleText
          className=" text-center lg:w-3/5 mx-auto text-raven-green-800 text-2xl lg:text-4xl font-semibold "
        >
          <Translate msgId="card.momoToCardTitle" />
        </SimpleText>
        <SimpleText
          component="h1"
          className="text-gray-400 lg:w-3/5 mx-auto  text-center "
        >
          <Translate msgId="card.momoToCardDesc" />
        </SimpleText>
      </div>
    </section>
  );
};

export default MomoToCard;
