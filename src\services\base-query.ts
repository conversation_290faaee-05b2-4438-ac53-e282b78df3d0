import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getToken } from 'features/Auth/authSlice';
import { RootState } from '@/store/store';
import { API_URL } from '@/lib/constants';

const baseQuery = fetchBaseQuery({
  baseUrl: API_URL,
  prepareHeaders: (headers, { getState }) => {
    const state = getState() as RootState;
    const token = getToken(state);

    if (token) headers.set('Authorization', `Bearer ${token}`);
    return headers;
  }
});

export default baseQuery;
