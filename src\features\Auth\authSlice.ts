import { RootState } from '@/store/store';
import { createSelector, createSlice } from '@reduxjs/toolkit';
import { getSessionStorageItem, setSessionStorageItem, removeSessionStorageItem } from '../utils/sessionStorage';
import { RAVEN_TOKEN_ID, RAVEN_USER_ID } from '@/lib/constants';

interface AuthState {
    uuid: string | null;
    token: string | null;
}

const initialState: AuthState = {
    uuid: getSessionStorageItem(RAVEN_TOKEN_ID) || null,
    token: getSessionStorageItem(RAVEN_USER_ID) || null,
};

export const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        unsetCredentials: (state) => {
            state.uuid = null;
            state.token = null;

            removeSessionStorageItem(RAVEN_TOKEN_ID);
            removeSessionStorageItem(RAVEN_USER_ID);
        },
        setCredentials: (state, action) => {
            const { uuid, token } = action.payload;
            if (!token) return 

            // set credentials in state 
            state.uuid = uuid;
            state.token = token;

            // set credentials in storage
            setSessionStorageItem(RAVEN_TOKEN_ID, token);
            setSessionStorageItem(RAVEN_USER_ID, uuid);
        }
    }
});

export const { actions, name } = authSlice
export const { unsetCredentials, setCredentials } = actions;
export const getCredentials = (state: RootState) => state[name];

export const getToken = createSelector(getCredentials, state => state?.token ?? null);
export const getUUID = createSelector(getCredentials, state => state?.uuid ?? null);
