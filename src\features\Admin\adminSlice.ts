import { createSelector, createSlice } from "@reduxjs/toolkit";
import { RootState } from "@/store/store";
import { User } from "@/models/users";

interface InitialState {
    users: User[];
}

const initialState: InitialState = {
    users: []
}

export const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        setUsers: (state, action: { type: string, payload: User[]})=>{
            state.users = action.payload
        },
    }
})

const { actions, name } = userSlice;

export const { setUsers } = actions

const getSlice = (state: RootState) => state[name]

export const getUsers = createSelector(getSlice, (slice) => slice.users || []);