import { Skeleton } from "@/components/skeleton";
import { FC } from "react";

interface PaymentLoadingProps {
  loading: boolean;
}

const PaymentLoading: FC<PaymentLoadingProps> = ({ loading }) => {
  return (
    <div className="border-none space-y-5 bg-gray-100 p-5 rounded ">
      <div className=" flex justify-between items-center border-b-2 pb-2">
        <div className=" flex gap-3 items-center flex-1">
          <Skeleton active={loading} className="bg-white w-[40px] h-[40px] rounded-[40px]">
            <div className="p-3 outline bg-white h-4 outline-2 outline-gray-100 rounded-lg"></div>
          </Skeleton>
          <div className="flex-1 space-y-2">
            <Skeleton active={loading} className="bg-white flex-1">
              <div className="p-1 outline bg-white h-3 outline-2 outline-gray-100 rounded-lg"></div>
            </Skeleton>
            <Skeleton active={loading} className="bg-white flex-1 w-[100px]">
              <div className="p-1 outline bg-white h-3 outline-2 outline-gray-100 rounded-lg"></div>
            </Skeleton>

          </div>
        </div>
      </div>

      <div className=" items-center flex justify-between gap-2 rounded-md">
        <Skeleton active={loading} className=" bg-white h-3 rounded-[20px] flex-1">
          <div className="p-1 outline rounded-full outline-2 outline-gray-100 "></div>
        </Skeleton>
        <Skeleton active={loading} className=" bg-white h-3 rounded-[20px] flex-1">
          <div className="p-1 outline rounded-full outline-2 outline-gray-100 "></div>
        </Skeleton>
      </div>
    </div>
  )
};

export default PaymentLoading;
