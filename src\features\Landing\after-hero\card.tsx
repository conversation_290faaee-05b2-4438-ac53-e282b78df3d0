import React from 'react';
import { Translate } from '@/components/translate';
interface CardData {
  image: string;
  flag: string;
  amount?: string;
  utilityBill?: string;
  nameAction?: string;
}

interface CardProps {
  card: CardData;
  index: number;
}

const Card: React.FC<CardProps> = ({ card, index }) => {
  return (
    <div
      className="relative h-[300px] w-full sm:w-[100%] md:w-[350px] sm:h-[350px] bg-cover bg-center rounded-[20px] "
      style={{ backgroundImage: `url(${card.image})` }}
    >
      <div className="absolute top-0 left-0 w-full flex justify-between p-2 sm:p-4">
        <button className="bg-green-500 text-white px-2 py-1 text-sm sm:text-base rounded-md">
          <Translate msgId="home.payWithRaven" />
        </button>
        {index >= 2 && (
          <img src={card.flag} alt="Flag" className="h-4 w-6 sm:h-6 sm:w-8" />
        )}
      </div>

      <div className="absolute bottom-4 right-4 w-auto gap-7 sm:w-auto bg-white bg-opacity-70 p-3 sm:p-4 flex justify-between items-center text-black rounded-[15px] backdrop-blur-md">
        {card.utilityBill && (
          <div className="flex items-center space-x-1 sm:space-x-2">
            {index < 2 && (
              <img
                src={card.flag}
                alt="Flag"
                className="h-4 w-6 sm:h-6 sm:w-8"
              />
            )}
            <span className="text-xs sm:text-base">{card.utilityBill}</span>
          </div>
        )}
        {card.amount && (
          <span className="font-bold text-sm sm:text-xl">{card.amount}</span>
        )}
        {card.nameAction && (
          <span className="text-xs sm:text-base">{card.nameAction}</span>
        )}
      </div>
    </div>
  );
};

export default Card;
