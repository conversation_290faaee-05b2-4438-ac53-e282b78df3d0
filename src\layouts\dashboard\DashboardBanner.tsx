import { Button } from 'components/button';
import { Link } from 'react-router-dom';
import { SimpleText } from 'components/typography';
import { FC } from 'react';
import clsx from 'features/utils/clsx';

interface DashboardBannerProps {
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  action: string;
  href?: string;
}

const DashboardBanner: FC<DashboardBannerProps> = ({
  className,
  children,
  onClick,
  action,
  href,
}) => (
  <div className={clsx('w-full', className)}>
    <div className="rounded-lg bg-cool-gray-100 p-6">
      <div className="flex items-center justify-between flex-wrap">
        <div className="w-auto sm:w-0 mr-3 mb-3 sm:mb-0 flex-1 flex items-center min-w-0">
          <SimpleText className="sm:truncate">{children}</SimpleText>
        </div>
        <div className="w-full sm:w-auto order-3 sm:order-2">
          {href ? (
            <Button
              variant="primary"
              component={Link}
              to={href}
              className="w-full sm:w-auto"
            >
              {action}
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={onClick}
              className="w-full sm:w-auto"
            >
              {action}
            </Button>
          )}
        </div>
      </div>
    </div>
  </div>
);

export default DashboardBanner;
