import { FC, useCallback } from 'react';
import { Skeleton } from 'components/skeleton';
import {
  <PERSON><PERSON>hart,
  CartesianGrid,
  Tooltip,
  YAxis,
  XAxis,
  Line,
  ResponsiveContainer,
} from 'recharts';
import { SimpleText } from 'components/typography';
import EmptyChart from './EmptyChart';
import formatCurrency from 'features/utils/formatCurrency';
import { Translate } from '@/components/translate';
import clsx from 'features/utils/clsx';

interface RevenueChartProps {
  loading: boolean;
  data: Record<string, string | number>[];
  title: string;
}

const RevenueChart: FC<RevenueChartProps> = ({ loading, data = [], title }) => {
  const formatter = useCallback(
    (value: number) => [formatCurrency(value), 'Earnings'],
    []
  );

  return (
    <Skeleton active={loading} className="h-full">
      <div
        className={clsx('rounded relative', {
          'h-full': data.length === 0,
        })}
      >
        <SimpleText truncate className="font-medium text-xl">
          <Translate msgId={title} />
        </SimpleText>
        <div
          className={clsx('h-full aspect-w-16 aspect-h-9', {
            'mt-16': data.length > 0,
          })}
        >
          <div className="flex items-center h-full justify-center">
            {data.length === 0 ? (
              <EmptyChart variant="bar" />
            ) : (
              <ResponsiveContainer minHeight={300}>
                <LineChart data={data}>
                  <CartesianGrid vertical={false} strokeDasharray="1 6" />
                  <Tooltip
                    formatter={formatter}
                    cursor={{ fill: '#F3F4F6' }}
                    labelStyle={{
                      color: '#111827',
                      fontSize: '.875rem',
                      lineHeight: '1.25rem',
                    }}
                    itemStyle={{
                      color: '#6B7280',
                      fontSize: '.875rem',
                      lineHeight: '1.25rem',
                    }}
                    contentStyle={{
                      boxShadow:
                        '0 10px 15px -3px rgba(0, 0, 0, 0.1),0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                      borderRadius: 8,
                      paddingLeft: '1rem',
                      paddingRight: '1rem',
                      paddingTop: '.5rem',
                      paddingBottom: '.5rem',
                    }}
                  />
                  <XAxis stroke="#6B7280" tickLine={false} dataKey="month" />
                  <YAxis stroke="#6B7280" tickLine={false} />
                  <Line dataKey="earning" fill="#128542" />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </div>
      </div>
    </Skeleton>
  );
};

export default RevenueChart;
