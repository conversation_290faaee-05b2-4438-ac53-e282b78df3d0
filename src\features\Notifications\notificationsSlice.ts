import { RootState } from '@/store/store';
import {
    createSelector,
    createSlice,
} from '@reduxjs/toolkit';
// import { injectReducer } from 'services/store';

export type SeverityOptions = 'info' | 'error' | 'success' | 'warning'

export interface NotificationAction {
    type: string,
    text: string,
    payload: Record<string, unknown>,
}

export interface NotificationsState {
    id: number;
    dismissed: boolean;
    severity: SeverityOptions;
    title: string;
    description: string;
    action: NotificationAction;
    dismissable: boolean;
}

const initialState: Record<'notifications', NotificationsState[]> = {
    notifications: []
}

const notificationsSlice = createSlice({
    name: 'notifications',
    initialState,
    reducers: {
        notify: (state, { payload }) => {
            /**
             * Example Payload
             * {
             * 	 severity: 'info',
             * 	 title: 'Test Notification',
             * 	 description: 'This is testing Raven notifications',
             * 	 action: {
             * 	 	 type: 'custom/action',
             * 	 	 text: 'Click me!',
             * 	 	 payload: { foo: 'bar' },
             * 	 }
             * }
            */
            const { severity, title, description, action, dismissable = true } = payload;

            const notification = {
                id: Math.round(Date.now() * Math.random()),
                dismissed: false,
                severity,
                title,
                description,
                action,
                dismissable,
            };

            state.notifications = [notification, ...state.notifications.slice(0, 3)];
        },
        dismiss: (state, action) => {
            const { id, max = 3 } = action.payload;

            state.notifications = [...state.notifications.slice(0, max)];

            if (id) {
                const index = state.notifications.findIndex(notification => notification.id === id);
                state.notifications[index].dismissed = true;
                return;
            }

            const index = state.notifications.slice().reverse().findIndex(notification => {
                return !notification?.dismissed
            });

            const count = state.notifications.length - 1
            const finalIndex = index >= 0 ? count - index : index;

            if (finalIndex !== -1) {
                state.notifications[finalIndex].dismissed = true;
            }
        }
    },
});

const { name, actions } = notificationsSlice;
const { notify, dismiss } = actions;
export { notify, dismiss };

export const getSlice = (state: RootState) => state[name];
export const getNotifications = createSelector(
    getSlice,
    slice => slice?.notifications ?? []
);
export const getActiveNotifications = createSelector(
    getNotifications,
    notifications => notifications.filter((notification: NotificationsState) => !notification?.dismissed)
);

export default notificationsSlice;