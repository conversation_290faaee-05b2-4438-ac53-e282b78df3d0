import UsersTable from './UsersTable';
import { Skeleton } from '@/components/skeleton';
import { FC } from 'react';
import { User } from '@/models/users';

interface UsersProps {
  users: User[]
  loading: boolean
  total: number;
}

const Users: FC<UsersProps> = ({ users, loading, total }) => {

  return (
    <Skeleton active={false}>
      <div>
        <div className="mt-5">
          <UsersTable users={users} loading={loading} total={total} />
        </div>
      </div>
    </Skeleton>
  );
};

export default Users;
