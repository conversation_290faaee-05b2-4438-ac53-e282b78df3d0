import { Button } from '@/components/button'
import { Modal } from '@/components/modal'
import { Translate } from '@/components/translate'
import  { useState } from 'react'
import SelectPaymentRequestType from './SelectPaymentRequestType'
import { Link } from 'react-router-dom'

const GetPaidModal = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [requestType, setRequestType] = useState<string>('payment-link')

  const onClose = () => {
    setIsOpen(false)
  }
  return (
    <div>
      <div>
        <Button variant="darkPrimary" className="w-full lg:w-auto rounded-lg" onClick={()=>setIsOpen(true)}>
          <Translate msgId="dashboard.getPaid" />
        </Button>
      </div>
      <Modal className=' w-96' isOpen={isOpen} onClose={onClose}>
        <div className="p-2 space-y-5">
          <SelectPaymentRequestType requestType={requestType} setRequestType={setRequestType} />
          
          <div className="w-full">
            <Button component={Link} variant='darkPrimary' to='/select-payment-link-plan' className=' block text-center w-full rounded-md mb-2'>
              <Translate msgId='dashboard.next' />
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default GetPaidModal