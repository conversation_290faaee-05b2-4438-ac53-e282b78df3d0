import ATMCard from 'components/atm-card/ATMCard';
import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import rightArrow from '/icons/right-arrow.svg';
import { Button } from 'components/button';
import { Link } from 'react-router-dom';

const ExclusiveCards = () => {
  return (
    <section className="mt-40">
      <div>
        <div className="md:flex md:flex-wrap md:items-center md:justify-between md:py-28">
          <div className="text-left md:px-10 space-y-10 xl:w-1/2 w-full">
            <SimpleText
              component="h2"
              className="text-6xl font-extrabold leading-normal"
            >
              <Translate msgId="home.exclusiveCards" />
            </SimpleText>
            <SimpleText>
              <Translate msgId="home.exclusiveCardsDescription" />
            </SimpleText>
            <div>
              <Button
                variant="transparent"
                component={Link}
                to="/get-a-card"
                className="inline-flex items-center space-x-3"
              >
                <SimpleText className="text-lg">
                  <Translate msgId="home.getCard" />
                </SimpleText>
                <img src={rightArrow} alt="right arrow" className="w-5" />
              </Button>
            </div>
          </div>
          <div className="relative">
            <ATMCard className="absolute right-20 top-10 light-green-card atm-card-rotated" />
            <ATMCard className="relative right-20 dark-green-card atm-card-rotated" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExclusiveCards;
