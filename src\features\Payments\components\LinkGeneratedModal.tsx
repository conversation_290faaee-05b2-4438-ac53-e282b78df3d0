import { Button } from '@/components/button'
import { Modal } from '@/components/modal'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'
import {  ClipboardCopyOutline } from 'heroicons-react'
import  { Dispatch, FC, SetStateAction } from 'react'
import { Link } from 'react-router-dom'

interface LinkGeneratedModalProps {
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
}

const LinkGeneratedModal:FC<LinkGeneratedModalProps> = ({open = false, setOpen}) => {

  return (
    <div>
      <Modal isOpen={open} onClose={()=> setOpen(false)}>
        <div className="p-2 text-center space-y-5 flex flex-col items-center justify-center">
          <img src="/images/check-circle.png" alt="upload" className=" w-14 h-14"/>

          <SimpleText component="h1" className=" text-lg font-semibold">
            <Translate msgId='dashboard.linkGenerated' />
          </SimpleText>
          <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
            <Translate msgId='dashboard.linkSent' />
          </SimpleText>

          <div className="flex justify-between flex-wrap  items-center gap-2 w-full bg-gray-100 p-2 rounded-lg">
            <SimpleText className=" text-sm">
              https://raven.payment.com/payment58231
            </SimpleText>
            <ClipboardCopyOutline className='text-gray-400 cursor-pointer' />
          </div>
          <Button component={Link} to='/dashboard' variant='darkPrimary' className='w-full rounded-md'>
            <Translate msgId='dashboard.goHome' />
          </Button>
        </div>
      </Modal>
    </div>
  )
}

export default LinkGeneratedModal