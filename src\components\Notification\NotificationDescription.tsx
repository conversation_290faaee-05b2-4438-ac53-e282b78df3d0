import clsx from '@/features/utils/clsx';
import { FC, ReactNode } from 'react';

interface NotificationDescriptionProps {
  className?: string;
  children: ReactNode;
}

const NotificationDescription: FC<NotificationDescriptionProps> = ({
  className = '',
  children,
}) => {
  const rootClass = clsx('mt-1 text-sm text-gray-500', className);

  return (
    <div className={rootClass}>
      <p>{children}</p>
    </div>
  );
};

export default NotificationDescription;
