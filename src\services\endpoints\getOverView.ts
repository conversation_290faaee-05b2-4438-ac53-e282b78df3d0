import rtkQueryBase from "../api/rtk-query-base";


const extendedAPI = rtkQueryBase.injectEndpoints({
    overrideExisting: true,
    endpoints: (builder) => ({
        getOverview: builder.query({
            query: () => '/api/business/overview',
        }),
        getMonthlyOverview: builder.query({
            query: () => '/api/business/stats'
        }),
        getBusinessStats: builder.query({
            query: () => '/api/business/stats/all'
        }),
        getTransactionsHistory: builder.query({
            query: (params) => ({
                url: '/api/tranactions/history',
                params
            })
        }),
        getCustomerOverview: builder.query({
            query: () => '/api/customers'
        })
    })
});

export const { useGetOverviewQuery, useGetMonthlyOverviewQuery, useGetTransactionsHistoryQuery, useGetCustomerOverviewQuery, useGetBusinessStatsQuery } = extendedAPI;

export default extendedAPI;
