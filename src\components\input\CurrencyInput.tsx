/**
 * @module CurrencyInput
 */

import { FC, ChangeEvent, ChangeEventHandler } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { currencyList } from '@/models/currency-object.interface';
import {
  getConvertorInput,
  getSelectedBaseCurrency,
  getSelectedReceivingCurrency,
  setBaseCurrency,
  setConvertingInput,
  setBaseInput,
  setReceivingCurrency,
} from '@/features/Landing/exchange-app/exchangeAppSlice';
import { AppDispatch } from '@/store/store';
import useCurrencyConvertor from '@/hooks/use-currency-convertor';

/**
 * Props interface for the CurrencyInput Component.
 * @interface CurrencyInputProps
 * @property {boolean} [isConvertor] - Determines if the currency input component is a convertor.
 */
interface CurrencyInputProps {
  isConvertor: boolean;
  index: number;
}

/**
 * Functional Component to render a Currency Input along with associated currency selector.
 * @function CurrencyInput
 * @param {CurrencyInputProps} props - Props passed to the component.
 * @returns {JSX.Element} Rendered CurrencyInput component.
 */
const CurrencyInput: FC<CurrencyInputProps> = ({
  isConvertor,
  index,
}: CurrencyInputProps): JSX.Element => {
  // Dispatch hook to dispatch actions.
  const dispatch: AppDispatch = useDispatch();
  const convertorInputValue = useSelector(getConvertorInput);
  const selectedBaseCurrency = useSelector(getSelectedBaseCurrency);
  const selectedReceivingCurrency = useSelector(getSelectedReceivingCurrency);

  const { exchangeRate } = useCurrencyConvertor();

  /**
   * Handles the change event on the currency selector.
   * @function handleCurrencySelect
   * @param {ChangeEvent<HTMLSelectElement>} event - The change event object.
   */
  const handleCurrencySelect: ChangeEventHandler<HTMLSelectElement> = (
    event: ChangeEvent<HTMLSelectElement>
  ) => {
    const currency = currencyList.filter(
      (currency) => currency.id === event.target.value
    )?.[0];
    isConvertor
      ? dispatch(setBaseCurrency(currency))
      : dispatch(setReceivingCurrency(currency));
  };

  /**
   * Handles the input change event on the currency input.
   * @function handleInputChange
   * @param {ChangeEvent<HTMLInputElement>} event - The input change event object.
   */
  const handleInputChange: ChangeEventHandler<HTMLInputElement> = async (
    event: ChangeEvent<HTMLInputElement>
  ) => {
    const { value } = event.target;

    isConvertor
      ? dispatch(setBaseInput(value))
      : dispatch(setConvertingInput({ value, id: index }));
  };

  return (
    <div>
      <div className="relative mt-2 rounded-md shadow-sm">
        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 w-auto">
          <span className="text-gray-500">
            {isConvertor
              ? selectedBaseCurrency.sign
              : selectedReceivingCurrency.sign}
          </span>
        </div>
        <input
          type="number"
          name="price"
          id="price"
          min={0}
          className="block h-16 w-full font-bold rounded-md border-0 py-1.5 pl-7 pr-20 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:leading-6 dark:bg-gray-700 dark:ring-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
          placeholder="0.00"
          onChange={handleInputChange}
          value={isConvertor ? convertorInputValue || '' : exchangeRate || ''}
        />
        <div className="absolute inset-y-0 right-0 flex items-center">
          <label htmlFor="currency" className="sr-only">
            Currency
          </label>
          <select
            id="currency"
            name="currency"
            onChange={handleCurrencySelect}
            defaultValue={
              isConvertor
                ? selectedBaseCurrency.id
                : selectedReceivingCurrency.id
            }
            className="h-full rounded-md border-0 bg-transparent py-0 pl-2 pr-7 text-gray-500 focus:ring-2 focus:ring-inset focus:ring-indigo-600"
          >
            {currencyList.map((currency) => (
              <option value={currency.id} key={currency.id}>
                {currency.flag} {currency.id}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
};

export default CurrencyInput;
