import { useSelector } from 'react-redux';
import { getOption } from '../paymentsSlice';
import { useMemo } from 'react';
import { paymentOptionStrings } from '@/models/payment-option';
import MomoDepositForm from './forms/MomoDepositForm';
import CardDepositForm from './forms/CardDepositForm';

const SelectedDepositForm = () => {
  const selectedOption = useSelector(getOption);

  const Component = useMemo(() => {
    switch (selectedOption) {
      case paymentOptionStrings.card:
        return CardDepositForm;
      case paymentOptionStrings.bank:
        return CardDepositForm;
      case paymentOptionStrings.momo:
        return MomoDepositForm;

      default:
        return 'div';
    }
  }, [selectedOption]);

  return selectedOption && <Component />;
};

export default SelectedDepositForm;
