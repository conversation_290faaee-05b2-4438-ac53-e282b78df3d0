import { createAsyncThunk, createSlice, createSelector } from "@reduxjs/toolkit";
import api, { cancelToken } from "@/services/api/api";
import { RootState } from "@/store/store";
import { SigninFormData } from "@/models/signin-form-data";
import { ResponseError } from "@/models/response-error.interface";
import getErrors from "features/utils/get-errors";
import { removeToken } from "features/utils/cookie";

/**
 * Async thunk to perform user login.
 * @param options - Contains the payload for the request.
 * @param param2 - The async thunk API methods.
 * @returns A Promise containing the login response data.
 */
export const loginUser = createAsyncThunk('loginUser', async (options: { data: SigninFormData }, { signal, rejectWithValue }) => {

    const source = cancelToken.source();

    signal.addEventListener('abort', () => {
        source.cancel();
    });

    try {
        const { password, email } = options.data

        const response = await api.post('/users/login', { password, email }, {
            cancelToken: source.token
        })

        return response.data.user
    } catch (error) {
        return rejectWithValue(getErrors(error))
    }
})

interface signinState {
    loading: boolean
    error: ResponseError | null,
    redirect: boolean
}

const initialState: signinState = {
    loading: false,
    error: null,
    redirect: false
}

export const loginSlice = createSlice({
    initialState,
    name: 'login',
    reducers: {
        signout: () => {
            removeToken()
        }
    },
    extraReducers: (builder) => {
        builder.addCase(loginUser.pending, (state) => {
            state.loading = true
            state.redirect = false
            state.error = null
        })
            .addCase(loginUser.fulfilled, (state, action) => {
                state.loading = false
                state.error = null
                sessionStorage.setItem('@payload', JSON.stringify(action.payload))
                state.redirect = true

            }).addCase(loginUser.rejected, (state, action) => {
                state.error = action.payload as ResponseError
                state.loading = false
                state.redirect = false
            })
    }
});

export const { name, actions } = loginSlice;

export const { signout } = actions


/**
 * Selector function to get the login slice from the Redux state.
 *
 * @param {RootState} state - The root Redux state.
 * @returns {InitialState} - The login slice from the Redux state.
 */
const getSlice = (state: RootState) => state[name]

//Selector: Get the loading state.
export const getLoading = createSelector(getSlice, (slice) => slice?.loading)

// Selector: Get the error state
export const getError = createSelector(getSlice, (slice) => slice?.error)

// Selector: Get the redirect state
export const getRedirect = createSelector(getSlice, (slice) => slice?.redirect || false)