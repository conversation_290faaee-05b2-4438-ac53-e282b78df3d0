// import { useEffect } from 'react';
import { useCallback } from 'react';
import Circle from './Circle';

const GenerateBackgroundCircles = () => {
  const circleCount = 6; // Number of circles on background
  const circles = Array.from({ length: circleCount }, (_, index) => index);

  const generateRandomValue = useCallback((min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }, []);

  //   useEffect(() => {
  //     // To make the circles appear on the entire body, not just the part where content exists
  //     document.body.style.height = '100vh';
  //   }, []);

  return circles.map((_, index) => {
    // Subtracting maximum possible diameter to avoid overflow
    const maxLeft = 100 - 40; // 40 is the maximum diameter in percentage

    const top = `${generateRandomValue(0, 100)}%`;
    const left = `${generateRandomValue(0, maxLeft)}%`;
    const diameter = `${generateRandomValue(10, 40)}rem`;

    return (
      <Circle
        key={index}
        top={top}
        left={left}
        diameter={diameter}
        opacity={0.05}
        color="#11E342"
      />
    );
  });
};

export default GenerateBackgroundCircles;
