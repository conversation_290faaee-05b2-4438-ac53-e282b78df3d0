import { Translate } from '@/components/translate';
import EarningOverviewChart from '@/features/Dashboard/Chart/EarningOverviewChart';
import Overview from '@/features/Dashboard/Overview/Overview';
import RecentTransactions from '@/features/Dashboard/RecentTransactions/RecentTransactions';
import PaymentMethodPreview from '@/features/Payments/PaymentMethodPreview';
import QuickPayments from '@/features/Payments/QuickPayments';
import { DashboardContent, DashboardGrid } from '@/layouts/dashboard';
import DashboardHeading from '@/layouts/dashboard/DashboardHeading';
import { Skeleton } from 'components/skeleton';

const DashboardHome = () => {
  return (
    <Skeleton active={false}>
      <DashboardContent>
        <DashboardHeading className="mb-5">
          <Translate msgId="dashboard.dashboard" />
        </DashboardHeading>
        <DashboardGrid
          className="grid-flow-row-dense"
          gap="gap-3 md:gap-4"
          columns="grid-cols-3"
        >
          <DashboardGrid
            item
            span="md:col-span-2 col-span-3 md:col-start-1 md:col-end-3"
          >
            <Overview />
          </DashboardGrid>
          <DashboardGrid
            item
            span="md:col-span-1 col-span-3"
            className="md:row-start-1 md:row-end-3 md:col-start-3 md:col-end-4"
          >
            <PaymentMethodPreview />
          </DashboardGrid>
          <DashboardGrid
            item
            span="md:col-span-2 col-span-3 md:col-start-1 md:col-end-3"
            className="rounded"
          >
            <EarningOverviewChart />
          </DashboardGrid>
          <DashboardGrid
            item
            span="md:col-span-1 col-span-3 md:col-start-3 md:col-end-4"
          >
            <QuickPayments />
          </DashboardGrid>
          <DashboardGrid
            item
            span="md:col-span-2 col-span-3 md:col-start-1 md:col-end-3"
          >
            <RecentTransactions />
          </DashboardGrid>
        </DashboardGrid>
      </DashboardContent>
    </Skeleton>
  );
};

export default DashboardHome;
