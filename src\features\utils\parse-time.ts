/**
 * Formats and returns the current time of the day
 * based on the received timestamp from the API.
 *
 * @returns {string} - The time of the day string in HH:mm format.
 */
export const getTimeFromSeconds = (timestamp: number) => {
    const options: Intl.DateTimeFormatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    };
    const date = new Date(timestamp * 1000);
    const timeOfDay = date.toLocaleTimeString('en-US', options);

    return timeOfDay
}