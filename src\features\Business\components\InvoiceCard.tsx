import { Button } from '@/components/button';
import { Skeleton } from '@/components/skeleton';
import { Translate } from '@/components/translate';

const InvoiceCard = () => {
  return (
    <div className='relative h-full -mt-16 '>
      <div className="px-4 py-2 border bg-white rounded-lg mb-5 text-sm ">
        <div className="flex justify-between items-center">
          <Skeleton active={true} className="w-20">
            <div className="h-2"></div>
          </Skeleton>
          <Skeleton active={true} className="w-28 mt-2">
            <div className="h-2"></div>
          </Skeleton>
        </div>
        <div className="flex justify-between items-center">
          <Skeleton active={true} className="w-20">
            <div className="h-2"></div>
          </Skeleton>
          <Skeleton active={true} className="w-28 mt-2">
            <div className="h-2"></div>
          </Skeleton>
        </div>
      </div>
      <div className="px-4 py-2 border bg-white rounded-lg mb-3 text-sm ">
        <div className="mb-2">
          <div className="flex justify-between items-center">
            <Skeleton active={true} className="w-20">
              <div className="h-2"></div>
            </Skeleton>
            <Skeleton active={true} className="w-28 mt-2">
              <div className="h-2"></div>
            </Skeleton>
          </div>
          <div className="flex justify-between items-center">
            <Skeleton active={true} className="w-10">
              <div className="h-2"></div>
            </Skeleton>
            <Skeleton active={true} className=" w-16 mt-2">
              <div className="h-2"></div>
            </Skeleton>
          </div>
        </div>
        <div className="mb-2">
          <div className="flex justify-between items-center">
            <Skeleton active={true} className="w-20">
              <div className="h-2"></div>
            </Skeleton>
            <Skeleton active={true} className="w-28 mt-2">
              <div className="h-2"></div>
            </Skeleton>
          </div>
          <div className="flex justify-between items-center">
            <Skeleton active={true} className="w-10">
              <div className="h-2"></div>
            </Skeleton>
            <Skeleton active={true} className=" w-16 mt-2">
              <div className="h-2"></div>
            </Skeleton>
          </div>
        </div>
        <div className="mb-2">
          <div className="flex justify-between items-center">
            <Skeleton active={true} className="w-20">
              <div className="h-2"></div>
            </Skeleton>
            <Skeleton active={true} className="w-28 mt-2">
              <div className="h-2"></div>
            </Skeleton>
          </div>
          <div className="flex justify-between items-center">
            <Skeleton active={true} className="w-10">
              <div className="h-2"></div>
            </Skeleton>
            <Skeleton active={true} className=" w-16 mt-2">
              <div className="h-2"></div>
            </Skeleton>
          </div>
        </div>
      </div>
      <div className="justify-center flex items-center  w-full mt-10 bottom-0 ">
        <Button variant="darkPrimary" className="rounded-lg !py-2">
          <Translate msgId={'business.payInvoice'} />
        </Button>
      </div>
    </div>
  );
};

export default InvoiceCard;
