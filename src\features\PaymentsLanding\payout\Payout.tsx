import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { Button } from '@/components/button';
import PayoutTable from '../components/PayoutTable';

const Payout = () => {
  return (
    <section className="mt-10 md:py-10 md:px-10">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
        <div className="space-y-3 order-last lg:order-first w-full dark:text-white text-gray-800 flex items-center justify-center md:pr-16 mb-10 md:mb-0">
          <div className=' space-y-5 md:space-y-10 px-10 py-5'>
            <SimpleText className='text-raven-green-800 text-2xl font-bold'>
              <Translate msgId="landing.payout" />
            </SimpleText>

            <SimpleText>
              <Translate msgId="landing.payoutDes1" />
            </SimpleText>
            <SimpleText>
              <Translate msgId="landing.payoutDes2" />
            </SimpleText>

            <Button variant='darkPrimary' className='rounded-md'>
              <Translate msgId="landing.contactSales" />
            </Button>

          </div>
        </div>
        <div className=" w-full p-5 md:p-12 rounded-3xl">
          <PayoutTable />
        </div>
      </div>
    </section>
  );
};

export default Payout;
