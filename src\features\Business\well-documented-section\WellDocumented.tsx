import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import reasons from '@/data/business-apis.json';
import { TextSecondary } from '@/components/typography/Text';
import { Trans } from 'react-i18next';
import { Button } from '@/components/button';
import { Link } from 'heroicons-react';

const WellDocumented = () => {
  return (
    <div className="mt-20 md:py-20 md:px-10">
      <div className="mb-10 space-y-4 text-center lg:px-20">
        <SimpleText
          component="h2"
          className="font-semibold text-3xl md:text-5xl"
        >
          <Translate msgId="business.apiTitle" />
        </SimpleText>
        <TextSecondary size="lg" shade="light">
          <Trans
            i18nKey="business.apiDesc"
            components={{
              span: <span className="text-raven-green-800" />,
            }}
          />
        </TextSecondary>
      </div>
      <div className="grid gap-3 md:gap-5 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {reasons.map((feature, ind) => (
          <div
            key={ind}
            className=" flex gap-3 items-center md:justify-center border rounded-md p-3"
          >
            <div className={'flex justify-center w-20 h-20 rounded-full p-5 '}>
              <img src={feature.icon} alt={feature.name} />
            </div>
            <SimpleText className="font-semibold">
              <Translate msgId={feature.title} />
            </SimpleText>
          </div>
        ))}
      </div>
      
      <div className="mt-6 flex justify-center">
        <Button
          variant="darkPrimary"
          className="flex gap-5 items-center rounded-md"
        >
          <div className="bg-white flex items-center p-1 justify-center h-6 w-6 rounded-full">
            <Link className="h-6 w-6 text-gray-500" />
          </div>
          <Translate msgId="business.apiStartIntegration" />
        </Button>
      </div>
    </div>
  );
};

export default WellDocumented;
