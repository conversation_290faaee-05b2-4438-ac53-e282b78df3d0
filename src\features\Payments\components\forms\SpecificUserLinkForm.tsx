import CountrySelector from '@/components/selector/CountrySelector';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { COUNTRIES } from '@/data/countries';
import { SelectMenuOption } from '@/data/types';
import { SpecificUserLinkFormValues } from '@/models/payment-option';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import LinkGeneratedModal from '../LinkGeneratedModal';
import { Button } from '@/components/button';
import { FormLabel } from '@/components/form';

const SpecificUserLinkForm = () => {
  const [country, setCountry] = useState('GH');
  const [isOpen, setIsOpen] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SpecificUserLinkFormValues>();

  const [isComplete, setIsComplete] = useState(false);

  const handleGeneratePaymentLink: SubmitHandler<SpecificUserLinkFormValues> = (
    data
  ) => {
    // Handle link generation
    console.log(data);
    setIsComplete(true);
  };

  return (
    <form
      onSubmit={handleSubmit(handleGeneratePaymentLink)}
      className="space-y-2"
    >
      <div className=" w-full ">
        <FormLabel name='customerEmail' text='dashboard.customerEmail' required />
        <input
          className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
          type="text"
          placeholder="<EMAIL>"
          {...register('email', { required: true })}
        />
        {errors.email && (
          <SimpleText className=" text-xs text-red-500">
            <Translate msgId="dashboard.emailRequired" />
          </SimpleText>
        )}
      </div>
      <div className=" w-full ">
        <FormLabel name='accountName' text='dashboard.accountName' required />
        <input
          className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
          type="text"
          placeholder="Kofi Benjamin"
          {...register('accountName', { required: true})}
        />
        {errors.accountName && (
          <SimpleText className=" text-xs text-red-500">
            <Translate msgId="dashboard.accountNameRequired" />
          </SimpleText>
        )}
      </div>
      <div className=" w-full ">
        <FormLabel name='accNumber' text='dashboard.accNumber' required />
        <input
          className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
          type="text"
          placeholder="(XXX) XXX-XXX-XXX"
          {...register('accountNumber', {
            required: true,
          })}
        />
        {errors.accountNumber && (
          <SimpleText className=" text-xs text-red-500">
            <Translate msgId="dashboard.accountNumberRequired" />
          </SimpleText>
        )}
      </div>
      <div className="flex gap-5">
        <div className=" w-full ">
          <FormLabel name='amount' text='dashboard.amount' required />
          <input
            className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
            type="text"
            placeholder="GHS 000.00"
            {...register('amount', { required: true })}
          />
          {errors.amount && (
            <SimpleText className=" text-xs text-red-500">
              <Translate msgId="dashboard.amountRequired" />
            </SimpleText>
          )}
        </div>
        <div className=" w-full ">
          <FormLabel name='description' text='dashboard.currency' />
          <CountrySelector
            id={'countries'}
            open={isOpen}
            onToggle={() => setIsOpen(!isOpen)}
            onChange={(event) => setCountry(event)}
            selectedValue={
              COUNTRIES.find(
                (option) => option.value === country
              ) as SelectMenuOption
            }
          />
        </div>
      </div>
      <div className=" w-full ">
        <FormLabel name='description' text='dashboard.description' />
        <textarea
          className=" h-28 w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
          placeholder="Description"
          {...register('description')}
        />
      </div>

      <Button variant="darkPrimary" className="rounded-lg w-full mt-5">
        <Translate msgId="dashboard.createLink" />
      </Button>
      <LinkGeneratedModal open={isComplete} setOpen={setIsComplete} />
    </form>
  );
};

export default SpecificUserLinkForm;
