import { Button } from '@/components/button';
import { Text } from '@/components/typography';
import cards from '@/data/card-types.json';
import { FormLabel } from '@/components/form';
import { Translate } from '@/components/translate';
import { useDispatch, useSelector } from 'react-redux';
import { ChangeEvent, FormEvent } from 'react';
import { getCardField, setCardFields } from '../paymentsSlice';
import { CardFieldsType } from '@/models/payment-option';

const CardDetailsForm = () => {
  const dispatch = useDispatch();
  const cardFields = useSelector(getCardField);

  const handleChange =
    (name: CardFieldsType) =>
    (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      dispatch(setCardFields({ [name]: e.target.value }));
    };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-10">
        <FormLabel name="cardType" text="dashboard.selectCard" />
        <select
          name="cardType"
          onChange={handleChange('cardType')}
          className="border bg-slate-50 w-full border-gray-100 rounded"
        >
          {!cardFields['cardType'] && <option>Select Your Card Type</option>}
          {cards.map((card) => (
            <option value={card.value} key={card.name}>
              <Translate msgId={card.name} />
            </option>
          ))}
        </select>
      </div>
      <div className="mb-10">
        <FormLabel name="cardNumber" text="dashboard.cardNumber" />
        <input
          type="text"
          name="cardNumber"
          value={cardFields['cardNumber']}
          onChange={handleChange('cardNumber')}
          className="border bg-slate-50 w-full border-gray-100 rounded"
          placeholder="XXXX XXXXX XXXXX XXXX"
        />
      </div>
      <div className="mb-10 grid grid-cols-2 gap-10">
        <div>
          <FormLabel name="expiryDate" text="dashboard.expiryDate" />
          <input
            type="text"
            value={cardFields['expiryDate']}
            name="expiryDate"
            onChange={handleChange('expiryDate')}
            placeholder="26/28"
            className="border bg-slate-50 w-full border-gray-100 rounded"
          />
        </div>
        <div>
          <FormLabel name="cvv" text="dashboard.cvv" />
          <input
            type="text"
            value={cardFields['cvv']}
            name="cvv"
            onChange={handleChange('cvv')}
            className="border bg-slate-50 w-full border-gray-100 rounded"
            placeholder="XXX"
          />
        </div>
      </div>
      <Button variant="darkPrimary" className="w-full rounded-lg">
        <Text size="sm" shade="white">
          <Translate msgId="dashboard.addCard" />
        </Text>
      </Button>
    </form>
  );
};

export default CardDetailsForm;
