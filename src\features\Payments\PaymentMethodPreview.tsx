import RavenCard from '@/components/atm-card/RavenCard';
import { Button } from 'components/button';
import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import { Link } from 'react-router-dom';
import useGetPaymentMethods from './hooks/useGetPaymentMethods';
import { Skeleton } from '@/components/skeleton';
import NoCards from './components/NoCards';

interface PaymentMethodsUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: {
    paymentMethods: Record<string, unknown>[];
    cards: Record<string, unknown>[];
  };
}

const PaymentMethodPreview = () => {
  const { cards, paymentMethods, loading } = useGetPaymentMethods({
    select: ({ isLoading, isUninitialized, data }: PaymentMethodsUtils) => ({
      loading: isLoading || isUninitialized,
      cards: data?.cards || [],
      paymentMethods: data?.paymentMethods || [],
    }),
  });

  return (
    <div className="bg-white p-5">
      <div className="mb-3">
        <SimpleText component="h3" className="font-semibold">
          <Translate msgId="dashboard.paymentMethods" />
        </SimpleText>
      </div>
      <Skeleton active={loading} className="mb-5">
        {cards.length > 0 ? (
          <div className="mb-5 relative">
            <RavenCard />
          </div>
        ) : (
          <NoCards />
        )}
      </Skeleton>
      <Button
        component={Link}
        to="/dashboard/payments/payment-methods"
        variant="dark"
        className="rounded-lg w-full inline-block text-center text-sm"
      >
        <Translate msgId="dashboard.addPayment" />
      </Button>
      {paymentMethods.length > 0 ? <>Payment methodds</> : null}
    </div>
  );
};

export default PaymentMethodPreview;
