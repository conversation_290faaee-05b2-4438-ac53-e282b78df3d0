import { FC } from 'react';
import { ButtonVariants } from '@/models/button-props.interface';
import { Button } from 'components/button';
import { Translate } from 'components/translate';
import { SimpleText } from 'components/typography';
import clsx from '@/features/utils/clsx';

interface FeatureCardsProps {
  variant?: ButtonVariants;
  icon: string;
  buttonText?: string;
  description: string;
  title?: string;
  name: string;
  color?: string;
  noButton?: boolean;
}
const FeatureCard: FC<FeatureCardsProps> = ({
  variant,
  buttonText,
  description,
  icon,
  name,
  title,
  color,
  noButton = false,
}) => {
  return (
    <div className="rounded-3xl bg-white dark:bg-gray-900 border shadow-lg text-center py-10 px-10">
      <div className="space-y-10">
        <div
          className={clsx('flex justify-center', {
            [`w-20 h-20 rounded-full p-5 m-auto`]: noButton,
            [`${color}`]: noButton,
          })}
        >
          <img src={icon} alt={name} />
        </div>
        <div className="space-y-6">
          {title && (
            <SimpleText className="font-bold">
              <Translate msgId={title} />
            </SimpleText>
          )}
          <div>
            <SimpleText>
              <Translate msgId={description} />
            </SimpleText>
          </div>
        </div>
        {!noButton && variant && buttonText && (
          <Button variant={variant} className="rounded-3xl">
            <Translate msgId={buttonText} />
          </Button>
        )}
      </div>
    </div>
  );
};

export default FeatureCard;
