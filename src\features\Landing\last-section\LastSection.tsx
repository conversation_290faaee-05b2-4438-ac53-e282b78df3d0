import RavenCard from '@/components/atm-card/RavenCard';
import { motion } from 'framer-motion';
const LastSection = () => {
  return (
    <div className="md:pb-20 mt-20 md:py-20 md:px-10">
      <div className="last-section-bg rounded-4xl">
        <div className="flex items-center justify-between">
          <div className="relative">
            <motion.div
              className="absolute"
              initial={{ top: '70px' }}
              animate={{ top: '1px' }}
              transition={{ delay: 1.5, duration: 1.5 }}
            >
              <RavenCard />
            </motion.div>
          </div>
          <div className="p-20 text-white">Get</div>
        </div>
      </div>
    </div>
  );
};

export default LastSection;
