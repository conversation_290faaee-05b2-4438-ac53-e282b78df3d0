import { Translate } from "@/components/translate"
import Transactions from "@/features/Transactions/components/Transactions"
import useGetTransactions from "@/features/Transactions/hooks/useGetTransactions";
import { Transaction } from "@/models/transactions";


interface TransactionsUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: { transactions: Transaction[], total: number };
}
const TransactionsPage = () => {
  const limit = 10;
  const page = 1;

  const { loading, transactions, total } = useGetTransactions({
    page,
    limit,
    select: ({ isLoading, isUninitialized, data }: TransactionsUtils) => ({
      loading: isLoading || isUninitialized,
      transactions: data?.transactions ?? [],
      total: data?.total ?? 0,
    }),
  });


  return <Transactions transactions={transactions} total={total} loading={loading}  page={<Translate msgId="dashboard.transactions" />} />
}

export default TransactionsPage