import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"
import AdminHeader from "@/features/Admin/AdminHeader"
import Users from "@/features/Admin/components/Users"
import useGetUsers from "@/features/Admin/hooks/useGetUsers"
import { DashboardContent, DashboardGrid } from "@/layouts/dashboard"
import DashboardHeading from "@/layouts/dashboard/DashboardHeading"
import { User } from "@/models/users"


interface UsersUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: { users: User[], total: number };
}

const AdminPage = () => {
  const limit = 10;
  const page = 1;

  const { loading, users, total } = useGetUsers({
    page,
    limit,
    select: ({ isLoading, isUninitialized, data }: UsersUtils) => ({
      loading: isLoading || isUninitialized,
      users: data?.users ?? [],
      total: data?.total ?? 0,
    }),
  });

  console.log({loading, users, total});
  return (
    <DashboardContent>
      <DashboardHeading>
        <Translate msgId="dashboard.userManagement" />
      </DashboardHeading>
      <DashboardGrid columns="grid-cols-1">
        <SimpleText>
          <Translate msgId="dashboard.adminSubTitle" />
        </SimpleText>

        <AdminHeader />
        <Users loading={loading} users={users} total={total} />
      </DashboardGrid>
    </DashboardContent>
  )
}

export default AdminPage