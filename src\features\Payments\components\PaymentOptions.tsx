import { Card, CardContent, CardHeader } from '@/components/Card';
import { Translate } from '@/components/translate';
import { Text } from '@/components/typography';
import masterCard from '/icons/mastercard-logo.png';
import momo from '/icons/momo-icon.png';
import ravenLogo from '/images/logo-header.png';
import visa from '/icons/visa-vector.svg';
import bank from '/icons/merchant-account.png';
import clsx from '@/features/utils/clsx';
import { ChangeEvent } from 'react';
import { PaymentOptionsType } from '@/models/payment-option';
import { useDispatch, useSelector } from 'react-redux';
import { getOption, setOption } from '@/features/Payments/paymentsSlice';

interface PaymentOptionType {
  name: string;
  value: PaymentOptionsType;
  icons: string[];
  id: number;
}

const paymentOptions: PaymentOptionType[] = [
  {
    id: 1,
    name: 'dashboard.card',
    value: 'card',
    icons: [masterCard, visa, ravenLogo],
  },
  {
    id: 2,
    name: 'dashboard.bankPayment',
    value: 'bank_payment',
    icons: [bank],
  },
  {
    id: 3,
    name: 'dashboard.momo',
    value: 'mobile_money',
    icons: [momo],
  },
];

const PaymentOptions = () => {
  const dispatch = useDispatch();
  const selectedOption = useSelector(getOption);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    dispatch(setOption(e.target.value as PaymentOptionsType));
  };

  return (
    <Card variant="outlined" className="w-full bg-white">
      <CardHeader
        className="bg-gray-100"
        primary={<Translate msgId="dashboard.paymentMethods" />}
        secondary={<Translate msgId="dashboard.chooseMethod" />}
      />
      <CardContent className="bg-white" gutterTop>
        <ul
          className={clsx('grid gap-10 lg:gap-6 grid-cols-1 py-20', {
            'xl:grid-cols-2': !selectedOption,
          })}
        >
          {paymentOptions.map((option) => (
            <li key={option.id}>
              <input
                type="radio"
                id={option.value}
                name="payment-option"
                value={option.value}
                className="hidden peer"
                required
                onChange={handleChange}
                checked={option.value === selectedOption}
              />
              <label
                htmlFor={option.value}
                className={
                  'inline-flex p-5 xl:p-10 shadow-lg items-center justify-between w-full text-gray-500 bg-white raven-outline rounded-lg cursor-pointer dark:hover:text-gray-300 dark:peer-checked:text-raven-green-500 peer-checked:outline-raven-green-800 peer-checked:text-raven-green-800 hover:text-gray-600 hover:outline-raven-green-800  dark:text-gray-400 dark:bg-gray-800'
                }
              >
                <div className="flex items-center gap-2 xl:gap-5">
                  <div className="w-5 h-5 rounded-full bg-transparent radio-border border inline-flex items-center justify-center">
                    <div className="w-3 h-3 rounded-full bg-gray-200 radio-button" />
                  </div>
                  <div className="block">
                    <div className="w-full font-semibold">
                      <Text size="md" shade="dark" className="radio-text">
                        <Translate msgId={option.name} />
                      </Text>
                    </div>
                  </div>
                </div>
                <div className="inline-flex items-center gap-1 lg:gap-3">
                  {option.icons.map((icon) => (
                    <img
                      key={icon}
                      className={clsx({
                        'xl:h-3 h-2': option.value === 'card',
                        'xl:w-5 w-3': option.value !== 'card',
                      })}
                      src={icon}
                      alt={`${option.name} icon`}
                    />
                  ))}
                </div>
              </label>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default PaymentOptions;
