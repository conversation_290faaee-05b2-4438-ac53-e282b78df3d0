import { Button } from '@/components/button'
import { Modal } from '@/components/modal'
import { Translate } from '@/components/translate'
import { Plus } from 'heroicons-react'
import { useState } from 'react'
import AddUserForm from './steps/AddUser'
import SelectUserRole from './steps/SelectUserRole'

const AddUserModal = () => {
  const [open, setOpen] = useState(false)
  const [step, setStep] = useState(1)

  const renderForm =(step: number)=> {
    switch (step) {
      case 1:
        return <AddUserForm />
        break;
      case 2:
        return <SelectUserRole />
      default:
        return <AddUserForm />
        break;
    }
  }
  return (
    <div>
      <Button onClick={() => setOpen(true)} variant='darkPrimary' className='rounded-md flex gap-2 whitespace-nowrap'>
        <Plus />
        <Translate msgId='dashboard.addUser' />
      </Button>
      <Modal isOpen={open} onClose={() => setOpen(false)}>
        <div className="px-5 space-y-5">
          {renderForm(step)}

          <div className="flex gap-5 w-2/3">
            <Button variant='light' className='rounded-md ' onClick={() => setOpen(false)}>
              <Translate msgId='dashboard.cancel' />
            </Button>
            <Button variant='darkPrimary' className='rounded-md flex-1' onClick={() => setStep(2)}>
              <Translate msgId='dashboard.next' />
            </Button>
          </div>
        </div>

      </Modal>
    </div>
  )
}

export default AddUserModal
