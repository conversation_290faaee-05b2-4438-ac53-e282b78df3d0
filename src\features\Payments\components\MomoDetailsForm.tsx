import { Button } from '@/components/button';
import { Text } from '@/components/typography';
import momo from '@/data/momo-options.json';
import { FormLabel } from '@/components/form';
import { Translate } from '@/components/translate';
import { useDispatch, useSelector } from 'react-redux';
import { getMomoFields, setMomoFields } from '../paymentsSlice';
import { MomoFieldsType } from '@/models/payment-option';
import { ChangeEvent, FormEvent } from 'react';

const MomoDetailsForm = () => {
  const dispatch = useDispatch();
  const momoFields = useSelector(getMomoFields);

  const handleMomoFormChange =
    (name: MomoFieldsType) =>
    (e: ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
      dispatch(setMomoFields({ [name]: e.target.value }));
    };

  const handleMomoFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  return (
    <form onSubmit={handleMomoFormSubmit}>
      <div className="mb-10">
        <FormLabel name="momoNetwork" text="dashboard.selectNetwork" />
        <select
          name="momoNetwork"
          onChange={handleMomoFormChange('momoNetwork')}
          className="border bg-slate-50 w-full border-gray-100 rounded"
        >
          {!momoFields['momoNetwork'] && (
            <option>Select Your Momo Network</option>
          )}
          {momo.map((momo) => (
            <option value={momo.value} key={momo.name}>
              <Translate msgId={momo.name} />
            </option>
          ))}
        </select>
      </div>
      <div className="mb-10">
        <FormLabel name="phoneNumber" text="auth.phoneNumber" />
        <input
          type="tel"
          name="phoneNumber"
          value={momoFields['phoneNumber']}
          onChange={handleMomoFormChange('phoneNumber')}
          className="border bg-slate-50 w-full border-gray-100 rounded"
          placeholder="XXXX XXXXX XXXXX XXXX"
        />
      </div>
      <Button variant="darkPrimary" className="w-full rounded-lg">
        <Text size="sm" shade="white">
          <Translate msgId="dashboard.addAccount" />
        </Text>
      </Button>
    </form>
  );
};

export default MomoDetailsForm;
