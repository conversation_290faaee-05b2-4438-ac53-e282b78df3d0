import { useCallback, useEffect, useState, useMemo } from 'react';
import ReactDOM from 'react-dom';
import PropTypes from 'prop-types';
import { Transition } from '@headlessui/react';
import {
  InformationCircleOutline,
  ExclamationOutline,
  XCircleOutline,
  CheckCircleOutline,
} from 'heroicons-react';
import {
  Notification,
  NotificationAction,
  NotificationActions,
  NotificationContent,
  NotificationTitle,
  NotificationDescription,
} from 'components/Notification';
import {
  dismiss,
  getNotifications,
  getActiveNotifications,
  SeverityOptions,
  NotificationAction as NotificationsAction,
} from './notificationsSlice';
import { useDispatch, useSelector } from 'react-redux';
import useMediaQuery from '@/hooks/useMediaQuery';
import clsx from 'features/utils/clsx';

const NotificationsProvider = ({
  className = '',
  maxAlerts = 3,
  duration = 6000,
}) => {
  const dispatch = useDispatch();
  const notifications = useSelector(getNotifications);
  const activeAlerts = useSelector(getActiveNotifications);
  const [hover, setHover] = useState(false);

  const mediaQueryLG = useMediaQuery('lg');
  const max = useMemo(
    () => (mediaQueryLG ? maxAlerts : 1),
    [mediaQueryLG, maxAlerts]
  );

  useEffect(() => {
    if (!activeAlerts.length || hover) {
      return;
    }

    const interval = setInterval(() => {
      dispatch(dismiss({ max }));
    }, duration);

    return () => {
      clearInterval(interval);
    };
  }, [dispatch, hover, duration, activeAlerts, max]);

  const onMouseEnter = useCallback(() => setHover(true), [setHover]);
  const onMouseLeave = useCallback(() => setHover(false), [setHover]);
  const onDismissClick = useCallback(
    (id: number) => dispatch(dismiss({ id })),
    [dispatch]
  );
  const onActionClick = useCallback(
    (id: number, { type, payload }: NotificationsAction) => {
      dispatch({
        type,
        ...payload,
      });
      dispatch(dismiss({ id }));
    },
    [dispatch]
  );

  const rootClass = clsx(
    'px-2 py-3 fixed z-50 inset-0 pointer-events-none flex flex-col-reverse items-center justify-start sm:flex-col sm:items-end sm:justify-start',
    className
  );

  const getIcon = useCallback((severity: SeverityOptions) => {
    if (!severity) {
      return null;
    }

    switch (severity) {
      case 'error':
        return XCircleOutline;
      case 'warning':
        return ExclamationOutline;
      case 'info':
        return InformationCircleOutline;
      default:
        return CheckCircleOutline;
    }
  }, []);

  return ReactDOM.createPortal(
    <div
      className={rootClass}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {notifications.map(
        (
          { id, title, description, severity, dismissed, dismissable, action },
          index
        ) => {
          const icon = getIcon(severity);
          const rootClass = clsx(
            'px-2 py-3 max-w-md w-full transform transition ease duration-300'
          );
          return (
            <Transition
              className={rootClass}
              key={id}
              appear={true}
              show={index < max && !dismissed}
              enter="duration-300 transition transform"
              enterFrom="opacity-0 scale-50 max-h-0"
              enterTo="opacity-100 scale-100 scale-y-100 max-h-full"
              leave="duration-300 transition transform"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-50"
            >
              <Notification>
                <NotificationContent icon={icon} iconSeverity={severity}>
                  <NotificationTitle>{title}</NotificationTitle>
                  {description && (
                    <NotificationDescription>
                      {description}
                    </NotificationDescription>
                  )}
                </NotificationContent>
                {(action || dismissable) && (
                  <NotificationActions>
                    {action && (
                      <NotificationAction
                        onClick={() => onActionClick(id, action)}
                      >
                        {action.text}
                      </NotificationAction>
                    )}
                    {dismissable && (
                      <NotificationAction
                        variant="secondary"
                        onClick={() => onDismissClick(id)}
                      >
                        Dismiss
                      </NotificationAction>
                    )}
                  </NotificationActions>
                )}
              </Notification>
            </Transition>
          );
        }
      )}
    </div>,
    document.body
  );
};

NotificationsProvider.propTypes = {
  maxAlerts: PropTypes.number,
  duration: PropTypes.number,
};

export default NotificationsProvider;
