import { Avatar } from '@/components/avatar'
import { SimpleText } from '@/components/typography'
import { FC } from 'react';
import { Payment } from "@/models/payment-option"
import formatCurrencyUtil from '@/features/utils/formatCurrency';

interface PaymentItemProps {
  payment: Payment
}

const PaymentItem: FC<PaymentItemProps> = ({ payment }) => {
  const { name, photo, type, timestamp, amount } = payment

  return (
    <div className="border rounded-md">
      <div className="p-3 space-y-2">
        <div className="flex gap-3 items-center">
          <Avatar size="lg" radius="full" src={photo} alt={name} />
          <div>
            <SimpleText className=" text-sm font-semibold ">
              {name}
            </SimpleText>
            <SimpleText className=" text-gray-300  text-sm">
              {type}
            </SimpleText>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center border-t p-3">
        <SimpleText className=" text-gray-300  text-sm">
          {timestamp}
        </SimpleText>
        <SimpleText className=" text-sm">
          {formatCurrencyUtil(amount)}
        </SimpleText>
      </div>
    </div>
  )
}

export default PaymentItem