import { Translate } from '@/components/translate';
import StatisticsOverviewChart from '@/features/Dashboard/Chart/StatisticsOverviewChart';
import Overview from '@/features/Dashboard/Overview/Overview';
import { DashboardContent, DashboardGrid } from '@/layouts/dashboard';
import DashboardHeading from '@/layouts/dashboard/DashboardHeading';

const StatisticsPage = () => {
  return (
    <DashboardContent>
      <DashboardHeading>
        <Translate msgId="dashboard.statistics" />
      </DashboardHeading>
      <DashboardGrid columns="grid-cols-1">
        <DashboardGrid gap="gap-3 md:gap-4" columns="grid-cols-3">
          <DashboardGrid item span="col-span-3">
            <Overview />
          </DashboardGrid>
          
          <DashboardGrid item span="col-span-3">
            <StatisticsOverviewChart />
          </DashboardGrid>
        </DashboardGrid>
      </DashboardGrid>
    </DashboardContent>
  );
};

export default StatisticsPage;
