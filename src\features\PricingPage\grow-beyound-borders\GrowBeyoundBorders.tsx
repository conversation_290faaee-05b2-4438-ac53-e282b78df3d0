import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"

const GrowBeyoundBorders = () => {
  return (
    <div className='py-10'>
      <div className="grid grid-cols-1 lg:grid-cols-2 ">
        <div className="bg-[#191919] p-10 px-10 lg:px-20 ">
          <div className="flex h-full flex-col justify-between">
            <div className="space-y-10">
              <SimpleText className="text-raven-green-800 uppercase font-bold text-2xl">
                <Translate msgId="landing.payments" />
              </SimpleText>
              <SimpleText className="">
                <Translate msgId="landing.growTxt1" />
              </SimpleText>
              <ul className="list-disc space-y-5 mt-10">
                <li>
                  <SimpleText className="">
                    <Translate msgId="landing.growTxt2" />
                  </SimpleText>
                </li>
                <li>
                  <SimpleText className="">
                    <Translate msgId="landing.growTxt3" />
                  </SimpleText>
                </li>
                <li>
                  <SimpleText className="">
                    <Translate msgId="landing.growTxt4" />
                  </SimpleText>
                </li>
              </ul>
            </div>

            <div className="mt-16">
              <SimpleText className="text-raven-green-800 uppercase font-bold text-2xl">
                <Translate msgId="landing.growTxt5" />
              </SimpleText>
              <ul className="list-disc space-y-5 mt-10">
                <li>
                  <SimpleText className="">
                    <Translate msgId="landiing.growTxt6" />
                  </SimpleText>
                </li>
                <li>
                  <SimpleText className="">
                    <Translate msgId="landing.growTxt7" />
                  </SimpleText>
                </li>
              </ul>
            </div>

            <div className="mt-16">
              <SimpleText className="">
                <Translate msgId="landing.growTxt8" />
              </SimpleText>
            </div>
          </div>
        </div>
        <div>
          <div className="p-10 space-y-10">
            <SimpleText className="lg:w-5/6 font-bold text-4xl leading-tight">
              <Translate msgId="landing.growTxt9" />
            </SimpleText>
            <SimpleText className="">
              <Translate msgId="landing.growTxt10" />
            </SimpleText>
          </div>
          <img src="/images/device.png" className="px-0 mx-auto md:max-w-lg" />
        </div>
      </div>
    </div>
  )
}

export default GrowBeyoundBorders