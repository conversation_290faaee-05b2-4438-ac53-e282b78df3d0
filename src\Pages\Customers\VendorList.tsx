import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import CustomersLoading from '@/features/Customers/components/CustomersLoading';
import useGetVendors from '@/features/Customers/hooks/useGetVendors';
import { Vendor } from '@/models/customer-types';
import CustomerItem from './CustomerItem';
import NoCustomer from '@/features/Customers/components/NoCustomer';
import { Button } from '@/components/button';
interface OverviewUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: { vendors: Vendor[] };
}

const VendorList = () => {
  const { loading, vendors } = useGetVendors({
    select: ({ isLoading, isUninitialized, data }: OverviewUtils) => ({
      loading: isLoading || isUninitialized,
      vendors: data?.vendors || [],
    }),
  });

  return (
    <div className="px-5 overflow-y-scroll max-h-72 py-3">
      <div className="flex flex-col gap-5">
        {loading && (
          <div className="space-y-5">
            <CustomersLoading loading={loading} />
            <CustomersLoading loading={loading} />
            <CustomersLoading loading={loading} />
          </div>
        )}
        {vendors.length > 0
          ? vendors.map((vendor: Vendor) => (
              <CustomerItem customer={vendor} key={vendor.id} />
            ))
          : !loading && (
              <NoCustomer>
                <SimpleText className='text-gray-500'>
                  <Translate msgId="dashboard.addVendorToList" />
                </SimpleText>
                <Button variant="darkPrimary" className="text-sm rounded-lg">
                  <b>+ </b> <Translate msgId="dashboard.addVendor" />
                </Button>
              </NoCustomer>
            )}
      </div>
    </div>
  );
};

export default VendorList;
