import { createSelector, createSlice } from "@reduxjs/toolkit";
import { BankNames, CardTypes, MomoOptions, PaymentOptionsType, Payment, NetworkOptionsType } from "@/models/payment-option";
import { RootState } from "@/store/store";

interface InitialState {
    network: NetworkOptionsType | null,
    option: PaymentOptionsType | null,
    cardFields: {
        cardType: CardTypes | string;
        cardNumber: string;
        expiryDate: string;
        cvv: string;
    };
    bankFields: {
        bankName: BankNames | string;
        bankNumber: string;
        bankAccountName: string;
    };
    momoFields: {
        momoNetwork: MomoOptions | string;
        phoneNumber: string;
    };
    payments: Record<string, unknown>[];
}

interface stateFieldsAction {
    [key: string]: string
}

const initialState: InitialState = {
    option: null,
    network: 'mtn_momo',
    cardFields: {
        cardType: '',
        cardNumber: '',
        expiryDate: '',
        cvv: ''
    },
    bankFields: {
        bankName: '',
        bankNumber: '',
        bankAccountName: ''
    },
    momoFields: {
        momoNetwork: '',
        phoneNumber: '',
    },
    payments: []
}

export const paymentSlice = createSlice({
    name: 'payment',
    initialState,
    reducers: {
        setNetwork: (state, action: { type: string, payload: NetworkOptionsType }) => {
            state.network = action.payload
        },
        setOption: (state, action: { type: string, payload: PaymentOptionsType }) => {
            state.option = action.payload
        },
        setCardFields: (state, action: { type: string, payload: stateFieldsAction }) => {
            const keys = Object.keys(action.payload) as Array<keyof typeof state.cardFields>
            keys.forEach(key => {
                state.cardFields[key] = action.payload[key]
            })
        },
        setBankFields: (state, action: { type: string, payload: stateFieldsAction }) => {
            const keys = Object.keys(action.payload) as Array<keyof typeof state.bankFields>

            keys.forEach(key => {
                state.bankFields[key] = action.payload[key]
            })

        },
        setMomoFields: (state, action: { type: string, payload: stateFieldsAction }) => {
            const keys = Object.keys(action.payload) as Array<keyof typeof state.momoFields>

            keys.forEach(key => {
                state.momoFields[key] = action.payload[key]
            })
        },
        setPayments: (state, action: { type: string, payload: Payment[]})=>{
            state.payments = action.payload
        }
    }
})

const { actions, name } = paymentSlice;

export const { setNetwork, setOption, setCardFields, setBankFields, setMomoFields } = actions

const getSlice = (state: RootState) => state[name]

export const getNetwork = createSelector(getSlice, (slice) => slice?.network || null)
export const getOption = createSelector(getSlice, (slice) => slice?.option || null)

export const getCardField = createSelector(getSlice, (slice) => slice?.cardFields || ({
    cardType: null,
    cardNumber: '',
    expiryDate: '',
    cvv: null
}))

export const getBankField = createSelector(getSlice, (slice) => slice?.bankFields || ({
    bankName: null,
    bankNumber: null,
    bankAccountName: ''
}))

export const getMomoFields = createSelector(getSlice, (slice) => slice?.momoFields || ({
    momoNetwork: '',
    phoneNumber: '',
}))

export const getPayments = createSelector(getSlice, (slice) => slice.payments || []);