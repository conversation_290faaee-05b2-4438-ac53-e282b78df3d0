import { FC, ReactNode } from 'react';
import clsx from '@/features/utils/clsx';

const variants = {
  primary:
    'text-indigo-600 hover:text-indigo-500 focus:outline-none focus:z-10 focus:ring-2 focus:ring-indigo-500',
  secondary:
    'text-gray-700 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500',
};

interface NotificationActionProps {
  className?: string;
  children: ReactNode;
  variant?: 'primary' | 'secondary';
  [key: string]: any;
}

const NotificationAction: FC<NotificationActionProps> = ({
  className = '',
  children,
  variant = 'primary',
  ...rest
}) => {
  const rootClass = clsx(
    'w-full border border-transparent rounded-none px-4 py-3 flex items-center justify-center text-sm font-medium first:rounded-tr-lg last:rounded-br-lg',
    {
      [variants[variant]]: true,
    },
    className
  );

  return (
    <div className="h-0 flex-1 flex">
      <button {...rest} className={rootClass}>
        {children}
      </button>
    </div>
  );
};

export default NotificationAction;
