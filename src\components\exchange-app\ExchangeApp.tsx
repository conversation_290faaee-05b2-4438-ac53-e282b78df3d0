import Card from 'components/cards/Card';
import { Translate } from 'components/translate';
import { SimpleText } from 'components/typography';
import CurrencyInput from 'components/input/CurrencyInput';
import rightArrow from '/icons/right-arrow.svg';
import { Button } from '../button';
import { useSelector } from 'react-redux';
import {
  getRates,
  getSelectedBaseCurrency,
  getSelectedReceivingCurrency,
  getTimestamp,
  getRatesError,
  getConvertorInputField
} from 'features/Landing/exchange-app/exchangeAppSlice';
import { doConversion } from 'features/utils/do-conversion';
import { getTimeFromSeconds } from 'features/utils/parse-time';
import { Link } from 'react-router-dom';

/**
 * ExchangeApp component which is the main component rendering the CurrencyInput components
 * and handling the currency exchange logic.
 *
 * @component
 * @example
 * <ExchangeApp />
 */
const ExchangeApp = () => {
  const rates = useSelector(getRates);
  const selectedBaseCurrency = useSelector(getSelectedBaseCurrency);
  const selectedReceivingCurrency = useSelector(getSelectedReceivingCurrency);
  const error = useSelector(getRatesError);
  const currentConvertor = useSelector(getConvertorInputField);

  const timeStamp = useSelector(getTimestamp);
  const formattedTime = getTimeFromSeconds(timeStamp);

  return (
    <Card>
      <div className="space-y-6">
        <div>
          <div className="flex justify-between items-center">
            <SimpleText className="text-gray-500">
              <Translate msgId="home.youSend" />
            </SimpleText>
            <button className="rounded-full text-sm bg-raven-green-800 p-2 text-white">
              <SimpleText>
                <Translate msgId="home.compareRates" />
              </SimpleText>
            </button>
          </div>

          <CurrencyInput isConvertor={currentConvertor.id === 1} index={1} />
        </div>
        <div>
          <SimpleText className="text-gray-500">
            <Translate msgId="home.theyGet" />
          </SimpleText>
          <CurrencyInput isConvertor={currentConvertor.id === 2} index={2} />
          {error && (
            <div className="mt-1 text-red-600">
              <SimpleText component="small">{error.message}</SimpleText>
            </div>
          )}
        </div>
        <div>
          {!!rates && (
            <SimpleText>
              <span className="text-bold">
                <Translate msgId="home.currentRate" /> {formattedTime}:{' '}
              </span>
              {`${selectedBaseCurrency.sign}1.00 = ${
                selectedReceivingCurrency.id
              } ${doConversion({
                rates,
                base: selectedBaseCurrency.id,
                receiving: selectedReceivingCurrency.id
              })?.toFixed(2)}`}
            </SimpleText>
          )}
        </div>
        <Button
          variant="primary"
          className="inline-flex items-center space-x-3 rounded-3xl"
          component={Link}
          to="/signup"
        >
          <SimpleText>
            <Translate msgId="home.getStarted" />
          </SimpleText>
          <img src={rightArrow} alt="right arrow" className="w-5" />
        </Button>
      </div>
    </Card>
  );
};

export default ExchangeApp;
