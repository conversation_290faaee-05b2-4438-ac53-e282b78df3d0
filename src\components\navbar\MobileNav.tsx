import { useState, useEffect } from 'react';
import { Logo } from '../logo';
import clsx from '@/features/utils/clsx';
import { Link, useLocation } from 'react-router-dom';
import { X } from 'heroicons-react';
import { Translate } from '../translate';
import { Button } from '../button';
import { ChevronDown, GlobeAlt } from 'heroicons-react';
import TooltipMenu from '../tooltipMenu/tooltipMenu';
import AppDownloadQrCode from './qrCode';

const MobileNav = () => {
  const [open, setOpen] = useState<boolean>(false);
  const { pathname } = useLocation();

  useEffect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    // Cleanup function to reset the overflow property
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [open]);
  const closeMenu = () => {
    setOpen(false);
  };
  const LinksForTooltips = {
    personal: [
      {
        label: 'ravenapp',
        href: '/'
      },
      {
        label: 'cards',
        href: '/card'
      }
    ],
    business: [
      {
        label: 'smes',
        href: '/business'
      },
      {
        label: 'enterprise',
        href: '/enterprise'
      },
      {
        label: 'solutions',
        href: '/payments'
      }
    ],
    company: [
      {
        label: 'pricing',
        href: '/pricing'
      },
      {
        label: 'resources',
        href: '/resources'
      },
      {
        label: 'careers',
        href: '/careers'
      },
      {
        label: 'aboutUs',
        href: '/'
      },
      {
        label: 'docs',
        href: '/documentation'
      }
    ]
  };
  return (
    <section className="relative block lg:hidden z-50">
      <nav
        className={`${
          pathname.startsWith('/pricing')
            ? 'bg-[#191919] text-white'
            : 'bg-white'
        } gap-3 flex items-center justify-between  max-w-[1024px] mx-auto h-full px-3 md:px-10 py-3`}
      >
        <Logo className="w-28" />

        <button
          onClick={() => setOpen(true)}
          type="button"
          className="inline-flex items-center p-2 w-10 h-10 justify-center text-sm lg:hidden"
          aria-controls="navbar-dropdown"
          aria-expanded="false"
        >
          <span className="sr-only">Open main menu</span>
          <svg
            className="w-5 h-5"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 17 14"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M1 1h15M1 7h15M1 13h15"
            />
          </svg>
        </button>

        <ul
          className={clsx(
            'absolute text-gray-500 flex flex-col gap-y-6 transition-all duration-300 top-0  h-screen bg-white',
            {
              '  left-0 w-full h-screen': open,
              ' -left-80 w-0': !open
            }
          )}
        >
          <div className="flex justify-between items-center border-b border-raven-green-800 py-3 px-5">
            <button
              onClick={() => {
                closeMenu();
              }}
            >
              <Logo className=" w-28" />
            </button>
            <button
              onClick={() => {
                closeMenu();
              }}
            >
              <X />
            </button>
          </div>

          <ul className="flex flex-col space-y-6 rounded  bg-white md:px-6 py-6  w-full">
            <li className="w-max group">
              <Button
                variant="transparent"
                component={Link}
                to="/"
                className="hover:bg-light w-100"
                onClick={(e: Event) => {
                  e.preventDefault();
                }}
              >
                <Translate msgId="home.personal" />
              </Button>
              <TooltipMenu data={LinksForTooltips.personal} position="right" />
            </li>

            <li className="w-max group">
              <Button
                variant="transparent"
                component={Link}
                to="/business"
                className="hover:bg-red w-100"
                onClick={(e: Event) => {
                  e.preventDefault();
                }}
              >
                <Translate msgId="home.business" />
              </Button>
              <TooltipMenu data={LinksForTooltips.business} position="right" />
            </li>
            <li className="w-max group">
              <Button
                variant="transparent"
                component={Link}
                to="/company"
                className="hover:bg-red w-100"
                onClick={(e: Event) => {
                  e.preventDefault();
                }}
              >
                <Translate msgId="home.company" />
              </Button>
              <TooltipMenu data={LinksForTooltips.company} position="right" />
            </li>
            <li className="w-max">
              <Button
                variant="transparent"
                component={Link}
                to="/support"
                className="hover:bg-light w-100"
                onClick={closeMenu}
              >
                <Translate msgId="home.support" />
              </Button>
            </li>
            <li className="w-100 items-center flex ml-3">
              <div className="flex mr-3 border-grey border-2 p-2 rounded-lg">
                {pathname.startsWith('/support') ? (
                  <Button
                    variant="transparent"
                    className="rounded-lg"
                    type="button"
                    component={Link}
                    to="/"
                  >
                    <Translate msgId="home.docs" />
                  </Button>
                ) : (
                  <>
                    <GlobeAlt />
                    <ChevronDown />
                  </>
                )}
              </div>
              <section className="group relative">
                <Button
                  variant="darkPrimary"
                  className="text-white rounded-lg md:h-auto h-12  w-auto flex justify-center items-center"
                  type="button"
                  component={Link}
                  to={`${pathname.startsWith('/support') ? '/signin' : '/'}`}
                  onClick={() => {
                    if (!pathname.startsWith('/support')) return;
                  }}
                >
                  <Translate
                    msgId={`${
                      pathname.startsWith('/support')
                        ? 'auth.login'
                        : 'home.download'
                    }`}
                  />
                </Button>
                {!pathname.startsWith('/support') ? (
                  <AppDownloadQrCode />
                ) : null}
              </section>
            </li>
          </ul>
        </ul>
      </nav>
    </section>
  );
};

export default MobileNav;
