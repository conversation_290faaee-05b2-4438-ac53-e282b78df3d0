import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { TextSecondary } from '@/components/typography/Text';
import downSpring from '/images/down-spring.png';
import raven from '/icons/raven-icon.png';
import bank from '/icons/merchant-account.png';
import card from '/icons/icon-card.svg';
import momo from '/icons/mobile-payment.png';
import phone from '/images/phone-portrait.png';
import { Trans } from 'react-i18next';

const ConvenientMethods = () => {
  return (
    <div className="md:pb-20 md:px-10">
      <img src={downSpring} alt="spring pointing down" />
      <div className="mb-10">
        <div className="text-center space-y-4">
          <SimpleText
            component="h2"
            className="font-semibold text-3xl md:text-5xl"
          >
            <Trans
              i18nKey="home.selectConvenient"
              components={{
                style: <span className="text-raven-green-800 leading-tight" />,
              }}
            />
          </SimpleText>
          <TextSecondary size="lg" shade="light">
            <Translate msgId="home.youGetToChoose" />
          </TextSecondary>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
        <div className="space-y-10 lg:space-y-44">
          <div className="bg-white rounded-lg shadow-lg p-10">
            <div className="w-10 h-10 mb-5 bg-gray-300 rounded-full flex items-center justify-center">
              <img src={raven} alt="raven" />
            </div>
            <div className="space-y-4">
              <SimpleText component="h3" className="font-semibold text-lg">
                <Translate msgId="home.ravenAccount" />
              </SimpleText>
              <SimpleText>
                <Translate msgId="home.ravenEasiest" />
              </SimpleText>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg p-10">
            <div className="w-10 h-10 mb-5 bg-green-300 rounded-full flex items-center justify-center">
              <img src={bank} alt="bank" />
            </div>
            <div className="space-y-4">
              <SimpleText component="h3" className="font-semibold text-lg">
                <Translate msgId="home.bankPayment" />
              </SimpleText>
              <SimpleText>
                <Translate msgId="home.bankPaymentDesc" />
              </SimpleText>
            </div>
          </div>
        </div>
        <div className="relative hidden lg:block">
          <div className="flex items-center justify-center">
            <img src={phone} alt="phone" />
          </div>
        </div>
        <div className="space-y-10 lg:space-y-44">
          <div className="bg-white rounded-lg shadow-lg p-10">
            <div className="w-10 h-10 mb-5 bg-yellow-300 rounded-full flex items-center justify-center">
              <img src={card} alt="card" />
            </div>
            <div className="space-y-4">
              <SimpleText component="h3" className="font-semibold text-lg">
                <Translate msgId="home.cards" />
              </SimpleText>
              <SimpleText>
                <Translate msgId="home.cardDesc" />
              </SimpleText>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg p-10">
            <div className="w-10 h-10 mb-5 bg-red-300 rounded-full flex items-center justify-center">
              <img src={momo} alt="momo" />
            </div>
            <div className="space-y-4">
              <SimpleText component="h3" className="font-semibold text-lg">
                <Translate msgId="dashboard.momo" />
              </SimpleText>
              <SimpleText>
                <Translate msgId="home.momoDesc" />
              </SimpleText>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConvenientMethods;
