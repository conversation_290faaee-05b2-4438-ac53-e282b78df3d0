import OverviewCard from '@/features/Dashboard/Overview/OverviewCard';
import useCustomerOverview from './hooks/useCusomterOverview';

interface OverviewUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: {
    clients: number;
    vendors: number;
    active: number;
    total_cutomers: number;
  };
}

const CustomerOverview = () => {
  const { loading, vendors, clients, active, totalCustomers } =
    useCustomerOverview({
      select: ({ isLoading, isUninitialized, data }: OverviewUtils) => ({
        loading: isLoading || isUninitialized,
        clients: data?.clients || 0,
        vendors: data?.vendors || 0,
        active: data?.active || 0,
        totalCustomers: data?.total_cutomers || 0,
      }),
    });

  return (
    <dl className="grid grid-cols-2 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <OverviewCard
        className="rounded border bg-white"
        loading={loading}
        name="dashboard.totalCustomers"
        stat={totalCustomers}
      />
      <OverviewCard
        className="rounded border bg-white"
        loading={loading}
        name="dashboard.vendors"
        stat={vendors}
      />
      <OverviewCard
        className="rounded border bg-white"
        loading={loading}
        name="dashboard.clients"
        stat={clients}
      />
      <OverviewCard
        className="rounded border bg-white"
        loading={loading}
        name="dashboard.active"
        stat={active}
      />
    </dl>
  );
};

export default CustomerOverview;
