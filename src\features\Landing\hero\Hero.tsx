import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import phone from '/images/Phone.png';
import { Trans } from 'react-i18next';
import spring from '/images/spring-arrow.png';
import { Button } from '@/components/button';
import { Link } from 'react-router-dom';
import LC from '/images/raven_card_bottom_left_bryan.png';
import RC from '/images/raven_card_top_right_bryan.png';
import AnnotatedText from '@/components/rough-notated-text/rough-notate';

const Hero = () => {
  return (
    <section>
      <div className="sm:mt-[-100px] lg:mt-[-50px] mb-[-50px] grid grid-cols-1 lg:grid-cols-2 ">
        <div className="flex flex-col items-start justify-center">
          <div className="flex flex-col items-start">
            <img
              src={spring}
              alt="spring arrow"
              className="mb-2 sm:mb-[10px]"
            />

            <SimpleText
              component="h1"
              className="text-4xl sm:text-5xl md:text-6xl font-extrabold leading-tight"
            >
              <Trans
                i18nKey="home.heroTitle"
                components={{
                  style1: (
                    <span className="text-raven-green-800 leading-tight" />
                  )
                }}
              />
              <AnnotatedText />
            </SimpleText>
          </div>

          <div className="mt-4 sm:mt-6">
            <SimpleText component="h2" className="text-lg font-light">
              <Translate msgId="home.heroSubtitle" />
            </SimpleText>
          </div>

          <div className="mt-8 sm:mt-10">
            <SimpleText className="font-bold text-xl sm:text-2xl">
              <Translate msgId="home.getOnAppStores" />
            </SimpleText>
            <div className="mt-4 sm:mt-6 flex items-center gap-4">
              <Button
                variant="darkPrimary"
                className="flex items-center justify-center text-white rounded-lg px-6 py-2 h-[47px]"
                type="button"
                component={Link}
                to="/"
              >
                <Translate msgId="home.download" />
              </Button>
              <Button
                className="text-dark border-2 border-white rounded-lg px-6 py-2 h-[45px]"
                type="button"
                variant="transparent"
              >
                <Link to="/">
                  <Translate msgId="home.learnMore" />
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Phone and Cards Section */}
        <div className="relative flex flex-col items-center lg:items-end gap-4 mt-[-100px] mb-[-150px] z-[-100]">
          <span className=" w-[300px]  h-[300px] bg-green-500 blur-[80px] rounded-full absolute top-1/2 transform -translate-y-1/2"></span>

          {/* Left and Right Card (RC) */}
          <div className="relative flex flex-col items-center">
            <img
              src={RC}
              alt="Decorative card top right"
              className=" h-auto w-[260px]  transform translate-y-[220px] translate-x-[60px]"
            />
            <img
              src={phone}
              alt="Phone showing raven app"
              className="relative z-10 w-[240px] sm:w-[260px] lg:w-[320px]"
            />

            {/* Left Card (LC)*/}
            <img
              src={LC}
              alt="Decorative card bottom left"
              className="h-auto w-[240px] transform translate-y-[-300px] -translate-x-[100px]"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
