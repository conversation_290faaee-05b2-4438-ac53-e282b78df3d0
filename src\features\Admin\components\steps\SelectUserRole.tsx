import { Button } from "@/components/button"
import { Translate } from "@/components/translate"
import { useState } from "react"

const SelectUserRole = () => {
  const [selectedRole, setSelectedRole] = useState('Admin')
  return (
    <div className="flex gap-1 container">
      <div className="flex items-center flex-1 ">
        <div className="flex flex-col">
          <Button variant='darkPrimary' className='rounded-md flex gap-2 items-center justify-between'>
            <Translate msgId="admin.defaultRole" />
            <div className={`w-3 h-3 border-2 border-gray-100 rounded-[10px]`} />

          </Button>
          <div className=" border-r-2 border-dashed border-gray-300 border-spacing-y-40 h-52 mr-5"></div>
          <Button variant='light' className='flex gap-2 items-center justify-between'>
            <Translate msgId="admin.manualRole" />
            <div className={`w-3 h-3 border-2 border-gray-100 rounded-[10px]`} /></Button>
        </div>
      </div>

      <div className="bg-gray-100 rounded-md space-y-4 p-8 max-h-96 overflow-y-scroll  flex-1 ">
        {roles.map(role => (
          <div onClick={() => setSelectedRole(role.title)} className=' cursor-pointer flex gap-5 items-center justify-between'>
            <div>
              <h1><Translate msgId={role.title} /></h1>
              <p className='text-gray-400 text-sm'>
                <Translate msgId={role.description} />
              </p>
            </div>
            <div className="">
              <div className={`w-4 h-4 border-[5px] ${selectedRole == role.title ? 'border-raven-green-800' : 'border-gray-300'} rounded-[10px]`}></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default SelectUserRole

const roles = [
  { title: 'admin.role1Title', description: 'admin.role1Des' },
  { title: 'admin.role2Title', description: 'admin.role2Des' },
  { title: 'admin.role3Title', description: 'admin.role3Des' },
  { title: 'admin.role4Title', description: 'admin.role4Des' },
  { title: 'admin.role5Title', description: 'admin.role5Des' },
]