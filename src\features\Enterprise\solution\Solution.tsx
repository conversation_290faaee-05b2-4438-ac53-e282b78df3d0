import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import reasons from '@/data/business-reasons.json';
import { Trans } from 'react-i18next';
import { PaperAirplane, ShieldCheck, CreditCard } from 'heroicons-react';
const Solution = () => {
  return (
    <div className="mt-5 lg:mt-20 md:py-20 md:px-10 space-y-20">
      <div className="relative overflow-hidden ">
        <span
          className="absolute w-2/3 h-[450px] bg-raven-green-50 top-0 -z-10 
        rounded-l-[250px] -right-32"
        ></span>

        <div className="mb-10 space-y-4 text-center lg:w-1/2 mx-auto pt-10">
          <SimpleText
            component="h2"
            className=" leading-relaxed font-semibold text-3xl md:text-5xl"
          >
            <Trans
              i18nKey="enterprise.solutionsTitle1"
              components={{
                style: <span className="text-raven-green-800 leading-8" />
              }}
            />
          </SimpleText>
          <SimpleText className="text-sm">
            <Translate msgId="enterprise.solutionsDescription" />
          </SimpleText>
        </div>
        <div className="grid gap-3 md:gap-5 grid-cols-1 md:grid-cols-3 lg:w-3/6 mx-auto pb-10">
          {reasons.slice(0, 3).map((_, index) => (
            <Button
              key={index}
              variant="light"
              className={`shadow-md hover:!bg-raven-green-800 hover:text-white
              !rounded !p-5 border-spacing-3 border-raven-green-800 ${
                index == 1 && '!bg-raven-green-50 dark:text-black'
              }`}
            >
              {_.label}
            </Button>
          ))}
        </div>
      </div>
      <div className="grid gap-3 md:gap-5 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 lg:w-5/6 mx-auto">
        {reasons.slice(0, 3).map((_, index) => (
          <div key={index} className="  rounded-md p-5 shadow-xl space-y-3">
            <div className="rounded-full h-10 w-10 bg-raven-green-800 grid place-items-center text-white">
              {index == 0 ? (
                <CreditCard />
              ) : index == 1 ? (
                <PaperAirplane />
              ) : (
                <ShieldCheck />
              )}
            </div>
            <SimpleText className="font-semibold mb-2">
              <Translate msgId={_.label ?? ''} />
            </SimpleText>
            <SimpleText className="text-sm">
              <Translate msgId={_.enterprise ?? ''} />
            </SimpleText>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Solution;
