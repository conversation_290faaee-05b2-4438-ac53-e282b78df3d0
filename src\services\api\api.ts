// import { notify } from '@/features/Notifications/notificationsSlice';
import getErrors from '@/features/utils/get-errors';
import axios, { CancelTokenStatic } from 'axios'
import { getToken } from 'features/utils/cookie';

/**
 * Axios instance for making API requests within the application.
 *
 * This instance is pre-configured with the base URL and request interceptors.
 * It automatically adds the authorization token to request headers if available.
 */
const api = axios.create({
    baseURL: 'http://localhost:8000/api',
    timeout: 10000
})

/**
 * Cancel token for canceling ongoing requests.
 *
 * This token source is used to create a CancelToken for each axios request.
 * It allows cancellation of requests using the AbortController signal.
 *
 * @type {CancelTokenSource}
 */
const cancelToken: CancelTokenStatic = axios.CancelToken

// Intercept axios requests and add token to authorization headers
api.interceptors.request.use((options) => {
    // Create a variable to store the CancelToken for each request
    const cancelTokenSource = cancelToken.source();

    if (options?.signal && !options?.cancelToken) {
        const { signal } = options;

        options.cancelToken = cancelTokenSource.token;

        signal.addEventListener!('abort', () => cancelTokenSource.cancel());

    }

    // Get the access token from local storage
    const accessToken = getToken()

    if (!accessToken) {
        return options
    }

    // if there is an accessToken in localstorage, set the Authorization header
    const { headers } = options
    headers['Authorization'] = `Bearer ${accessToken}`

    return options
});

// consider using a custom middleware to dispatch the actions instead
api.interceptors.response.use(null, error => {
    const { isAxiosError, response } = error;

    // If the error is not an Axios error, or there's no response, reject the promise.
    // This can happen if the request was cancelled, if the server didn't respond,
    // or if some network error occurred.
    if (!isAxiosError || !response) {
        return Promise.reject(error);
    }

    const {
        // status,
        config } = response;

    /**
     * Do not import store into this file
     * Add a slice which adds store.dispatch function to the state and use it to dispatch actions 
     */

    // if (status === 401 && isSessionAuthorized(store.getState())) {
    //     store.dispatch(endSession());
    // }

    /**
     * In the event you want to display an error differently without
     * a notification, pass "disableErrorAlerts" in your request config.
     */
    if (config?.disableErrorAlerts) {
        return Promise.reject(error);
    }

    const result = getErrors(error);

    if (hasErrors(result)) {
        // const { errors } = result;

        // errors.forEach((error: { message?: string }) => {
        //     if (error?.message !== 'Not found') {
        //         store.dispatch(
        //             notify({
        //                 severity: 'error',
        //                 title: error?.message,
        //             })
        //         );
        //     }
        // });
    }

    return Promise.reject(error);
});

function hasErrors(obj: any): obj is { errors: { message: string }[] } {
    return obj && obj.errors && Array.isArray(obj.errors);
}



export { cancelToken };
export default api