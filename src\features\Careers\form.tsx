import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import { FormLabel } from '@/components/form';
import {
  DocumentTextOutline,
  CloudUploadOutline,
  XCircle
} from 'heroicons-react';
import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/button';
import { jobSelected } from './jobs';
// import axios from 'axios';

export const input_style =
  'bg-raven-green-50 border-2 rounded-lg w-full dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 mt-3';
interface careerForm {
  onCloseForm: () => void;
  valueFromJobs: jobSelected | null;
}
export default function CareerApplicationForm({
  onCloseForm,
  valueFromJobs
}: careerForm) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  // const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [allowMultiple, setAllowMultiple] = useState(true);

  const handleFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFilesChange = () => {
    const files = fileInputRef.current?.files;
    if (files && files.length > 0) {
      const currentFiles = [...(selectedFiles || [])];
      const newFiles: File[] = [];

      if (!allowMultiple) {
        const firstFile = files[0];
        const isAlreadySelected = currentFiles.some(
          (existingFile) =>
            existingFile.name === firstFile.name &&
            existingFile.size === firstFile.size &&
            existingFile.lastModified === firstFile.lastModified
        );

        if (!isAlreadySelected) {
          setSelectedFiles([firstFile, ...currentFiles]);
        }
        return;
      }

      Array.from(files).forEach((file) => {
        const isAlreadySelected = currentFiles.some(
          (existingFile) =>
            existingFile.name === file.name &&
            existingFile.size === file.size &&
            existingFile.lastModified === file.lastModified
        );

        if (!isAlreadySelected) {
          newFiles.push(file); // Adding only if it's not already selected
        }
      });

      console.log('current Files: ', currentFiles, 'new Files :', newFiles);

      const updatedFiles = [...newFiles, ...currentFiles].slice(0, 2);
      console.log('Updated Files together : ', updatedFiles);

      setSelectedFiles(updatedFiles);
    }
  };

  // const uploadFiles = async (formData: FormData) => {
  //   try {
  //     const response = await axios.post('/api/careers/apply', formData, {
  //       headers: {
  //         'Content-Type': 'multipart/form-data'
  //       },
  //       onUploadProgress: (progressEvent) => {
  //         if (progressEvent.total) {
  //           const percentComplete = Math.round(
  //             (progressEvent.loaded / progressEvent.total) * 100
  //           );
  //           setUploadProgress(percentComplete);
  //         }
  //       }
  //     });

  //     console.log('Upload successful:', response.data);
  //     setUploadProgress(100);
  //   } catch (error) {
  //     console.error('Error uploading files:', error);
  //     setUploadProgress(0);
  //   }
  // };
  const removeFileByIndex = (index: number) => {
    const updatedFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(updatedFiles);
  };
  const handleSubmit = () => {
    console.log('value from jobs listing: ', valueFromJobs);
  };
  useEffect(
    () => {
      // console.log(uploadProgress);
    },
    [
      /*uploadProgress*/
    ]
  );

  return (
    <div className="w-full md:max-w-[600px] bg-slate-50 p-5 py-2 md:py-5 rounded-lg flex flex-col gap-y-4 md:gap-y-5) ">
      <div>
        <section className="flex items-center justify-between mb-1 md:mb-5">
          <SimpleText component="h3" className="text-2xl ">
            <Translate msgId="careers.applicationForm" />
          </SimpleText>
          <XCircle
            className="text-red-400 border-2 border-gray-800 p-1 rounded-full w-8 h-8"
            onClick={onCloseForm}
          />
        </section>
        <SimpleText component="h2">
          <Translate msgId="careers.personalInfo" />
        </SimpleText>
      </div>
      <div>
        <FormLabel
          name="fullname"
          text="Full name"
          className="-mb-[8px]"
          required
        />
        <input
          type="text"
          name="fullname"
          className={`${input_style} form-control form-input`}
          required
          placeholder="eg: Oaqson Oppong"
        />
      </div>
      <div>
        <FormLabel
          name="workemaial"
          text="Work email"
          className="-mb-[8px]"
          required
        />
        <input
          type="email"
          name="fullname"
          className={`${input_style} form-control form-input`}
          required
          placeholder="eg: <EMAIL>"
        />
      </div>
      <div>
        <FormLabel
          name="number"
          text="Phone Number (optional)"
          className="-mb-[8px]"
        />
        <section className="relative flex gap-x-2 ">
          <select
            className={`w-[140px]  bg-[#f8f8f8] border-2 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 form-control h-14 mt-3 text-xs border-[#eee]`}
          >
            <option value="+233">Ghana (+233)</option>
            <option value="+234">Nigeria (+234)</option>
            <option value="+20">Egypt (+20)</option>
            <option value="+251">Ethipia (+251)</option>
            <option value="+223">Mali (+223)</option>
            <option value="+971">United Arab Emirates (+971)</option>
            <option value="+27">South Africa(+27)</option>
          </select>
          <input
            type="text"
            name="umber"
            className={`${input_style} form-control form-input`}
            placeholder="eg:539811512"
          />
        </section>
      </div>
      <div>
        <FormLabel
          name="file"
          text="Upload your CV and cover page"
          className="-mb-[8px]"
          required
        />
        <div
          className={`${input_style} form-control form-input flex flex-col md:flex-row gap-3 justify-between`}
        >
          <input
            type="file"
            name="cv_and_cover_letter"
            multiple
            hidden
            ref={fileInputRef}
            onChange={handleFilesChange}
            accept=".pdf,.doc,.docx"
          />
          <div className="flex  items-center gap-x-2 ">
            {(selectedFiles?.length ?? 0) < 1 ? (
              <div
                onClick={() => {
                  setAllowMultiple(true);
                  handleFileSelect();
                }}
                className="flex items-center gap-x-2"
              >
                <CloudUploadOutline />
                <SimpleText>
                  <Translate msgId="careers.upload" />
                </SimpleText>
              </div>
            ) : (
              <div className="flex  items-center gap-x-2">
                <div className="flex  items-center gap-x-2 ">
                  <DocumentTextOutline className="text-raven-green-800" />
                  <SimpleText className="line-clamp-1">
                    {selectedFiles && selectedFiles[0].name}
                  </SimpleText>
                </div>
                <XCircle
                  className="text-red-400"
                  onClick={() => removeFileByIndex(0)}
                />
              </div>
            )}
          </div>

          <div>
            {(selectedFiles?.length ?? 0) < 2 ? (
              <>
                {(selectedFiles?.length ?? 0) == 1 ? (
                  <div
                    onClick={() => {
                      setAllowMultiple(false);
                      handleFileSelect();
                    }}
                    className="flex items-center gap-x-2"
                  >
                    <CloudUploadOutline />
                    <SimpleText>
                      <Translate msgId="careers.uploadCover" />
                    </SimpleText>
                  </div>
                ) : (
                  <a
                    download
                    href="./file.txt"
                    className="flex items-center text-sm md:text-base gap-x-2 p-2 bg-white rounded-lg border-2 border-[#eee] dark:border-gray-600 "
                  >
                    <DocumentTextOutline className="text-raven-green-800" />
                    <Translate msgId="careers.download" />
                  </a>
                )}
              </>
            ) : (
              <div className="flex  items-center gap-x-2">
                <div className="flex  items-center gap-x-2 ">
                  <DocumentTextOutline className="text-raven-green-800" />
                  <SimpleText className="line-clamp-1">
                    {selectedFiles && selectedFiles[1].name}
                  </SimpleText>
                </div>
                <XCircle
                  className="text-red-400"
                  onClick={() => removeFileByIndex(1)}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      <Button
        variant="primary"
        className="self-center w-2/5 rounded-lg"
        onClick={handleSubmit}
      >
        <Translate msgId="careers.submit" />
      </Button>
    </div>
  );
}
