import { http, HttpResponse } from 'msw'
import { setupWorker } from 'msw/browser'

export const worker = setupWorker(
    http.post('http://localhost:8000/api/register', () => {
        return HttpResponse.json({
            otp: 1234,
            redirect: '/verify-otp'
        }, { headers: { 'Set-Cookie': 'otpCookie=true' }, status: 200 }
        )
    }),
    http.post('http://localhost:8000/api/otp', () => {
        return HttpResponse.json({
            messaage: 'OTP Confirmed'
        }, { status: 200 })
    }),
    http.post('http://localhost:8000/api/signup', ({ request, }) => {
        console.log('body', request.body?.getReader());
        return HttpResponse.json({
            messaage: 'success',
            user: {
                firstName: "David",
                otherNames: "",
                lastName: "Quartey",
                phoneNumber: "123456789",
                email: "<EMAIL>",
                password: "test123",
                confirmPassword: "test123",
                country: "GH"
            }
        }, { status: 200, headers: { 'Set-Cookie': 'token=sdfwefewf' } })
    }),
    http.post('http://localhost:8000/api/users/login', () => {
        return HttpResponse.json({
            message: 'success',
            user: {
                firstName: "David",
                otherNames: "",
                lastName: "Quartey",
                phoneNumber: "123456789",
                email: "<EMAIL>",
                password: "test123",
                confirmPassword: "test123",
                country: "GH"
            }
        }, {
            status: 200, headers: { 'Set-Cookie': 'auth=sdfwefewf' }
        })
    }),
    http.get('http://localhost:8000/api/business/overview', () => {
        const income = 0
        const expenses = 0
        const savings = 0
        const balance = income - expenses
        return HttpResponse.json({
            balance,
            expenses,
            income,
            savings
        })
    }),
    http.get('http://localhost:8000/api/business/stats', () => {
        const data = [
            {
                month: 'Mar 1 - 7',
                earning: 2400,
            },
            {
                month: 'Mar 8 - 14',
                earning: 2210,
            },
            {
                month: 'Mar 15 - 21',
                earning: 4290,
            },
            {
                month: 'Mar 22 - 28',
                earning: 9000,
            },
            {
                month: 'Final wk',
                earning: 20181,
            }
        ];
        return HttpResponse.json({
            overview: data
        })
    }),
    http.get('http://localhost:8000/api/business/stats/all', () => {
        const data = [
            {
                name: 'Jan',
                profit: 0,
                average_transaction: 0,
                revenue: 0,
            },
            {
                name: 'Feb',
                profit: 7068,
                average_transaction: 2067,
                revenue: 8090,
            },
            {
                name: 'Mar',
                profit: 5000,
                average_transaction: 6098,
                revenue: 3550,
            },
            {
                name: 'Apr',
                profit: 1480,
                average_transaction: 4200,
                revenue: 7080,
            },
            {
                name: 'May',
                profit: 6520,
                average_transaction: 1108,
                revenue: 3060,
            },
            {
                name: 'Jun',
                profit: 1400,
                average_transaction: 3100,
                revenue: 5080,
            },
            {
                name: 'Jul',
                profit: 5700,
                average_transaction: 5580,
                revenue: 6080,
            },
            {
                name: 'Aug',
                profit: 2700,
                average_transaction: 1580,
                revenue: 1000,
            },
        ];
        return HttpResponse.json({
            overview: data
        })
    }),
    http.get('http://localhost:8000/api/customers/vendors', () => {

        const data = [
            {
                id: 1,
                company_name: 'Adom Group',
                full_name: 'Adjoa Adom',
                email: '<EMAIL>',
                phone_number: '0550040300',
                avatar: null
            },
            {
                id: 2,
                company_name: 'Liams Nest',
                full_name: 'Joe Williams',
                email: '<EMAIL>',
                phone_number: '0553459323',
                avatar: null
            },
            {
                id: 3,
                company_name: 'Oniocha Limited',
                full_name: 'Deborah Saki',
                email: '<EMAIL>',
                phone_number: '0557728837',
                avatar: null
            },
            {
                id: 4,
                company_name: 'Lush Group',
                full_name: 'Moses Arthur',
                email: '<EMAIL>',
                phone_number: '0553479274',
                avatar: null
            },
            {
                id: 5,
                company_name: 'Olam Ghana',
                full_name: 'Roberta Quarshie',
                email: '<EMAIL>',
                phone_number: '05534757394',
                avatar: null
            },
        ];
        return HttpResponse.json({
            vendors: data
        })
    }),
    http.get('http://localhost:8000/api/customers/clients', () => {

        const data = [
            {
                id: 1,
                company_name: 'Adom Group',
                full_name: 'Adjoa Adom',
                email: '<EMAIL>',
                phone_number: '0550040300',
                avatar: null
            },
            {
                id: 2,
                company_name: 'Liams Nest',
                full_name: 'Joe Williams',
                email: '<EMAIL>',
                phone_number: '0553459323',
                avatar: null
            },
            {
                id: 3,
                company_name: 'Oniocha Limited',
                full_name: 'Deborah Saki',
                email: '<EMAIL>',
                phone_number: '0557728837',
                avatar: null
            },
        ];
        return HttpResponse.json({
            clients: data
        })
    }),
    http.get('http://localhost:8000/api/admin/users', () => {

        const data = [
            {
                id: 1,
                name: 'Adjoa Adom',
                email: '<EMAIL>',
                role: 'Admin', 
                status: 'Confirmed', 
                avatar: null, 
                joined: '24/08/2024 6:45pm',
            },
            {
                id: 2,
                name: 'Joe Williams',
                email: '<EMAIL>',
                role: 'Admin', 
                status: 'Pending', 
                avatar: null, 
                joined: '24/08/2024 6:45pm',
            },
            {
                id: 3,
                name: 'Deborah Saki',
                email: '<EMAIL>',
                role: 'Admin', 
                status: 'Confirmed', 
                avatar: null, 
                joined: '24/08/2024 6:45pm',
            },
        ];
        return HttpResponse.json({
            users: data
        })
    }),
    http.get('http://localhost:8000/api/settings/integrations', () => {

        const data = [
            { id: 'uuid-1', name: 'Maichinp', description: 'Grow your business an all-in-One marketing automation & email marketing platform', isActive: true, link: 'https://mailchimp.com', logo: '/images/profile.png' },
            { id: 'uuid-3', name: 'Hubtel', description: 'Grow your business an all-in-One marketing automation & email marketing platform', isActive: true, link: 'https://hubtel.com', logo: '/images/profile.png' },
            { id: 'uuid-4', name: 'Calendly', description: 'Grow your business an all-in-One marketing automation & email marketing platform', isActive: false, link: 'https://calendly.com', logo: '/images/profile.png' },
            { id: 'uuid-5', name: 'Slack', description: 'Grow your business an all-in-One marketing automation & email marketing platform', isActive: true, link: 'https://slack.com', logo: '/images/profile.png' },
            { id: 'uuid-2', name: 'Gmail', description: 'Grow your business an all-in-One marketing automation & email marketing platform', isActive: false, link: 'https://gmail.com', logo: '/images/profile.png' },
            { id: 'uuid-6', name: 'Whats App', description: 'Grow your business an all-in-One marketing automation & email marketing platform', isActive: true, link: 'https://whatsapp.com', logo: '/images/profile.png' },
        ];
        return HttpResponse.json({
            integrations: data
        })
    }),
    http.get('http://localhost:8000/api/settings/accounts', () => {

        const data = [
            { id: 1, type: 'bank', name: 'Fidelity Bank', number: **************, logo: '' },
            { id: 2, type: 'bank', name: 'Ecobank', number: **************, logo: '' },
            { id: 3, type: 'card', name: 'Visa Card', number: **************, logo: '' },
            { id: 4, type: 'others', name: 'MTN Mobile Money', number: **************, logo: '' },
        ];
        return HttpResponse.json({
            accounts: data
        })
    }),
    http.get('http://localhost:8000/api/accounts/payments', () => {

        return HttpResponse.json({
            payments: []
        })
    }),
    http.get('http://localhost:8000/api/transactions', () => {

        const data = [
            { id: '1', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '2', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '3', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '4', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '5', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '6', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '7', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '8', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '9', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '10', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '11', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '12', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
        ];
        return HttpResponse.json({
            transactions: data
        })
    }),
    http.get('http://localhost:8000/api/transactions/:id', ({ params }) => {

        const data = {
            id: params.id,
            accountNumber: ********,
            status: 'Pending',
            type: 'Monthly',
            name: 'Joseph Nartey',
            amount: 98.54,
            paymentMethod: 'Mobile Money',
            dueDate: '25/09/2025 6:44pm',
            serviceCharge: 9.85,
            note: 'payment for building materials',
            photo: '/images/profile.png',
            timestamp: '24/08/2024 6:45pm'
        }

        return HttpResponse.json({
            transaction: data
        })
    }),
    http.get('http://localhost:8000/api/transactions/refunds', () => {

        const data = [
            { id: '1', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '2', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '3', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '4', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '5', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '6', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '7', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '8', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '9', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '10', accountNumber: ********, status: 'Pending', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '11', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
            { id: '12', accountNumber: ********, status: 'Completed', type: 'Monthly', name: 'Joseph Nartey', amount: 98.54, photo: '/images/profile.png', timestamp: '24/08/2024 6:45pm' },
        ];
        return HttpResponse.json({
            refunds: data
        })
    }),
)