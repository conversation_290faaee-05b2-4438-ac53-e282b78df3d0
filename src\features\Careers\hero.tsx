import { But<PERSON> } from '@/components/button';
import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import { ArrowRight } from 'heroicons-react';
import { Link } from 'react-router-dom';
import { Trans } from 'react-i18next';
import career_hero from '/images/careers_hero.png';
import about_hero from '/images/africa_green.png';
interface heroInterface {
  onJoinClick?: () => void;
  isCareerPage?: boolean;
}
export default function Hero({
  onJoinClick,
  isCareerPage = true
}: heroInterface) {
  return (
    <div className="md:mt-16 mt-5 relative ">
      <section className="border-2 lg:w-max w-fit px-2 rounded-xl mb-4 group ">
        <Button
          variant="secondary"
          component={Link}
          to="/"
          className="flex gap-x-3 md:text-sm text-xs items-center cursor-pointer transition-all duration-200 transform scale-95 group-hover:scale-100"
        >
          <Translate msgId="careers.lookHere" />
          <ArrowRight className="text-raven-green-800" />
        </Button>
      </section>
      <div className="flex w-full flex-col lg:flex-row items-center">
        <section className="flex flex-col gap-y-12 w-full">
          <SimpleText>
            <Trans
              i18nKey="careers.hero"
              components={{
                br: <br />,
                h1: <h1 className="text-5xl font-bold leading-[60px] " />
              }}
            />
          </SimpleText>
          <SimpleText className="md:w-2/3 w-full">
            <Translate msgId="careers.changing" />
          </SimpleText>
          {isCareerPage && (
            <Button
              variant="primary"
              className="rounded-lg w-max"
              onClick={onJoinClick}
            >
              <Translate msgId="careers.join" />
            </Button>
          )}
        </section>
        <img
          src={isCareerPage ? career_hero : about_hero}
          alt="intelligence"
          className="lg:static absolute top-52 left-20 md:left-32 -z-10 lg:w-2/4 transform lg:scale-125"
        />
      </div>
    </div>
  );
}
