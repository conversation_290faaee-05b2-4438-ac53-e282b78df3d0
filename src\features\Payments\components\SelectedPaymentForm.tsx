import { useSelector } from 'react-redux';
import { getOption } from '../paymentsSlice';
import { useMemo } from 'react';
import { paymentOptionStrings } from '@/models/payment-option';
import MomoPaymentForm from './forms/MomoPaymentForm';
import CardPaymentForm from './forms/CardPaymentForm';
import BankPaymentForm from './forms/BankPaymentForm';

const SelectedPaymentForm = () => {
  const selectedOption = useSelector(getOption);

  const Component = useMemo(() => {
    switch (selectedOption) {
      case paymentOptionStrings.card:
        return CardPaymentForm;
      case paymentOptionStrings.bank:
        return BankPaymentForm;
      case paymentOptionStrings.momo:
        return MomoPaymentForm;

      default:
        return 'div';
    }
  }, [selectedOption]);

  return selectedOption && <Component />;
};

export default SelectedPaymentForm;
