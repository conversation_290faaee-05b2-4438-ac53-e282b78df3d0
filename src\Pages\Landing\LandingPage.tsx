import { Header } from 'components/header';
// import Partners from 'features/Landing/partners/Partners';
import Hero from 'features/Landing/hero/Hero';
import ExchangeAppSection from 'features/Landing/exchange-app/ExchangeAppSection';
import Features from 'features/Landing/features/Features';
import Plans from '@/features/Landing/plans/Plans';
import Footer from '@/features/footer/Footer';
import ConvenientMethods from '@/features/Landing/convenient-methods/ConvenientMethods';
import GetStartedSection from '@/features/Landing/get-started-section/GetStartedSection';
import WhyChooseRaven from '@/features/Landing/why-choose-section/WhyChooseRaven';
// import LastSection from '@/features/Landing/last-section/LastSection';
import SecuritySection from '@/features/Landing/security-section/SecuritySection';
import BusinessOnRaven from '@/features/Landing/business-ravenhive/business-on-raven';
import AfterHero from '@/features/Landing/after-hero/after-hero';
const LandingPage = () => {
  return (
    <>
      <Header />
      <main>
        <div className="body">
          <Hero />
        </div>

        <div className="body">
          <AfterHero />
        </div>
        <div className="body">
          <ExchangeAppSection />
        </div>
        <div className="body">
          <Features />
        </div>
        <div className="body">
          <Plans />
        </div>
        <div className="body">
          <WhyChooseRaven />
        </div>
        <div className="body">
          <ConvenientMethods />
        </div>
        <GetStartedSection />
        <div className="body">
          <SecuritySection />
        </div>
        <div className="body">
          <BusinessOnRaven />
        </div>
        <div className="body">{/* <LastSection /> */}</div>
        <Footer />
      </main>
    </>
  );
};

export default LandingPage;
