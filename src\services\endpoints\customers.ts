import customerBaseQuery from "@/services/api/customer-base-query";

const extendedAPI = customerBaseQuery.injectEndpoints({
    overrideExisting: true,
    endpoints: (builder) => ({
        getVendors: builder.query({
            query: () => ({
                url: '/api/customers/vendors',
            })
        }),
        getClients: builder.query({
            query: () => ({
                url: '/api/customers/clients'
            })
        })
    })
})

export const { useGetVendorsQuery, useGetClientsQuery } = extendedAPI