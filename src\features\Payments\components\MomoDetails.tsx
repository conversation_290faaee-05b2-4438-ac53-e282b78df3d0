import { Card, CardHeader, CardContent } from '@/components/Card';
import { Translate } from '@/components/translate';
import MomoDetailsForm from './MomoDetailsForm';

const MomoDetails = () => {
  return (
    <Card
      variant="outlined"
      className="w-full bg-raven-green-50 lg:py-10 lg:px-12"
    >
      <CardHeader
        primary={<Translate msgId="dashboard.momoDetails" />}
        secondary={<Translate msgId="dashboard.enterYourMomo" />}
      />
      <CardContent className="bg-white">
        <MomoDetailsForm />
      </CardContent>
    </Card>
  );
};

export default MomoDetails;
