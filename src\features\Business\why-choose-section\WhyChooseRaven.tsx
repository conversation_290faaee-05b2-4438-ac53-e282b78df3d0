import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import reasons from '@/data/business-reasons.json';
import { Trans } from 'react-i18next';

const WhyChooseRaven = () => {
  return (
    <div className="mt-20 md:py-20 md:px-10">
      <div className="mb-10 space-y-4 text-center">
        <SimpleText
          component="h2"
          className="font-semibold text-3xl md:text-5xl"
        >
          <Trans
            i18nKey="business.whyChooseRavenBusiness"
            components={{
              style: <span className="text-raven-green-800 leading-tight" />,
            }}
          />
        </SimpleText>
      </div>
      <div className="grid gap-3 md:gap-5 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {reasons.map((feature, ind) => (
          <div key={ind} className=" border rounded-md p-3">
            <SimpleText className="font-semibold mb-2">
              <Translate msgId={feature.title} />
            </SimpleText>
            <SimpleText className='text-sm' >
              <Translate msgId={feature.description} />
            </SimpleText>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WhyChooseRaven;
