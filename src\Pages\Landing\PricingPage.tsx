import { Header } from '@/components/header';
import FAQs from '@/features/PricingPage/components/FAQs';
import Footer from '@/features/footer/Footer';
import GrowBeyoundBorders from '@/features/PricingPage/grow-beyound-borders/GrowBeyoundBorders';
import Hero from '@/features/PricingPage/hero/Hero';
import Management from '@/features/PricingPage/management/Management';
import PayoutCalculator from '@/features/PricingPage/payout-calculate/PayoutCalculator';
import GetStarted from '@/features/PricingPage/get-started/GetStarted';
import CoreApi from '@/features/PricingPage/core-api/CoreApi';

const PricingPage = () => {
  return (
    <>
      <Header />
      <main className="pricing">
        <div className="body">
          <Hero />
        </div>
        <div className="body">
          <GrowBeyoundBorders />
        </div>
        <div className="body">
          <PayoutCalculator />
        </div>
        <div className="body">
          <Management />
        </div>
        <div className="body">
          <CoreApi />
        </div>
        <div className="body">
          <GetStarted />
        </div>
        <div className="body">
          <FAQs />
        </div>
        <Footer />
      </main>
    </>
  );
};

export default PricingPage;
