import { useRef, useLayoutEffect, MutableRefObject } from 'react';
import { useDispatch } from 'react-redux';
import { push } from 'connected-react-router';
import useIsAuthorized from './useIsAuthorized';

const useAuthorizedRedirect = (path: string) => {
  const loaded: MutableRefObject<null | boolean> = useRef(null);
  const dispatch = useDispatch();
  const authorized = useIsAuthorized();

  useLayoutEffect(() => {
    if (loaded.current) {
      return;
    }

    loaded.current = true;

    if (!authorized) {
      return;
    }

    dispatch(push(path));
  }, [path, dispatch, authorized]);
};

export default useAuthorizedRedirect;
