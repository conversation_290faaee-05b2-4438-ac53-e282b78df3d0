import { Button } from "@/components/button"
import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"
import IntegrationItem from "@/features/Settings/components/IntegrationItem"
import IntegrationLoading from "@/features/Settings/components/IntegrationLoading"
import NoData from "@/features/Settings/components/NoData"
import useGetIntegrations from "@/features/Settings/hooks/useGetIntegrations"
import { Integration as IntegrationType } from "@/models/settings-types"

interface IntegrationUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: { integrations: IntegrationType[] };
}

const Integration = () => {
  const { loading, integrations } = useGetIntegrations({
    select: ({ isLoading, isUninitialized, data }: IntegrationUtils) => ({
      loading: isLoading || isUninitialized,
      integrations: data?.integrations || [],
    }),
  });

  return (
    <div>
      <div className=" mb-7 py-5 flex w-full flex-col lg:flex-row gap-5 lg:items-center lg:justify-between">
        <div>
          <SimpleText component="h1" className="text-xl">
            <Translate msgId="dashboard.integrations" />
          </SimpleText>
          <SimpleText className=" text-gray-300">
            <Translate msgId="dashboard.integrationSubTitle" />
          </SimpleText>
        </div>
        <Button variant="darkPrimary" className=" w-full lg:w-auto border rounded-lg  ">
          + <Translate msgId="dashboard.createIntegration" />
        </Button>
      </div>
      {loading && (
        <div className=" grid  grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
          <IntegrationLoading loading={loading} />
          <IntegrationLoading loading={loading} />
          <IntegrationLoading loading={loading} />
          <IntegrationLoading loading={loading} />
          <IntegrationLoading loading={loading} />
          <IntegrationLoading loading={loading} />
        </div>
      )}

      {integrations.length > 0 ?
        <div className=" grid  grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
          {integrations.map((integration: IntegrationType) => (
            <IntegrationItem key={integration.id} id={integration.id} name={integration.name} description={integration.description} link={integration.link} logo={integration.logo} isActive={integration.isActive} />
          ))}
        </div>
      : !loading &&(
        <NoData />
      )}
    </div>
  )
}

export default Integration
