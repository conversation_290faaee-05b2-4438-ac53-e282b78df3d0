import { Button } from '@/components/button';
import { Header } from '@/components/header';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import Consumers from '@/features/Enterprise/consumers/Consumers';
import Hero from '@/features/Enterprise/hero/Hero';
import Solution from '@/features/Enterprise/solution/Solution';
import Footer from '@/features/footer/Footer';
import { Trans } from 'react-i18next';
import Actions from '@/features/Enterprise/actions/actions';
import { ArrowRight } from 'heroicons-react';
const EnterprisePage = () => {
  return (
    <>
      <Header />
      <main>
        <div className="body">
          <Hero />
        </div>
        <div className="body">
          <Consumers />
        </div>
        <div className="body">
          <Actions />
        </div>
        <div className="body">
          <Solution />
        </div>

        <div className="body">
          <div className="lg:max-w-7xl mx-auto place-items-center grid grid-cols-1 md:grid-cols-2">
            <div className="p-5 !leading-10 space-y-5 flex-col justify-around flex gap-10">
              <SimpleText
                component="h2"
                className="!leading-10  font-semibold text-3xl md:text-5xl"
              >
                <Trans
                  className="!leading-10"
                  i18nKey="enterprise.solutionsTitle2"
                  components={{
                    style: <span className="text-raven-green-800 !leading-10" />
                  }}
                />
              </SimpleText>

              <div className="flex gap-5">
                <Button variant="darkPrimary" className="rounded-md">
                  <Translate msgId="home.getInTouch" />
                </Button>
                <Button variant="light" className="rounded-md">
                  <Translate msgId="home.docs" />
                </Button>
              </div>
            </div>

            <div className="p-5 space-y-5 relative mb-20">
              <span className="bg-gray-100 dark:bg-raven-green-900 w-2 h-[80%] absolute -left-5 top-[10%] flex flex-col justify-between items-center">
                <div className="rounded-full h-10 w-10 bg-raven-green-800 grid place-items-center text-white ">
                  <ArrowRight />
                </div>
                <div className="rounded-full h-10 w-10 bg-raven-green-800 grid place-items-center text-white">
                  <ArrowRight />
                </div>
                <div className="rounded-full h-10 w-10 bg-raven-green-800 grid place-items-center text-white">
                  <ArrowRight />
                </div>
              </span>
              {[1, 2, 3].map((number, index) => (
                <div
                  className="bg-gray-100 rounded-md p-5 shadow-xl space-y-3 w-64 border-l-8 border-l-raven-green-800"
                  key={index}
                >
                  <SimpleText className="font-semibold mb-2">
                    <Translate msgId={`enterprise.integrationTitle${number}`} />
                  </SimpleText>
                  <SimpleText className="text-sm">
                    <Translate
                      msgId={`enterprise.integrationDescription${number}`}
                    />
                  </SimpleText>
                </div>
              ))}
            </div>
          </div>
        </div>
        <Footer />
      </main>
    </>
  );
};

export default EnterprisePage;
