import { FC, useMemo } from 'react';
import { Link, useLocation } from 'react-router-dom';
import clsx from '@/features/utils/clsx';
import { TabProps } from '@/models/tab-routes';

const Tab: FC<TabProps> = (props) => {
  const { to, label, Icon, disabled } = props;
  const location = useLocation();
  const selected = useMemo(
    () => location.pathname === to,
    [location.pathname, to]
  );

  const rootClass = clsx(
    'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm group inline-flex items-center',
    {
      'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
        !selected,
      'border-raven-green-500 text-raven-green-800': selected,
      'opacity-50 cursor-default pointer-events-none': disabled,
    }
  );

  const iconClass = clsx('-ml-0.5 mr-2 h-5 w-5', {
    'text-gray-400 group-hover:text-gray-500': !selected,
    'text-raven-green-500': selected,
    'opacity-50 cursor-default pointer-events-none': disabled,
  });

  return (
    <Link to={to} className={rootClass}>
      {Icon && <Icon className={iconClass} />}
      {label}
    </Link>
  );
};

export default Tab;
