import { Avatar } from "@/components/avatar"
import { <PERSON><PERSON> } from "@/components/button"
import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"

const Profile = () => {
  // const payload = sessionStorage.getItem('@payload')
  // const user = JSON.parse(payload as string)
  
  return (
    <div>
      <div className=" border-b-2">
        <SimpleText component="h1" className="text-xl">
          <Translate msgId="dashboard.profileDetails" />
        </SimpleText>
        <SimpleText className="text-gray-300">
          <Translate msgId="dashboard.profileDetailsSubTitle" />
        </SimpleText>
      </div>

      <div>
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
          <SimpleText className="text-gray-300 text-lg lg:w-[30%]">
            <Translate msgId="dashboard.companyName" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full lg:w-[478px]"
            type="text"
            value={''}
            placeholder={'Company Name'}
          />
        </div>
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
          <SimpleText className="text-gray-300 text-lg lg:w-[30%]">
            <Translate msgId="dashboard.emailAddress" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full lg:w-[478px]"
            type="text"
            value={''}
            placeholder={'Email Address'}
          />
        </div>
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
          <SimpleText className="text-gray-300 text-lg lg:w-[30%]">
            <Translate msgId="dashboard.phoneNumber" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full lg:w-[478px]"
            type="text"
            value={''}
            placeholder={'Phone Number'}
          />
        </div>
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
          <SimpleText className="text-gray-300 text-lg lg:w-[30%]">
            <Translate msgId="dashboard.companyWebsite" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full lg:w-[478px]"
            type="text"
            value={''}
            placeholder={'Website'}
          />
        </div>
        <div className="mb-3 form-group sm:block lg:flex items-center w-full space-y-3 py-5 border-b-2">
          <div className="  lg:w-[30%]">
            <SimpleText component="h1" className="font-semibold text-lg">
              <Translate msgId="dashboard.profilePhoto" />
            </SimpleText>
            <SimpleText component="p" className="text-gray-300">
              <Translate msgId="dashboard.profilePhotoSubTitle" />
            </SimpleText>
          </div>

          <div className=" flex gap-10">
            <Avatar size="xl" radius="full" src={'/images/profile.png'} alt="Profie Photo" />
            <SimpleText component="h1" className="font-semibold text-raven-green-800 cursor-pointer">
              <label htmlFor="photo" className=" cursor-pointer">
                <Translate msgId="dashboard.uploadProfile" />
              </label>
              <input type="file" hidden id="photo" />
            </SimpleText>
            <SimpleText component="h1" className="font-semibold text-gray-300 cursor-pointer">
              <Translate msgId="dashboard.delete" />
            </SimpleText>
          </div>
        </div>
      </div>

      <div className="flex w-full flex-col lg:flex-row gap-5 items-center justify-between mt-5">
        <Button variant="darkPrimary" className=" w-full lg:w-[250px] rounded-lg">
          <Translate msgId="dashboard.updateProfile" />
        </Button>
        <div className="group w-full lg:w-[200px]">
          <Button variant="light" className=" w-full transition-all rounded-lg border-raven-green-800 !text-raven-green-800 group-hover:bg-raven-green-800 group-hover:!text-white">
            <Translate msgId="dashboard.cancel" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Profile