import { ElementType, FC, Fragment, useEffect, useState } from 'react';
import DashboardProvider from './DashboardProvider';
import DashboardNavigation from './DashboardNavigation';
import DashboardSidebar from './DashboardSidebar';
import DashboardHeader from './DashboardHeader';
import DashboardBody from './DashboardBody';
import {
  Collection,
  Cog,
  Identification,
  Briefcase,
  Users,
  ReceiptRefund,
  ClipboardCheck,
  ShieldCheck,
  UserAdd,
  PresentationChartLine
} from 'heroicons-react';
import { Ping } from 'components/ping';
import { Translate } from '@/components/translate';
import { useMatch } from 'react-router-dom';
import useAuthRedirect from '@/hooks/useAuthRedirect';

interface DashboardRouteProps {
  redirectUnauthorized?: boolean;
  component: ElementType;
  [key: string]: any;
}

const DashboardRoute: FC<DashboardRouteProps> = ({
  redirectUnauthorized = false,
  component: Component,
  ...rest
}) => {
  const admin = true;
  const isMerchant = true; // will be updated after user login

  // Check authentication
  const { isAuthStatusLoading, isAuthenticated } = useAuthRedirect();

  // Prevent rendering until auth status is confirmed
  if (isAuthStatusLoading || !isAuthenticated) {
    return null;
  }

  // Match the current url to payments route to tell if it is active
  const paymentSubmenuPage = Boolean(useMatch('/dashboard/payments/*'));
  const [showPaymentMenu, setShowPaymentMenu] = useState<boolean>(false);

  useEffect(() => {
    setShowPaymentMenu(paymentSubmenuPage);
  }, [paymentSubmenuPage]);

  return (
    <DashboardProvider>
      {isAuthenticated && (
        <DashboardSidebar>
          <DashboardNavigation>
            {isMerchant ? (
              <Fragment>
                <DashboardNavigation
                  item
                  href={'/dashboard'}
                  icon={Identification}
                >
                  Dashboard
                </DashboardNavigation>
                <DashboardNavigation
                  item
                  href="/dashboard/payments"
                  icon={Collection}
                  subnav
                  open={showPaymentMenu}
                >
                  <Translate msgId="dashboard.payments" />
                </DashboardNavigation>
                {showPaymentMenu && (
                  <>
                    <Ping position="center-right" active={true}>
                      <DashboardNavigation
                        item
                        href="/dashboard/payments"
                        depth="1"
                      >
                        <Translate msgId="dashboard.pay" />
                      </DashboardNavigation>
                    </Ping>
                    <DashboardNavigation
                      item
                      href="/dashboard/payments/get-paid"
                      depth="1"
                    >
                      <Translate msgId="dashboard.getPaid" />
                    </DashboardNavigation>
                    <Ping position="center-right" active={false}>
                      <DashboardNavigation
                        item
                        href="/dashboard/payments/payment-methods"
                        depth="1"
                      >
                        <Translate msgId="dashboard.paymentMethods" />
                      </DashboardNavigation>
                    </Ping>
                  </>
                )}
              </Fragment>
            ) : (
              <DashboardNavigation
                item
                href="/dashboard/subscriptions"
                icon={Collection}
              >
                Subscriptions
              </DashboardNavigation>
            )}
            <DashboardNavigation
              item
              href="/dashboard/transactions"
              icon={ClipboardCheck}
            >
              <Translate msgId="dashboard.transactions" />
            </DashboardNavigation>
            <DashboardNavigation
              item
              href="/dashboard/transactions/refund"
              icon={ReceiptRefund}
            >
              <Translate msgId="dashboard.refund" />
            </DashboardNavigation>
            {isMerchant && (
              <Fragment>
                <DashboardNavigation
                  item
                  href="/dashboard/customers/vendors"
                  nestedHref="/dashboard/customers/clients"
                  icon={Users}
                >
                  <Translate msgId="dashboard.customers" />
                </DashboardNavigation>
                <DashboardNavigation
                  item
                  href="/dashboard/sales"
                  icon={Briefcase}
                >
                  Sales
                </DashboardNavigation>
              </Fragment>
            )}
            <DashboardNavigation
              item
              href="/dashboard/statistics"
              icon={PresentationChartLine}
            >
              <Translate msgId="dashboard.statistics" />
            </DashboardNavigation>
            <DashboardNavigation
              item
              href="/dashboard/settings/profile"
              icon={Cog}
            >
              <Translate msgId="dashboard.settings" />
            </DashboardNavigation>
            {admin && (
              <DashboardNavigation
                item
                href="/dashboard/admin"
                icon={ShieldCheck}
              >
                Admin
              </DashboardNavigation>
            )}
            <DashboardNavigation
              item
              href="/dashboard/settings/accounts"
              subnav
              icon={UserAdd}
            >
              <Translate msgId="dashboard.subAccount" />
            </DashboardNavigation>
          </DashboardNavigation>
        </DashboardSidebar>
      )}
      <DashboardBody>
        <DashboardHeader
          disableNotifications
          disableMenu={!isAuthenticated}
          disableLogo={isAuthenticated}
        />
        <Component {...rest} />
      </DashboardBody>
    </DashboardProvider>
  );
};

export default DashboardRoute;
