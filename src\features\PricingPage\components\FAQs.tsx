import { Accordion } from "@/components/accordion";
import { Translate } from "@/components/translate";
import { SimpleText } from "@/components/typography";

const FAQs = () => {
  return (
    <section className='py-5 lg:py-28'>
      <section className=' container mx-auto '>
        <SimpleText className=" text-center font-semibold text-4xl lg:text-5xl ">
          <Translate msgId="landing.faq" />
        </SimpleText>

        <div className='space-y-10 lg:w-3/5 mx-auto px-5 lg:px-0  pt-10 md:pt-16'>
          <Accordion data={items} itemClass="bg-[#191919] shadow-md " />
        </div>
      </section>
    </section>
  );
};

export default FAQs;

const items = [
  {
    title: "landing.pricingFaq1",
    content: "",
  },
  {
    title: "landing.pricingFaq2",
    content: "",
  },
  {
    title: "landing.pricingFaq3",
    content: "",
  },
];
