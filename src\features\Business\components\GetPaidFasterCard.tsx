import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { Button } from '@/components/button';
import { DocumentDuplicate } from 'heroicons-react';

const GetPaidFasterCard = () => {
  return (
    <div className=" p-4 ">
      <div className=" w-full ">
        <SimpleText className="text-lg mb-5">
          <Translate msgId={'Your payment link'} />
        </SimpleText>
        <div className="mb-3  flex justify-between gap-3  items-center w-full ">
        <div className="p-4 border text-raven-green-800 w-full bg-[#F8FFF9] rounded-lg">
          <SimpleText className=" text-sm w-56 overflow-hidden">
            www.raven.com/payment/id=9872bac
          </SimpleText>
        </div>
          <Button variant="light" className="!text-raven-green-800 !p-4 !bg-[#F8FFF9]">
            <DocumentDuplicate />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GetPaidFasterCard;
