import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import CustomersLoading from '@/features/Customers/components/CustomersLoading';
import useGetClients from '@/features/Customers/hooks/useGetClients';
import { Client } from '@/models/customer-types';
import NoCustomer from '@/features/Customers/components/NoCustomer';
import { Button } from '@/components/button';
import CustomerItem from './CustomerItem';

interface OverviewUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: { clients: Client[] };
}

const ClientList = () => {
  const { loading, clients } = useGetClients({
    select: ({ isLoading, isUninitialized, data }: OverviewUtils) => ({
      loading: isLoading || isUninitialized,
      clients: data?.clients || [],
    }),
  });

  return (
    <div className="px-5 overflow-y-scroll max-h-72 py-3">
      <div className="flex flex-col gap-5">
        {loading && (
          <div className="space-y-5">
            <CustomersLoading loading={loading} />
            <CustomersLoading loading={loading} />
            <CustomersLoading loading={loading} />
          </div>
        )}
        {clients.length > 0
          ? clients.map((client: Client) => (
              <CustomerItem customer={client} key={client.id} />
            ))
          : !loading && (
              <NoCustomer>
                <SimpleText className='text-gray-500'>
                  <Translate msgId="dashboard.addClientToList" />
                </SimpleText>
                <Button variant="darkPrimary" className="text-sm rounded-lg">
                  <b>+ </b> <Translate msgId="dashboard.addClient" />
                </Button>
              </NoCustomer>
            )}
      </div>
    </div>
  );
};

export default ClientList;
