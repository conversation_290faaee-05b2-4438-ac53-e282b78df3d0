import { createAsyncThunk, createSelector, createSlice } from "@reduxjs/toolkit";
import api, { cancelToken } from "@/services/api/api";
import { CurrencyObject, currencyList } from "@/models/currency-object.interface";
import { RootState } from "@/store/store";
import { doConversion } from "@/features/utils/do-conversion";

/**
 * Async thunk action to request exchange rates.
 *
 * @function
 * @async
 * @param {object} options - The options for the request.
 * @param {string} options.base - The base currency for conversion rates.
 * @returns {Promise<object>} - The response object containing rates and timestamp.
 */
export const requestExchangeRates = createAsyncThunk('requestExchangeRates', async (options: { base: string }, { signal, rejectWithValue }) => {

    const source = cancelToken.source()
    const params = { base: options.base }

    signal.addEventListener('abort', () => source.cancel())

    try {
        const response = await api.get(`currency/convert`, { params })
        return response.data

    } catch (error) {
        return rejectWithValue(error)
    }
})

/**
 * @typedef {object} exchangeAppState
 * @property {number} conversionRate - The conversion rate for selected currencies.
 * @property {CurrencyObject} baseCurrency - The selected base currency object.
 * @property {CurrencyObject} receivingCurrency - The selected receiving currency object.
 * @property {number} baseInput - The amount in base currency.
 * @property {number} convertingInput - The amount in receiving currency after conversion.
 * @property {object|null} rates - The conversion rates.
 * @property {number} timestamp - The timestamp when rates were received.
 * @property {boolean} loading - The loading state for rates request.
 * @property {unknown|null} error - The error state for rates request.
 */
interface exchangeAppState {
    conversionRate: number;
    baseCurrency: CurrencyObject;
    receivingCurrency: CurrencyObject;
    baseInput: number;
    convertingInput: number;
    rates: { [key: string]: number } | null;
    timestamp: number;
    loading: boolean;
    error: any
    changeConvertor: { prev: CurrencyObject, current: CurrencyObject, id: number }
}

/**
 * The initial state for the exchange app slice.
 * 
 * @type {exchangeAppState}
 */
const initialState: exchangeAppState = {
    conversionRate: 0,
    baseCurrency: currencyList[0],
    receivingCurrency: currencyList[currencyList.length - 1],
    baseInput: 1000,
    convertingInput: 0,
    rates: null,
    timestamp: 1,
    loading: false,
    error: null,
    changeConvertor: { prev: currencyList[currencyList.length - 1], current: currencyList[0], id: 1 }

}

/**
 * Slice for managing state in the currency exchange app.
 *
 * @type {Slice}
 */
export const exchangeAppSlice = createSlice({
    name: 'exchangeApp',
    initialState,
    reducers: {
        setConvertingInput: (state, action) => {
            state.convertingInput = action.payload.value
            // We want to allow convertion from the converting input 
            // so we will set it as the new convertor and base
            state.changeConvertor = { prev: state.changeConvertor.current, current: state.changeConvertor.prev, id: action.payload.id }
            state.receivingCurrency = state.changeConvertor.prev
            state.baseCurrency = state.changeConvertor.current
            state.baseInput = state.convertingInput
        },
        setBaseInput: (state, action) => {
            state.baseInput = action.payload
        },
        setBaseCurrency: (state, action) => {
            state.baseCurrency = action.payload
            // if the selected currency is not consistent with the convertor state
            if (state.changeConvertor.current.id !== action.payload.id) {
                state.changeConvertor.current = action.payload
            }
        },
        setReceivingCurrency: (state, action) => {
            state.receivingCurrency = action.payload
            // if the selected currency is not consistent with the convertor state
            if (state.changeConvertor.prev.id !== action.payload.id) {
                state.changeConvertor.prev = action.payload
            }
        },
    },
    extraReducers: (builder) => {
        builder.addCase(requestExchangeRates.pending, (state) => {
            state.error = null,
                state.loading = true
        }).addCase(requestExchangeRates.fulfilled, (state, action) => {
            state.rates = action.payload.rates
            state.timestamp = action.payload.timestamp
            state.error = null
            state.loading = false
        }).addCase(requestExchangeRates.rejected, (state, action) => {
            state.loading = false
            state.error = action.payload
        })
    }
})

const { name, actions } = exchangeAppSlice
export const { setConvertingInput, setBaseInput, setBaseCurrency, setReceivingCurrency } = actions

/**
 * Selector function to get the exchangeApp slice from the Redux state.
 *
 * @param {RootState} state - The root Redux state.
 * @returns {exchangeAppState} - The exchangeApp slice from the Redux state.
 */
const getSlice = (state: RootState): exchangeAppState => state[name]

/**
 * Selector to get the converting input value from the Redux state.
 *
 * @function
 * @param {RootState} state - The root state of the Redux store.
 * @returns {number} - The converting input value.
 */
export const getConvertingInput = createSelector(getSlice, (slice) => {
    return slice.convertingInput
})

export const getConvertorInput = createSelector(getSlice, (slice) => {
    return slice.baseInput
})

export const getSelectedBaseCurrency = createSelector(getSlice, (slice) => {
    return slice.baseCurrency
})

export const getSelectedReceivingCurrency = createSelector(getSlice, (slice) => {
    return slice.receivingCurrency
})

export const getRates = createSelector(getSlice, (slice) => {
    return slice.rates
})


/**
 * Factory function to create a selector for getting conversion value.
 *
 * @function
 * @returns {Function} - The selector instance to get the conversion value.
 */
export const makeGetConversion = () => {
    return createSelector(
        [getRates, (_, utils) => utils],
        (rates, utils) => {
            const options = { rates, base: utils.base, receiving: utils.receiving }
            const exchangeRate = doConversion(options)
            if (!exchangeRate) {
                return null
            }
            const { input } = utils
            const result = input * exchangeRate
            return result.toFixed(2)
        }
    );
};

export const getRatesLoading = createSelector(getSlice, (slice) => {
    return slice.loading
})

export const getRatesError = createSelector(getSlice, (slice) => {
    return slice.error
})

export const getTimestamp = createSelector(getSlice, (slice) => {
    return slice.timestamp
})

export const getConvertorInputField = createSelector(getSlice, (slice) => {
    return slice.changeConvertor
})