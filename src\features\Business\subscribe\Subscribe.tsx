import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { TextSecondary } from '@/components/typography/Text';
import { Trans } from 'react-i18next';
import { Button } from '@/components/button';

const Subscribe = () => {
  return (
    <div className="mt-20 md:py-20 md:px-10">
      <div className="mb-10 space-y-4 text-center">
        <SimpleText
          component="h2"
          className="font-semibold text-3xl md:text-5xl"
        >
          <Trans
            i18nKey="business.subsTitle"
            components={{
              style: <span className="text-raven-green-800 leading-tight" />,
            }}
          />
        </SimpleText>
        <TextSecondary size="lg" shade="light">
          <Translate msgId="business.subsDesc" />
        </TextSecondary>
      </div>
      <div className="flex-col md:flex-row flex gap-3 items-center justify-center rounded-md p-3">
        <input
          className="w-full md:w-80 dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
          type="text"
          placeholder="Enter your email"
        />
        <Button variant="darkPrimary" className="rounded-md w-full md:w-auto">
          <Translate msgId="business.subscribe" />
        </Button>
      </div>
    </div>
  );
};

export default Subscribe;
