import { Logo } from 'components/logo';
import { Translate } from 'components/translate';
import { SimpleText } from 'components/typography';
import { Link, useNavigate } from 'react-router-dom';
import pos from '/images/pos.png';
import SigninForm from 'features/Signin/SigninForm';
import { getCookie } from '@/features/utils/cookie';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { getRedirect } from '@/features/Signin/LoginSlice';

const SigninPage = () => {
  const accessToken = getCookie('auth');
  const redirect = useSelector(getRedirect);

  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to the dashboard page if accessToken is available
    if (redirect || accessToken) {
      navigate('/dashboard');
    }
  }, [accessToken, navigate, redirect]);

  return (
    <div className="mt-10 signup-page">
      <div className="flex md:justify-end md:pr-20 pl-8">
        <div className="w-28">
          <Link to="/">
            <Logo className="w-100" />
          </Link>
        </div>
      </div>
      <div>
        <div className="md:flex w-full gap-4 justify-between">
          <div className="w-full md:w-1/2 p-10 md:p-20">
            <div className="text-center">
              <div className="mb-5">
                <SimpleText component="h1" className="font-bold text-2xl mb-3">
                  <Translate msgId="auth.signin" />
                </SimpleText>
                <SimpleText>
                  <Translate msgId="auth.signinSub" />
                </SimpleText>
              </div>
              <SigninForm />
              <div className="flex justify-center gap-3 mb-10">
                <SimpleText>
                  <Translate msgId="auth.noAccount" />
                </SimpleText>
                <Link to="/signup">
                  <SimpleText className="text-raven-link">
                    <Translate msgId="auth.signup" />
                  </SimpleText>
                </Link>
              </div>
            </div>
          </div>
          <div className="hidden md:block shrink relative">
            <div className="relative h-hull flex flex-col justify-between">
              <div className="relative z-1">
                <img src={pos} alt="pos system" />
              </div>
              {/* <div className="flex justify-end">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="534"
                  height="511"
                  viewBox="0 0 534 511"
                  fill="none"
                >
                  <path
                    d="M95.6817 52.7312C254.426 52.7311 538.733 -50.5844 583.98 32.3698C622.229 102.493 585.305 462.534 572.967 572.235C570.923 590.408 555.533 604 537.246 604H120.406C105.496 604 92.1512 594.91 87.0632 580.895C49.1129 476.361 -93.0405 52.7312 95.6817 52.7312Z"
                    fill="#11E342"
                  />
                </svg>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SigninPage;
