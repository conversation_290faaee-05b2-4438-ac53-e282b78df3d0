import clsx from 'features/utils/clsx';
import { FC, ReactNode } from 'react';

interface DashboardContainerProps {
  children: ReactNode;
  className: string;
}
const DashboardContainer: FC<DashboardContainerProps> = ({
  children,
  className,
}) => (
  <div
    className={clsx('rounded-lg shadow-lg bg-white overflow-hidden', className)}
  >
    {children}
  </div>
);

export default DashboardContainer;
