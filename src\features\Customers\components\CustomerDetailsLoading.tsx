import { Skeleton } from "@/components/skeleton";
import { FC } from "react";

interface CustomerDetailsLoadingProps {
  loading: boolean;
}

const CustomerDetailsLoading: FC<CustomerDetailsLoadingProps> = ({ loading }) => {
  return (
    <div className="border-none space-y-5 bg-gray-100 p-5 rounded h-full">
      <div className=" flex justify-between items-center">
        <div className=" flex gap-3 items-center flex-1">
          <Skeleton active={loading} className="bg-white w-[50px] h-[50px] rounded-[30px]">
            <div className="p-3 outline bg-white h-4 outline-2 outline-gray-100 rounded-lg"></div>
          </Skeleton>
          <Skeleton active={loading} className="bg-white w-52">
            <div className="p-3 outline bg-white h-4 outline-2 outline-gray-100 rounded-lg"></div>
          </Skeleton>
        </div>
      </div>

      <div className=" bg-white p-5 space-y-5 rounded-md">
        <Skeleton active={loading} className=" w-[50%] bg-gray-300 h-[20px] rounded-[20px]">
          <div className="p-3 outline rounded-full outline-2 outline-gray-100 "></div>
        </Skeleton>

        <Skeleton active={loading} className=" h-[40px] rounded-[20px] w-[80%]">
          <div className="p-5 outline outline-2 outline-gray-100 "></div>
        </Skeleton>

        <Skeleton active={loading} className="w-[80%]">
          <div className="p-5 outline outline-2 outline-gray-100 "></div>
        </Skeleton>

        <Skeleton active={loading} className=" h-[40px] rounded-[20px] w-[80%]">
          <div className="p-5 outline outline-2 outline-gray-100 "></div>
        </Skeleton>

        <Skeleton active={loading} className="w-[80%]">
          <div className="p-5 outline outline-2 outline-gray-100 "></div>
        </Skeleton>
      </div>
    </div>
  )
};

export default CustomerDetailsLoading;
