import { But<PERSON> } from "@/components/button"
import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"

const Api = () => {
  return (
    <div>
      <div className=" py-5 border-b-2 flex w-full flex-col lg:flex-row gap-5 lg:items-center lg:justify-between">
        <div>
          <SimpleText component="h1" className="text-xl">
            <Translate msgId="dashboard.apiAndWebhooks" />
          </SimpleText>
          <SimpleText className=" text-gray-300">
            <Translate msgId="dashboard.apiSubTitle" />
          </SimpleText>
        </div>
        <Button variant="light" className=" w-full lg:w-auto border rounded-lg  ">
          <Translate msgId="dashboard.goToDocs" />
        </Button>
      </div>

      <div>
        <div className="mt-10">
          <SimpleText component="h1" className="text-xl">
            <Translate msgId="dashboard.liveMode" />
          </SimpleText>
          <SimpleText className=" text-gray-300">
            <Translate msgId="dashboard.liveModeSubTitle" />
          </SimpleText>

          <div className="mb-3 gap-5 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
            <div className=" w-full lg:w-[60%]">
              <SimpleText className="text-lg ">
                <Translate msgId="dashboard.privateKey" />
              </SimpleText>
              <div className="flex justify-between flex-wrap  items-center gap-2 w-full bg-gray-100 p-2 rounded-lg">
                <SimpleText className=" text-sm">
                  aldjfkadjfao3u487390eriqu397483459isfju
                </SimpleText>
                <Button variant="light" className=" !p-1 !px-4">
                  <Translate msgId="dashboard.copy" />
                </Button>
              </div>
            </div>
            <div className=" w-full lg:w-[40%]">
              <SimpleText className="text-lg ">
                <Translate msgId="dashboard.callbackUrl" />
              </SimpleText>
              <input
                className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="text"
                value=''
                placeholder="https://example.com/callback"
              />
            </div>
          </div>
          
          <div className="mb-3 gap-5 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
            <div className=" w-full lg:w-[60%]">
              <SimpleText className="text-lg ">
                <Translate msgId="dashboard.publicKey" />
              </SimpleText>
              <div className="flex justify-between flex-wrap items-center gap-2 bg-gray-100 p-2 rounded-lg">
                <SimpleText className=" text-sm">
                  aldjfkadjfao3u487390eriqu397483459isfju
                </SimpleText>
                <Button variant="light" className=" !p-1 !px-4">
                  <Translate msgId="dashboard.copy" />
                </Button>
              </div>
            </div>
            <div className=" w-full lg:w-[40%]">
              <SimpleText className="text-lg ">
                <Translate msgId="dashboard.webhookUrl" />
              </SimpleText>
              <input
                className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="text"
                value=''
                placeholder="https://example.com/webhook"
              />
            </div>
          </div>
        </div>
        
        <div className="mt-10">
          <SimpleText component="h1" className="text-xl">
            <Translate msgId="dashboard.testKeys" />
          </SimpleText>
          <SimpleText className=" text-gray-300">
            <Translate msgId="dashboard.testKeySubTitle" />
          </SimpleText>
          <div className=" bg-orange-50 rounded-lg p-5 my-4 flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-orange-400">
              <path strokeLinecap="round" strokeLinejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
            </svg>

            <SimpleText className=" text-sm">
              <Translate msgId="dashboard.neverUseTestKey" />
            </SimpleText>
          </div>

          <div className="mb-3 gap-5 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
            <div className=" w-full lg:w-[60%]">
              <SimpleText className="text-lg ">
                <Translate msgId="dashboard.privateKey" />
              </SimpleText>
              <div className="flex justify-between flex-wrap items-center gap-2 bg-gray-100 p-2 rounded-lg">
                <SimpleText className=" text-sm">
                  aldjfkadjfao3u487390eriqu397483459isfju
                </SimpleText>
                <Button variant="light" className=" !p-1 !px-4">
                  <Translate msgId="dashboard.copy" />
                </Button>
              </div>
            </div>
            <div className=" w-full lg:w-[40%]">
              <SimpleText className="text-lg ">
                <Translate msgId="dashboard.callbackUrl" />
              </SimpleText>
              <input
                className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="text"
                value=''
                placeholder="https://example.com/callback"
              />
            </div>
          </div>
          
          <div className="mb-3 gap-5 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
            <div className=" w-full lg:w-[60%]">
              <SimpleText className="text-lg ">
                <Translate msgId="dashboard.publicKey" />
              </SimpleText>
              <div className="flex justify-between flex-wrap items-center gap-2 bg-gray-100 p-2 rounded-lg">
                <SimpleText className=" text-sm">
                  aldjfkadjfao3u487390eriqu397483459isfju
                </SimpleText>
                <Button variant="light" className=" !p-1 !px-4">
                  <Translate msgId="dashboard.copy" />
                </Button>
              </div>
            </div>
            <div className=" w-full lg:w-[40%]">
              <SimpleText className="text-lg ">
                <Translate msgId="dashboard.webhookUrl" />
              </SimpleText>
              <input
                className=" w-full dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input "
                type="text"
                value=''
                placeholder="https://example.com/webhook"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end w-full flex-col lg:flex-row gap-5 items-center mt-5">
        <div className="group w-full lg:w-auto">
          <Button variant="light" className=" w-full transition-all rounded-lg border-raven-green-800 !text-raven-green-800 group-hover:bg-raven-green-800 group-hover:!text-white">
            <Translate msgId="dashboard.generateKey" />
          </Button>
        </div>
        <Button variant="darkPrimary" className=" w-full lg:w-auto rounded-lg">
          <Translate msgId="dashboard.saveChanges" />
        </Button>
      </div>
    </div>
  )
}

export default Api