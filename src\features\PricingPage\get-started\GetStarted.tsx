import { Button } from '@/components/button'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'

const GetStarted = () => {
  return (
    <div>
      <section className='py-5 lg:py-10'>
        <section className=' container mx-auto '>
          <SimpleText className=" text-center font-semibold lg:w-1/2  mx-auto leading-tight text-4xl  ">
            <Translate msgId="landing.getStartedTitle" />
          </SimpleText>

          <div className='space-y-10 lg:w-1/2 mx-auto px-5 lg:px-0  pt-6 md:pt-10'>
            <SimpleText className=" lg:w-1/2 text-center  mx-auto">
              <Translate msgId="landing.getStartedDes" />
            </SimpleText>
            <div className="mt-6 flex-col md:flex-row gap-3 flex md:items-center justify-center">
              <Button
                variant="light"
                className="mr-5 !bg-raven-dark-900 !text-white border-2 !border-raven-green-800 rounded "
                type="button"
              >
                <Translate msgId="landing.contactSales" />
              </Button>
            </div>
          </div>
        </section>
      </section>
    </div>
  )
}

export default GetStarted