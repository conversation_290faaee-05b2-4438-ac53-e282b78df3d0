import { Button } from '@/components/button';
import OTPInput from '@/components/input/OtpInput';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import { getOTPSuccess, handleOTPConfirm } from '@/features/Signup/SignupSlice';
import { getSessionItem } from '@/features/utils/get-session-item';
import { AppDispatch } from '@/store/store';
import { ArrowLeft } from 'heroicons-react';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

const ConfirmWithdrawal = () => {
  const auth = getSessionItem('auth');
  const dispatch: AppDispatch = useDispatch();
  const otpSuccess = useSelector(getOTPSuccess);

  const [otp, setOtp] = useState<string>('');

  const handleConfirm = (e: MouseEvent, userOtp: string) => {
    e.preventDefault();
    if (!userOtp || userOtp.length !== 4) return;
    dispatch(handleOTPConfirm({ otp: userOtp }));
  };

  useEffect(() => {
    if (otpSuccess) {
      // dispatch(sendUserDetails());
    }
  }, [otpSuccess, dispatch]);

  return (
    <div className="p-5 lg:p-10 w-screen h-screen">
      <Button
        component={Link}
        variant="transparent"
        to="/dashboard/payments/get-paid"
        className="!p-0 text-center w-fit flex gap-5 rounded-md mb-2"
      >
        <ArrowLeft />
        <Translate msgId="dashboard.back" />
      </Button>

      <div className=" flex flex-col items-center gap-10 justify-center p-5 lg:h-full">
        <div className="flex items-center justify-center flex-col">
          <SimpleText
            component="h1"
            className="font-semibold text-center text-3xl"
          >
            <Translate msgId="dashboard.enterSecretCode" />
          </SimpleText>
            <SimpleText className="text-gray-400 text-center md:w-2/3">
              <Translate
                msgId="dashboard.withdrawalEmailOtpSent"
                value={{ email: JSON.parse(auth!)?.email }}
              />
            </SimpleText>
        </div>

        <div>
          <OTPInput
            inputStyle={
              'w-12 h-20 rounded form-input font-bold text-3xl dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
            }
            inputType="text"
            value={otp}
            onChange={setOtp}
            numInputs={4}
            renderSeparator={<span className="mr-4" />}
            renderInput={(props) => <input {...props} />}
          />
        </div>

        <Button
          variant="darkPrimary"
          onClick={(e: MouseEvent) => handleConfirm(e, otp)}
          component={Link}
          to="/dashboard"
          className="block rounded-md w-60 py-3 px-4 text-center"
        >
          <SimpleText component="p">
            <Translate msgId="auth.confirm" />
          </SimpleText>
        </Button>
        <div>
          <Button
            variant="transparent"
            className="text-raven-link"
            type="button"
          >
            <SimpleText component="p">
              <Translate msgId="auth.resendCode" />
            </SimpleText>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmWithdrawal;
// 0e9b582
