import { Card, CardContent } from '@/components/Card';
import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { Skeleton } from '@/components/skeleton';
import { DashboardContent, DashboardGrid } from '@/layouts/dashboard';
import { Avatar } from '@/components/avatar';
import { SimpleText } from '@/components/typography';
import ScheduleBillModal from '@/features/Payments/components/ScheduleBillModal';
import useGetTransaction from '@/features/Transactions/hooks/useGetTransaction';
import { TransactionDetails } from '@/models/transactions';
import { useParams } from 'react-router-dom';
import formatCurrencyUtil from '@/features/utils/formatCurrency';
import ActionsDropdown from '@/components/button/ActionsDropdown/ActionsDropdown';
import { useState } from 'react';
import { FolderDownload, Pencil } from 'heroicons-react';

interface TransactionUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: { transaction: TransactionDetails };
}
const TransactionDetailsPage = () => {
  const { id } = useParams();

  const { loading, transaction } = useGetTransaction({
    id: id as string,
    select: ({ isLoading, isUninitialized, data }: TransactionUtils) => ({
      loading: isLoading || isUninitialized,
      transaction: data?.transaction,
    }),
  });
  const [open, setOpen] = useState(false);

  return (
    <Skeleton active={loading}>
      <DashboardContent>
        <DashboardGrid
          className="my-5"
          gap="gap-3 md:gap-5"
          columns="grid-cols-1"
        >
          <Card variant="outlined" className="w-full bg-white rounded-lg">
            <CardContent className="bg-white space-y-5" gutterTop>
              <div className="p-5 flex flex-col-reverse lg:flex-row items-center gap-5 justify-between w-full">
                <div className="flex gap-5 justify-between items-center">
                  <Avatar
                    size="lg"
                    radius="full"
                    src={'/images/profile.png'}
                    alt={'name'}
                  />
                  <div>
                    <SimpleText
                      component="h1"
                      className=" font-semibold text-sm: lg:text-lg flex gap-2 cursor-pointer"
                    >
                      {transaction?.name}
                    </SimpleText>
                    <SimpleText
                      component="h1"
                      className=" text-gray-300 text-sm flex gap-2 cursor-pointer"
                    >
                      #{transaction?.accountNumber}
                    </SimpleText>
                  </div>
                </div>

                <div className="flex flex-col lg:flex-row gap-5 w-full lg:w-auto">
                  <ScheduleBillModal />
                </div>
              </div>

              <div className="border p-5 space-y-5 rounded-md">
                <div className="flex items-center justify-between">
                  <SimpleText component="h1" className=" text-sm lg:text-lg">
                    <Translate msgId="dashboard.refundDetails" />
                  </SimpleText>

                  <ActionsDropdown onToggle={()=> setOpen(!open)} open={open} label='dashboard.action'>
                    <Button
                      variant="light"
                      onClick={()=> setOpen(!open)}
                      className=" !border-none flex items-center gap-1 w-full !px-5 !py-2 "
                    >
                      <Pencil />
                      <Translate msgId="dashboard.editBill" />
                    </Button>
                    <Button
                      variant="light"
                      onClick={()=> setOpen(!open)}
                      className=" !border-none flex items-center gap-1 w-full !px-5 !py-2 "
                    >
                      <FolderDownload />
                      <Translate msgId="dashboard.downloadReceipt" />
                    </Button>
                  </ActionsDropdown>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-5 lg:gap-x-10">
                  <div className="h-full space-y-2">
                    <div className=" w-full ">
                      <SimpleText className=" text-gray-300 ">
                        <Translate msgId="dashboard.vendorName" />
                      </SimpleText>
                      <SimpleText className=" text-gray-900 ">
                        {transaction?.name}
                      </SimpleText>
                    </div>
                    <div className=" w-full ">
                      <SimpleText className=" text-gray-300 ">
                        <Translate msgId="dashboard.totalAmount" />
                      </SimpleText>
                      <SimpleText className=" text-gray-900 ">
                        {formatCurrencyUtil(transaction?.amount)}
                      </SimpleText>
                    </div>
                    <div className=" w-full ">
                      <SimpleText className=" text-gray-300 ">
                        <Translate msgId="dashboard.paymentMethod" />
                      </SimpleText>
                      <SimpleText className=" text-gray-900 ">
                        {transaction?.paymentMethd}
                      </SimpleText>
                    </div>
                    <div className="flex gap-5">
                      <div className=" w-full ">
                        <SimpleText className=" text-gray-300 ">
                          <Translate msgId="dashboard.status" />
                        </SimpleText>
                        <SimpleText className=" text-gray-900 ">
                          {transaction?.status}
                        </SimpleText>
                      </div>
                    </div>
                    <div className="flex gap-5">
                      <div className=" w-full ">
                        <SimpleText className=" text-gray-300 ">
                          <Translate msgId="dashboard.dueDate" />
                        </SimpleText>
                        <SimpleText className=" text-gray-900 ">
                          {transaction?.dueDate}
                        </SimpleText>
                      </div>
                    </div>
                    <div className="flex gap-5">
                      <div className=" w-full ">
                        <SimpleText className=" text-gray-300 ">
                          <Translate msgId="dashboard.billServiceCharge" />
                        </SimpleText>
                        <SimpleText className=" text-gray-900 ">
                          {formatCurrencyUtil(transaction?.serviceCharge)}
                        </SimpleText>
                      </div>
                    </div>
                    <div className=" w-full ">
                      <SimpleText className=" text-gray-300 ">
                        <Translate msgId="dashboard.note" />
                      </SimpleText>
                      <SimpleText className=" text-gray-900 ">
                        {transaction?.note}
                      </SimpleText>
                    </div>
                  </div>
                  <div className="order-first mb-5 lg:mb-0 lg:order-last bg-gray-100 p-10 rounded-lg"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </DashboardGrid>
      </DashboardContent>
    </Skeleton>
  );
};

export default TransactionDetailsPage;
