const NumberFormat = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'GHS' });

const formatCurrencyUtil = (value: number) => {
    return NumberFormat.format(value);
};

export default formatCurrencyUtil;


export function formatCurrency(amount: number, currencyCode: string = "GHS") {
    const formattedNumber = new Intl.NumberFormat('en-US', { style: 'decimal' }).format(amount);
    return `${formattedNumber} ${currencyCode}`;
  }
  