import { Button } from '@/components/button'
import { Modal } from '@/components/modal'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'
import {  ChevronRight } from 'heroicons-react'
import  { useState } from 'react'

const HowToPayModal = () => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div>
      <div>
        <Button variant="darkPrimary" className="text-sm rounded-lg" onClick={()=>setIsOpen(true)}>
          <Translate msgId="dashboard.getPaid" />
        </Button>
      </div>
      <Modal className=' w-[430px]' isOpen={isOpen} onClose={()=>setIsOpen(false)}>
        <div className="p-2 space-y-5">
          <div>
            <SimpleText component='h1' className="text-lg leading-6 font-medium text-gray-900">
              <Translate msgId='dashboard.addCustomer' />
            </SimpleText>
            <SimpleText component='h1' className="mt-1 text-sm text-gray-500">
              <Translate msgId='dashboard.selectCustomerType' />
            </SimpleText>
          </div>

          <div className="border py-2 px-3 flex items-center justify-between rounded-md">
            <div>
              <SimpleText component="h1" className=" text-base">
                <Translate msgId='dashboard.vendor' />
              </SimpleText>
              <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
                <Translate msgId='dashboard.sendMoneyTo' />
              </SimpleText>
            </div>
            <ChevronRight className=' h-8 w-8' />
          </div>

          <div className="border py-2 px-3 flex items-center justify-between rounded-md">
            <div>
              <SimpleText component="h1" className=" text-base">
                <Translate msgId='dashboard.client' />
              </SimpleText>
              <SimpleText component='h1' className="mt-1 text-sm text-gray-300">
                <Translate msgId='dashboard.sendMoneyTo' />
              </SimpleText>
            </div>
            <ChevronRight className=' h-8 w-8' />
          </div>

          <Button variant='darkPrimary' className='w-full rounded-md'>
            <Translate msgId='dashboard.continue' />
          </Button>
        </div>
      </Modal>
    </div>
  )
}

export default HowToPayModal