import { Translate } from '@/components/translate';
import CustomerOverview from '@/features/Customers/CustomerOverview';
import CustomerDetails from '@/features/Customers/components/CustomerDetails';
import CustomerDetailsLoading from '@/features/Customers/components/CustomerDetailsLoading';
import CustomerList from '@/features/Customers/components/CustomerList';
import { getCustomer } from '@/features/Customers/cusomterSlice';
import { DashboardContent, DashboardGrid } from '@/layouts/dashboard';
import DashboardHeading from '@/layouts/dashboard/DashboardHeading';
import { useSelector } from 'react-redux';

const CustomersPage = () => {
  const selectedCustomer = useSelector(getCustomer);

  return (
    <DashboardContent>
      <DashboardHeading>
        <Translate msgId="dashboard.customers" />
      </DashboardHeading>
      <DashboardGrid className="mt-10" columns="grid-cols-1">
        <DashboardGrid item span="col-span-1">
          <CustomerOverview />
        </DashboardGrid>
      </DashboardGrid>
      <DashboardGrid
        gap="gap-3 md:gap-5"
        className="bg-white mt-10 rounded"
        columns="grid-cols-1 lg:grid-cols-2"
      >
        <DashboardGrid
          className="p-5 border-none"
          item
          span="col-span-2 md:col-span-1"
        >
          <CustomerList />
        </DashboardGrid>
        <DashboardGrid
          className="p-5 border-none hidden lg:block"
          item
          span="col-span-2 md:col-span-1"
        >
          {selectedCustomer ? (
            <CustomerDetails />
          ) : (
            <CustomerDetailsLoading loading={true} />
          )}
        </DashboardGrid>
      </DashboardGrid>
    </DashboardContent>
  );
};

export default CustomersPage;
