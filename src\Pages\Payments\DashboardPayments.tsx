import { DashboardContent, DashboardGrid } from '@/layouts/dashboard';
import PaymentOptions from '../../features/Payments/components/PaymentOptions';
import { useSelector } from 'react-redux';
import { getOption } from '@/features/Payments/paymentsSlice';
import clsx from '@/features/utils/clsx';
import SelectedPaymentDetails from '@/features/Payments/components/SelectedPaymentDetails';

const DashboardPayments = () => {
  const selectedOption = useSelector(getOption);

  return (
    <DashboardContent>
      <DashboardGrid
        gap="gap-3 md:gap-4"
        columns={clsx('rounded', {
          'grid-cols-3': !selectedOption,
          'grid-cols-2': selectedOption,
        })}
      >
        <DashboardGrid
          item
          span={clsx('col-span-3', {
            'md:col-span-2': !selectedOption,
            'md:col-span-1': selectedOption,
          })}
        >
          <PaymentOptions />
        </DashboardGrid>
        {selectedOption && (
          <DashboardGrid item span="col-span-3 md:col-span-1">
            <SelectedPaymentDetails />
          </DashboardGrid>
        )}
      </DashboardGrid>
    </DashboardContent>
  );
};

export default DashboardPayments;
