import { Button } from '@/components/button'
import { Translate } from '@/components/translate'
import { SimpleText } from '@/components/typography'
import { Trans } from 'react-i18next'

const Hero = () => {
  return (
    <section>
      <div className=" py-10 grid grid-cols-1 items-center lg:grid-cols-2 gap-5 md:gap-10 ">
        <div className='flex flex-col gap-5'>
          <div>
            <SimpleText
              component="h1"
              className="md:text-6xl text-5xl font-extrabold leading-tight"
            >
              <Trans
                i18nKey="landing.pricing"
                components={{
                  style1: (
                    <span className="text-raven-green-800 leading-tight" />
                  ),
                  style2: (
                    <span className="text-raven-green-800 leading-tight" />
                  ),
                }}
              />
            </SimpleText>
          </div>
          <div className="mt-6">
            <SimpleText component="h2" className="text-lg mt-5 font-light lg:w-4/6">
              <Translate msgId="landing.pricingDes" />
            </SimpleText>
          </div>
          <div className="grid gap-10 text-2xl text-center grid-cols-1 lg:grid-cols-4 md:grid-cols-2">
            <SimpleText component="h2" className="text-lg mt-5 font-light">
              <Translate msgId="landing.pricingTxt1" />
            </SimpleText>
            <SimpleText component="h2" className="text-lg mt-5 font-light">
              <Translate msgId="landing.pricingTxt2" />
            </SimpleText>
            <SimpleText component="h2" className="text-lg mt-5 font-light">
              <Translate msgId="landing.pricingTxt3" />
            </SimpleText>
            <SimpleText component="h2" className="text-lg mt-5 font-light">
              <Translate msgId="landing.pricingTxt4" />
            </SimpleText>
          </div>
          <div>
            <div className="mt-6 flex-col md:flex-row gap-3 flex md:items-center">
              <Button
                variant="darkPrimary"
                className="mr-5 rounded"
                type="button"
              >
                <Translate msgId="landing.getStarted" />
              </Button>
              <Button
                variant="light"
                className="mr-5 !bg-raven-dark-900 !text-white border-2 !border-raven-green-800 rounded "
                type="button"
              >
                <Translate msgId="landing.contactSales" />
              </Button>
            </div>
          </div>
        </div>
        <div className="  flex items-center justify-start ">
          <img src="/images/pricing-hero.png" className="px-0 mx-auto lg:mx-0 md:max-w-lg shrink-0" />
        </div>
      </div>
    </section>
  )
}

export default Hero

