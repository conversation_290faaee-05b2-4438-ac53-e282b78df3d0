import React from 'react';
import { Logo } from '@/components/logo';
import { SimpleText } from '@/components/typography';
import { Translate } from '@/components/translate';
import { Button } from '@/components/button';
export default function Header() {
  return (
    <div className="flex justify-between items-center w-full p-3  sticky top-0 border-b-[1px] border-gray-800 dark:bg-gray-900 bg-white">
      <div className="flex justify-between items-center gap-x-2">
        <Logo />
        <span>|</span>
        <SimpleText>
          <Translate msgId="docs.docs" />
        </SimpleText>
      </div>
      <div className="flex gap-x-3">
        <Button
          variant="transparent"
          className="border border-gray-600 rounded-lg "
        >
          <Translate msgId="auth.login" />
        </Button>
        <Button variant="primary" className="rounded-lg">
          <Translate msgId="landing.getStarted" />
        </Button>
      </div>
    </div>
  );
}
