import { FC } from 'react';
import { But<PERSON> } from 'components/button';
import apple from '/images/download-on-appstore.svg';

interface AppleButtonProps {
  className?: string;
  href: string;
}
const AppleButton: FC<AppleButtonProps> = ({ className, href }) => {
  return (
    <div>
      <Button
        variant="transparent"
        component="a"
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        role="button"
      >
        <img
          src={apple}
          alt="apple logo"
          className={className}
          title="Click to download on AppStore"
        />
      </Button>
    </div>
  );
};

export default AppleButton;
