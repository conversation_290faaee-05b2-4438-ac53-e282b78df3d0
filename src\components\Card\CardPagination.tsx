import { FC, useMemo } from 'react';
import { Button } from 'components/button';
import clsx from '@/features/utils/clsx';

interface CardPaginationProps {
  className?: string;
  total: number;
  page: number;
  limit: number;
  // eslint-disable-next-line no-unused-vars
  setPage: (page: number) => unknown;
}

const CardPagination: FC<CardPaginationProps> = ({
  className,
  total,
  page,
  limit,
  setPage,
}) => {
  const rootClass = clsx(
    'bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6',
    className
  );

  const { start, end, max } = useMemo(
    () => ({
      start: total === 0 ? 0 : (page - 1) * limit + 1,
      end: limit * page < total ? limit * page : total,
      max: Math.ceil(total / limit),
    }),
    [page, limit, total]
  );

  return (
    <nav className={rootClass} aria-label="Pagination">
      <div className="hidden sm:block">
        <p className="text-sm text-gray-700">
          Showing <span className="font-medium">{start}</span> to{' '}
          <span className="font-medium">{end}</span> of{' '}
          <span className="font-medium">{total}</span> results
        </p>
      </div>
      <div className="flex-1 flex justify-end sm:justify-end gap-3">
        <Button
          onClick={() => setPage(page - 1)}
          disabled={total === 0 || page === 1}
          variant="light"
          className="btn-tiny"
        >
          Previous
        </Button>
        <Button
          data-testid="paginate-next"
          onClick={() => setPage(page + 1)}
          disabled={total === 0 || page === max}
          className="btn-tiny"
          variant="light"
        >
          Next
        </Button>
      </div>
    </nav>
  );
};

export default CardPagination;
