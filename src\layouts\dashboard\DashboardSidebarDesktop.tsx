import { FC, ReactNode } from 'react';
import DashboardNavigation from './DashboardNavigation';
import { QuestionMarkCircleOutline, ArchiveOutline } from 'heroicons-react';
import { Translate } from '@/components/translate';
import { Logo } from 'components/logo';

interface DashboardSidebarDesktopProps {
  children: ReactNode;
}

const DashboardSidebarDesktop: FC<DashboardSidebarDesktopProps> = ({
  children,
}) => (
  <div className="hidden md:flex md:shrink-0 h-screen">
    <div className="flex flex-col w-64">
      <div className="flex flex-col flex-grow pb-3 bg-white overflow-y-auto">
        <div className="flex items-center shrink-0 px-6 h-20 mb-6">
          <Logo className="w-24" />
        </div>
        <div className="flex-grow flex flex-col">
          {children}
          <DashboardNavigation className="flex-initial justify-self-end">
            <DashboardNavigation
              item
              href="/p/help"
              icon={QuestionMarkCircleOutline}
            >
              <Translate msgId="dashboard.help" />
            </DashboardNavigation>
            <DashboardNavigation item href="/p/privacy" icon={ArchiveOutline}>
              <Translate msgId="dashboard.privacy" />
            </DashboardNavigation>
          </DashboardNavigation>
        </div>
      </div>
    </div>
  </div>
);

export default DashboardSidebarDesktop;
