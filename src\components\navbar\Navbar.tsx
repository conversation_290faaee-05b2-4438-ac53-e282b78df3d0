import { useState } from 'react';
import Translate from '../translate/Translate';
import Button from 'components/button/Button';
import { Link } from 'react-router-dom';
import { Logo } from '../logo';
import BusinessLogo from '../logo/BusinessLogo';
import { useLocation } from 'react-router-dom';
// import support from '/icons/support_bryan.svg';
import { ChevronDown, GlobeAlt } from 'heroicons-react';
import SupportIcon from './supportIcon';
import TooltipMenu from '../tooltipMenu/tooltipMenu';
import AppDownloadQrCode from './qrCode';
import { SimpleText } from '../typography';
const Navbar = () => {
  const { pathname } = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const LinksForTooltips = {
    personal: [
      {
        label: 'ravenapp',
        href: '/'
      },
      {
        label: 'cards',
        href: '/card'
      }
    ],
    business: [
      {
        label: 'smes',
        href: '/business'
      },
      {
        label: 'enterprise',
        href: '/enterprise'
      },
      {
        label: 'solutions',
        href: '/payments'
      }
    ],
    company: [
      {
        label: 'pricing',
        href: '/pricing'
      },
      {
        label: 'resources',
        href: '/resources'
      },
      {
        label: 'careers',
        href: '/careers'
      },
      {
        label: 'aboutUs',
        href: '/about-us'
      },
      {
        label: 'docs',
        href: '/documentation'
      }
    ]
  };
  const showIconAndDownloadAppButton =
    pathname.endsWith('/') || pathname.startsWith('/card');
  //the button that handles whether to display the download app button or navigate to login page
  const isADocumentationPage = pathname.startsWith('/doc');
  return (
    // logo
    <nav
      className={`hidden lg:flex w-100 h-auto ${
        pathname.startsWith('/pricing') ? 'bg-[#191919] text-white' : 'bg-white'
      } items-center py-4 justify-between relative`}
    >
      <div className="w-28">
        {pathname.startsWith('/business') ? (
          <BusinessLogo className="w-100" />
        ) : (
          <Logo className="w-100" />
        )}
      </div>
      {/* logo ends ;right section starts */}
      <div className="relative flex items-center md:order-2">
        <div className="lg:flex items-center justify-between hidden">
          {!pathname.startsWith('/careers') ? (
            <>
              <div>
                {showIconAndDownloadAppButton && (
                  <div className="flex mr-3 border-grey border-2 p-2 rounded-lg">
                    <GlobeAlt />
                    <ChevronDown />
                  </div>
                )}
                {!showIconAndDownloadAppButton && !isADocumentationPage && (
                  <Button
                    variant="transparent"
                    type="button"
                    component={Link}
                    to="/signup"
                  >
                    <SimpleText className="rounded-lg   border-2 p-2 -mr-3">
                      <Translate msgId="business.getStarted" />
                    </SimpleText>
                  </Button>
                )}
              </div>
              <section className="relative group">
                <Button
                  variant="darkPrimary"
                  className="rounded-lg"
                  type="button"
                  component={Link}
                  to={showIconAndDownloadAppButton ? '' : '/signin'}
                >
                  <Translate
                    msgId={`${
                      showIconAndDownloadAppButton
                        ? 'home.download'
                        : 'auth.login'
                    }`}
                  />
                </Button>
                {showIconAndDownloadAppButton ? <AppDownloadQrCode /> : null}
              </section>
            </>
          ) : (
            <Button variant="primary" className="rounded-lg" type="button">
              <Translate msgId="business.getStarted" />
            </Button>
          )}
        </div>

        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          type="button"
          className="inline-flex items-center p-2 w-10 h-10 justify-center text-sm lg:hidden"
          aria-controls="navbar-dropdown"
          aria-expanded="false"
        >
          <span className="sr-only">Open main menu</span>
          <svg
            className="w-5 h-5"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 17 14"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M1 1h15M1 7h15M1 13h15"
            />
          </svg>
        </button>
      </div>
      {/* right section ends;links list */}
      <div
        className={`hidden absolute left-0 top-full mt-2 w-full md:relative md:top-0 md:mt-0  lg:flex  md:w-auto md:order-1 px-2`}
      >
        <ul className="flex flex-col  md:flex-row md:space-x-8 space-y-6 md:space-y-0 rounded border shadow-lg lg:w-1/2 md:w-auto md:border-none md:shadow-none justify-self-end  px-6 py-6 md:py-0 w-full">
          <li className="w-28 group">
            <Button
              variant="transparent"
              component={Link}
              to="/"
              className={`hover:bg-light w-100 ${
                pathname.endsWith('/') && 'text-raven-green-500'
              }`}
            >
              <Translate msgId="home.personal" />
            </Button>
            <TooltipMenu data={LinksForTooltips.personal} />
          </li>
          {/* card route removed */}

          <li className="w-28 group">
            <Button
              variant="transparent"
              component={Link}
              to="/business"
              className={`hover:bg-light w-100 ${
                pathname.startsWith('/business') && 'text-raven-green-500'
              }`}
            >
              <Translate msgId="home.business" />
            </Button>
            <TooltipMenu data={LinksForTooltips.business} />
          </li>
          <li className="w-28 group">
            <Button
              variant="transparent"
              component={Link}
              to="/company"
              className={`hover:bg-light w-100 ${
                pathname.startsWith('/company') && 'text-raven-green-500'
              }`}
            >
              <Translate msgId="home.company" />
            </Button>
            <TooltipMenu data={LinksForTooltips.company} />
          </li>
          {/* support route added */}
          {!pathname.startsWith('/careers') && (
            <li className="w-28 flex items-center justify-center relative -right-[2.6em]">
              <SupportIcon active={pathname.startsWith('/support')} />
              <Button
                variant="transparent"
                component={Link}
                to="/support"
                className={`hover:bg-light w-100   ${
                  pathname.startsWith('/support') && 'text-raven-green-500'
                }`}
              >
                <Translate msgId="home.support" /> {/*adjust accordingly */}
              </Button>
            </li>
          )}
        </ul>
      </div>
    </nav>
  );
};

export default Navbar;
