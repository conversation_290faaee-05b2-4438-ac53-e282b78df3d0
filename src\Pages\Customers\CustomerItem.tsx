import { Avatar } from '@/components/avatar';
import { Button } from '@/components/button';
import { Modal } from '@/components/modal';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import CustomerDetails from '@/features/Customers/components/CustomerDetails';
import { setCustomer } from '@/features/Customers/cusomterSlice';
import { Client, Vendor } from '@/models/customer-types';
import { FC, useState } from 'react';
import { useDispatch } from 'react-redux';

interface CustomerItemProps {
  customer: Vendor | Client;
}

const CustomerItem: FC<CustomerItemProps> = ({ customer }) => {
  const [showCustomer, setShowCustomer] = useState(false)

  const dispatch = useDispatch();
  
  const onOpen = ()=> {
    setShowCustomer(true)
    dispatch(setCustomer(customer))
  }

  return (
    <div>
      <Button
        variant='transparent'
        onClick={onOpen}
        className="px-3 py-2 md:py-5 raven-outline rounded-lg w-full"
      >
        <div className="flex items-center gap-5">
          <Avatar radius="full" size="lg" src={customer.avatar} />
          <SimpleText truncate className="text-sm md:text-lg font-semibold">
            {customer.company_name}
          </SimpleText>
        </div>
      </Button>
      
      <div className="sm:block lg:hidden">
        <Modal isOpen={showCustomer} onClose={()=>setShowCustomer(false)} title={<Translate msgId="dashboard.customerDetails" />}>
          <CustomerDetails />
        </Modal>
      </div>
    </div>
  );
};

export default CustomerItem;
