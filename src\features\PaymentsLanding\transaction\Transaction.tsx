import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import EmpowerImage from '/images/raven-globe.png';
import Ghana from '/flags/ghana.png';
import SouthAfrica from '/flags/ghana.png';
import SierraLeon from '/flags/sierra-leon.png';
import Tanzania from '/flags/tanzania.png';
import Nigeria from '/flags/nigeria.png';
import Togo from '/flags/togo.png';
import Congo from '/flags/congo.png';
import Ethiopia from '/flags/ethiopia.png';

const Transaction = () => {
  return (
    <section className="mt-10 md:py-10 md:px-10">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
        <div className="space-y-3 order-last lg:order-first w-full dark:text-white text-gray-800 flex-col flex items-center justify-center md:pr-16 mb-10 md:mb-0">
          <div className=' space-y-5 md:space-y-10 px-10 py-5'>
            <SimpleText className='text-raven-green-800 text-2xl font-bold'>
              <Translate msgId="landing.transaction" />
            </SimpleText>

            <div className="">
              <SimpleText>
                <Translate msgId="landing.transactionDes1" />
              </SimpleText>
              <SimpleText>
                <Translate msgId="landing.transactionDes2" />
              </SimpleText>
            </div>

            <SimpleText>
              <Translate msgId="landing.transactionCountries" />
            </SimpleText>
          </div>
          <div className="grid grid-cols-4 gap-2">
            <img src={Ghana} className='shrink-0 object-contain' />
            <img src={SouthAfrica} className='shrink-0 object-contain' />
            <img src={SierraLeon} className='shrink-0 object-contain' />
            <img src={Tanzania} className='shrink-0 object-contain' />
            <img src={Nigeria} className='shrink-0 object-contain' />
            <img src={Togo} className='shrink-0 object-contain' />
            <img src={Congo} className='shrink-0 object-contain' />
            <img src={Ethiopia} className='shrink-0 object-contain' />
          </div>
        </div>
        <div className=" w-full p-5 md:p-12 rounded-3xl">
          <img
            src={EmpowerImage}
            alt="Phone showing raven app"
            className="shrink-0 object-contain"
          />
        </div>
      </div>
    </section>
  );
};

export default Transaction;
