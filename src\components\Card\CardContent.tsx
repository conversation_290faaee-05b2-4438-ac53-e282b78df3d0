import clsx from '@/features/utils/clsx';
import { FC, ReactNode } from 'react';

interface CardContentProps {
  className?: string;
  children: ReactNode;
  gutterTop?: boolean;
  inset?: boolean;
  gutterBottom?: boolean;
}

const CardContent: FC<CardContentProps> = ({
  className,
  children,
  inset,
  gutterTop = true,
  gutterBottom = true,
}) => {
  const rootClass = clsx(
    'px-4 sm:px-6',
    {
      'bg-gray-50': inset,
      'py-5': gutterTop && gutterBottom,
      'pt-5': gutterTop && !gutterBottom,
      'pb-5': !gutterTop && gutterBottom,
    },
    className
  );

  return <div className={rootClass}>{children}</div>;
};

export default CardContent;
