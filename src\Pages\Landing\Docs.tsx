import Header from '@/features/Docs/components/header';
import DocSection from '@/features/Docs/docSection';
import LeftPanel from '@/features/Docs/leftPanel';
const sectionIds = [
  'Home',
  'Core_API',
  'Payments',
  'Transaction_FX',
  'Payouts',
  'Global_Treasury',
  'Payments_for_Platforms',
  'Lazer',
  'Developer_Tools',
  'Trust_Centre'
];
export default function Docs() {
  return (
    <div>
      <Header />
      <div className="w-full">
        <LeftPanel />
        <div className="ml-96 dark:bg-gray-900 bg-[#f8f8f8]">
          {sectionIds.map((id) => (
            <DocSection key={id} id={id} />
          ))}
        </div>
      </div>
    </div>
  );
}
