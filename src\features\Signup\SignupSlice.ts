import api, { cancelToken } from "@/services/api/api"
import { RootState } from "@/store/store"
import { createAsyncThunk, createSelector, createSlice } from "@reduxjs/toolkit"
import getErrors from "../utils/get-errors"
import { SignupFormData } from "@/models/signup-form-data"
import { ResponseError } from "@/models/response-error.interface"

/**
 * Async thunk to register a new user.
 * This function makes an API call to register a new user with the provided data.
 * The function is cancellable and will abort the request if the signal indicates an abort.
 * It also provides feedback on the upload progress of the form data.
 *
 * @function
 * @async
 * @param {Object} options - The thunk options object.
 * @param {Object} options.data - The user registration data.
 * @param {Array} options.data.files - The array of files to be uploaded.
 * @param {Object} thunkApi - The thunk API object.
 * @param {AbortSignal} thunkApi.signal - The abort signal to abort the async request if needed.
 * @param {Function} thunkApi.rejectWithValue - A utility to create an error action if the async request fails.
 * @param {Function} thunkApi.dispatch - The Redux `dispatch` function.
 * @returns {Promise} Returns a promise that either resolves with the server response data or rejects with an error.

 */
export const registerUser = createAsyncThunk('registerUser', async (options: { data: SignupFormData }, { signal, rejectWithValue, getState }) => {

    const source = cancelToken.source();

    signal.addEventListener('abort', () => {
        source.cancel();
    });

    const state = getState()
    const { country } = (state as RootState).register

    // set the data to the session storage
    sessionStorage?.setItem('auth', JSON.stringify({ ...options.data, country }))

    try {
        // Make an API call to register a new user
        const response = await api.post('/register', options, {
            cancelToken: source.token,
        })

        return response.data
    } catch (error) {
        return rejectWithValue(getErrors(error))
    }
})

export const handleOTPConfirm = createAsyncThunk('handleOTPConfirm', async (options: { otp: string }, { rejectWithValue, signal }) => {
    const source = cancelToken.source();

    signal.addEventListener('abort', () => {
        source.cancel();
    });

    try {
        // Make an API call to register a new user
        const response = await api.post('/otp', options, {
            cancelToken: source.token,
        })

        return response.data
    } catch (error) {
        return rejectWithValue(getErrors(error))
    }
})

/**
 * Send the user's details to the server after the email has been confirmed
 */
export const sendUserDetails = createAsyncThunk('sendUserDetails', async (_, { rejectWithValue, signal }) => {
    const source = cancelToken.source();

    signal.addEventListener('abort', () => {
        source.cancel();
    });

    const userData = sessionStorage.getItem('auth')

    try {
        // Make an API call to register a new user
        const response = await api.post('/signup', userData, {
            cancelToken: source.token,
        })

        sessionStorage.removeItem('auth')

        return response.data
    } catch (error) {
        return rejectWithValue(getErrors(error))
    }
})

interface signupState {
    loading: boolean
    otp: number | null;
    user: null | object;
    error: ResponseError | null;
    redirect: string;
    country: string;
    otpSucess: boolean;
}

const initialState: signupState = {
    loading: false,
    user: null,
    error: null,
    redirect: '',
    country: 'GH',
    otp: null,
    otpSucess: false
}

// Registration slice
export const registerSlice = createSlice({
    initialState,
    name: 'register',
    reducers: {
        // Reducer to update upload progress
        reset: (state) => {
            state.loading = false;
            state.user = null;
            state.error = null;
            state.redirect = ''
            state.country = 'GH'
            state.otp = null
            state.otpSucess = false
        },
        setCountry: (state, action) => {
            state.country = action.payload
        }
    },
    // Extra reducers for handling async actions
    extraReducers: (builder) => {
        builder.addCase(registerUser.pending, (state) => {
            state.loading = true
            state.error = null
            state.redirect = ''
        })
            .addCase(registerUser.fulfilled, (state, action) => {
                state.error = null
                state.loading = false
                state.otp = action.payload.otp
                state.redirect = action.payload.redirect
                sessionStorage.setItem('otp', action.payload.otp)
                state.country = ''
            }).addCase(registerUser.rejected, (state, action) => {
                state.error = action.payload as ResponseError
                state.loading = false
                state.redirect = ''
            }).addCase(handleOTPConfirm.pending, (state) => {
                state.loading = true
                state.error = null
                state.redirect = ''
                state.otpSucess = false
            }).addCase(handleOTPConfirm.fulfilled, (state, action) => {
                state.error = null
                state.otpSucess = action.payload
            }).addCase(handleOTPConfirm.rejected, (state, action) => {
                state.error = action.payload as ResponseError
                state.loading = false
                state.otpSucess = false
            }).addCase(sendUserDetails.fulfilled, (state, action) => {
                console.log('paylosd', action.payload)
                state.user = action.payload.user
                state.loading = false
                state.otpSucess = false
            }).addCase(sendUserDetails.rejected, (state, action) => {
                state.error = action.payload as ResponseError
                state.loading = false
                state.otpSucess = false
            })
    }
});

export const { name, actions } = registerSlice;

export const { reset, setCountry } = actions


/**
 * Selector function to get the register slice from the Redux state.
 *
 * @param {RootState} state - The root Redux state.
 * @returns {InitialState} - The register slice from the Redux state.
 */

const getSlice = (state: RootState) => state[name]

// Selector: Get the loading state
export const getLoading = createSelector(getSlice, (slice) => slice?.loading)
// Selector: Get the error state
export const getError = createSelector(getSlice, (slice) => slice?.error)
// Selector: Get the Redirect state
export const getRedirect = createSelector(getSlice, (slice) => slice?.redirect)
// get selected country
export const getCountry = createSelector(getSlice, (slice) => slice?.country || 'GH')
// get OTP success state
export const getOTPSuccess = createSelector(getSlice, (slice) => slice?.otpSucess || false)