import { SimpleText } from 'components/typography';
import { Translate } from 'components/translate';
import FeatureItem from '../components/FeatureItem';
import ex1 from '/images/ex4.png';
import ex2 from '/images/ex5.png';
import ex3 from '/images/ex6.png';

const DiscoverMore = () => {
  return (
    <section className="py-10 p-5">
      <div className="lg:pt-28 flex flex-col items-center justify-center container mx-auto space-y-5 ">
        <SimpleText
          component="h1"
          className="text-gray-400 leading-8  text-center  font-extrabold "
        >
          <Translate msgId="card.ravenCards" />
        </SimpleText>
        <SimpleText className="lg:w-4/5 text-center font-extrabold text-raven-green-800 text-4xl lg:text-5xl ">
          <Translate msgId="card.discoverMore" />
        </SimpleText>
      </div>

      <div className="grid md:grid-cols-2 grid-cols-1 lg:grid-cols-3 gap-5 py-10">
        <FeatureItem
          title="card.sendAndReceive"
          icon={ex1}
          descriptoin="card.sendAndReceiveDesc"
        />
        <FeatureItem
          title="card.openAccount"
          icon={ex2}
          descriptoin="card.openAccountDesc"
        />
        <FeatureItem
          title="card.exchange"
          icon={ex3}
          descriptoin="card.exchangeDesc"
        />
      </div>
    </section>
  );
};

export default DiscoverMore;
