import { useState } from 'react';
import Table, {
  TableHead,
  TableBody,
  TableRow,
  TableColumn,
} from 'components/Table';
import { Card, CardPagination } from 'components/Card';
import useTransactionsHistory from './hooks/useTransactionsHistory';
import TransactionHistoryItem from './HistoryItem';

interface History {
  id: string;
  date: string;
  type: string;
  status: string;
  amount: number;
}

interface TransactionsUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: {
    transaction_history: History;
    total: number;
  };
}

const TransactionsTable = () => {
  const limit = 10;
  const [page, setPage] = useState(1);
  const { loading, history, total } = useTransactionsHistory({
    page,
    limit,
    select: ({ isLoading, isUninitialized, data }: TransactionsUtils) => ({
      loading: isLoading || isUninitialized,
      history: data?.transaction_history ?? [],
      total: data?.total ?? 0,
    }),
  });

  return (
    <Card variant="outlined">
      <Table
        disableStyle
        disableBorderBottom
        className="lg:rounded-md lg:overflow-hidden"
      >
        <TableHead>
          <TableRow>
            <TableColumn heading>Payment Date</TableColumn>
            <TableColumn heading>Type</TableColumn>
            <TableColumn heading>Status</TableColumn>
            <TableColumn heading>Amount</TableColumn>
          </TableRow>
        </TableHead>
        {loading ? (
          <TableBody>
            <TransactionHistoryItem loading />
            <TransactionHistoryItem loading />
            <TransactionHistoryItem loading />
            <TransactionHistoryItem loading />
            <TransactionHistoryItem loading />
            <TransactionHistoryItem loading />
            <TransactionHistoryItem loading />
            <TransactionHistoryItem loading />
          </TableBody>
        ) : (
          <TableBody>
            {history.map((result: History) => (
              <TransactionHistoryItem key={result.id} {...result} />
            ))}
          </TableBody>
        )}
      </Table>
      <CardPagination
        total={total}
        limit={limit}
        page={page}
        setPage={setPage}
      />
    </Card>
  );
};

export default TransactionsTable;
