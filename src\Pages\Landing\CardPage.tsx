import { Header } from '@/components/header';
import FAQs from '@/features/Card/components/FAQs';
import Corperate from '@/features/Card/corperate/Corperate';
import DiscoverMore from '@/features/Card/discover-more/DiscoverMore';
import ExclusiveVirtualCard from '@/features/Card/exclusive-virtual-card/ExclusiveVirtualCard';
import Ezwitch from '@/features/Card/ezwitch/Ezwitch';
import Hero from '@/features/Card/hero/Hero';
import MomoToCard from '@/features/Card/momo-to-card/MomoToCard';
import ProtectOnlinePayment from '@/features/Card/protect-online-payment/ProtectOnlinePayment';
import RequestCard from '@/features/Card/request-card/RequestCard';
import Footer from '@/features/footer/Footer';

const CardPage = () => {
  return (
    <>
      <Header />
      <main>
        <Hero />
        <div className="body">
          <MomoToCard />
        </div>
        <div className="body">
          <Ezwitch />
        </div>
        <div className="body">
          <ExclusiveVirtualCard />
        </div>
        <div className="body">
          <RequestCard />
        </div>
        <div className="body">
          <DiscoverMore />
        </div>
        <div className="body">
          <ProtectOnlinePayment />
        </div>
        <div className="body">
          <FAQs />
        </div>
        <div className="body">
          <Corperate />
        </div>
        <Footer />
      </main>
    </>
  );
};

export default CardPage;
