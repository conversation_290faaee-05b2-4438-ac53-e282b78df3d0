import { useState, FC, ReactNode } from 'react';
import { Trans } from 'react-i18next';
import { SimpleText } from '../typography';
/**
 * Props for the AccordionItem component.
 * @property {string} title - The title of the accordion item, used as a key for translations.
 * @property {ReactNode | string} content - The content of the accordion item. Can be plain text from translation key or JSX.
 * @property {string} [className] - Optional additional CSS classes to style the accordion item.
 */
interface AccordionItemProps {
  title: string;
  content: ReactNode | string;
  className?: string;
}

/**
 * AccordionItem Component
 *
 * A reusable accordion item component that toggles between collapsed and expanded states.
 * It supports localization for both the title and content using the `Trans` component from `react-i18next`.
 *
 * @param {AccordionItemProps} props - Props to customize the accordion item.
 * @returns {JSX.Element} The rendered accordion item component.
 */
const AccordionItem: FC<AccordionItemProps> = ({
  className,
  title,
  content
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={`space-y-0 mb-2 rounded-md ${className || ''}`}>
      {/* Accordion button */}
      <button
        className="w-full p-4 flex justify-between items-center gap-5"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-lg text-gray-500 text-left">
          <Trans i18nKey={title} />
        </span>

        <svg
          className={`w-6 h-6 transform transition-transform duration-300 ${
            isOpen ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {/* Accordion content */}
      <div
        className={`overflow-hidden text-gray-400 transition-all duration-500 ${
          isOpen ? 'max-h-auto' : 'max-h-0'
        }`}
      >
        <div className="p-4 pl-6">
          {/* Render translated content or JSX  */}
          {typeof content === 'string' ? (
            <SimpleText>
              <Trans i18nKey={content} />
            </SimpleText>
          ) : (
            content
          )}
        </div>
      </div>
    </div>
  );
};

export default AccordionItem;
