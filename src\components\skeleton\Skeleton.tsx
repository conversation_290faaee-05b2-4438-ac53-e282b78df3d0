import { FC, ReactNode } from 'react';

interface SkeletonProps {
  className?: string;
  children: ReactNode;
  active: boolean;
}

const Skeleton: FC<SkeletonProps> = ({ className, children, active }) => {
  if (!active) {
    return children;
  }

  return (
    <div
      data-testid="skeleton"
      className={`relative overflow-hidden rounded-md bg-gray-100 dark:bg-black pointer-events-none
        ${className}`}
    >
      <div className="opacity-0">{children}</div>
    </div>
  );
};

export default Skeleton;
