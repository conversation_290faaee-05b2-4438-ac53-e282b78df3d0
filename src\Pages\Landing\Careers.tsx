import { useState } from 'react';
import Footer from '@/features/footer/Footer';
import { Header } from '@/components/header';
import Overlay from '@/features/Careers/overlay';
import VisionCards from '@/features/Careers/visionCards';
import Hero from '@/features/Careers/hero';
import RoleCards from '@/features/Careers/roleCards';

type cardChild = 'form' | 'jobs' | null;
export default function Careers() {
  const [overlayChild, setOverlayChild] = useState<cardChild>(null);
  const [displayOverlay, setDisplayOverlay] = useState(false);
  const handleOverlayState = (child: cardChild) => {
    setOverlayChild(child);
    setDisplayOverlay(true);
  };

  const handleOnOverlayClose = () => {
    setDisplayOverlay(false);
  };
  return (
    <div>
      <Overlay
        displayOverlay={displayOverlay}
        child={overlayChild}
        onClose={handleOnOverlayClose}
      />
      <Header />
      <div className="body overflow-x-hidden">
        <Hero onJoinClick={() => handleOverlayState('jobs')} />
        <VisionCards />
        <RoleCards onCardClick={() => handleOverlayState('form')} />
      </div>
      <Footer />
    </div>
  );
}
