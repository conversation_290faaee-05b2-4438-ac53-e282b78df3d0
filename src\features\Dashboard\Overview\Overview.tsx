import useOverview from '@/hooks/useGetOverView';
import OverviewCard from './OverviewCard';
import formatCurrency from '@/features/utils/formatCurrency';
import wallet from '/icons/wallet.png';
import moneyHand from '/icons/get-revenue.png';
import ledger from '/icons/general-ledger.png';
import { FC } from 'react';

interface OverviewUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: {
    balance: number;
    expenses: number;
    income: number;
    savings: number;
  };
}

interface OverviewProps {
  showSavings?: boolean;
}

const Overview:FC<OverviewProps> = ({ showSavings }) => {
  const { loading, balance, expenses, income, savings } = useOverview({
    select: ({ isLoading, isUninitialized, data }: OverviewUtils) => ({
      loading: isLoading || isUninitialized,
      balance: data?.balance || 0,
      expenses: data?.expenses || 0,
      income: data?.income || 0,
      savings: data?.savings || 0,
    }),
  });

  return (
    <dl className={`grid grid-cols-1 gap-5 sm:grid-cols-2 ${showSavings ? 'lg:grid-cols-4' : 'lg:grid-cols-3'}`}>
      <OverviewCard
        loading={loading}
        name="dashboard.balance"
        stat={formatCurrency(balance)}
        icon={wallet}
        iconAlt="wallet"
      />
      <OverviewCard
        loading={loading}
        name="dashboard.income"
        stat={formatCurrency(income)}
        icon={moneyHand}
        iconAlt="revenue icon"
      />
      <OverviewCard
        loading={loading}
        name="dashboard.expenses"
        stat={formatCurrency(expenses)}
        icon={ledger}
        iconAlt="ledger"
        />
      {showSavings && (
        <OverviewCard
          loading={loading}
          name="dashboard.savings"
          stat={formatCurrency(savings)}
          icon={ledger}
          iconAlt="ledger" 
        />
      )}
    </dl>
  );
};

export default Overview;
