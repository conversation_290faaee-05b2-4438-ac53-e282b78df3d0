import { Avatar } from "@/components/avatar"
import { <PERSON><PERSON> } from "@/components/button"
import { Translate } from "@/components/translate"
import { SimpleText } from "@/components/typography"

const Security = () => {
  return (
    <div>
      <div className=" border-b-2">
        <SimpleText component="h1" className="text-xl">
          <Translate msgId="dashboard.loginDetails" />
        </SimpleText>
        <SimpleText className="text-gray-300">
          <Translate msgId="dashboard.reviewInfo" />
        </SimpleText>
      </div>

      <div>
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
          <SimpleText className="text-gray-300 text-lg lg:w-[30%]">
            <Translate msgId="dashboard.currentPassword" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full lg:w-[478px]"
            type="text"
            value=''
            placeholder={'example.12.Ab'}
          />
        </div>
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
          <SimpleText className="text-gray-300 text-lg lg:w-[30%]">
            <Translate msgId="dashboard.securityQuestions" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full lg:w-[478px]"
            type="text"
            value=''
            placeholder={'Your fathers name'}
          />
        </div>
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
          <SimpleText className="text-gray-300 text-lg lg:w-[30%]">
            <Translate msgId="dashboard.emailAddress" />
          </SimpleText>
          <input
            className="dark:bg-gray-700 dark:border-gray-600 text-gray-900 dark:placeholder-gray-400 dark:text-white focus:ring-gray-100 focus:border-gray-100 form-control form-input w-full lg:w-[478px]"
            type="text"
            value=''
            placeholder={'Enable'}
          />
        </div>
      </div>

      <div className=" mt-10">
        <div className="mb-3 form-group sm:block lg:flex items-center w-full py-5 border-b-2">
          <div className="  lg:w-[30%]">
            <SimpleText component="h1" className="font-semibold text-xl">
              <Translate msgId="dashboard.securityCredential" />
            </SimpleText>
            <SimpleText component="p" className="text-gray-300">
              <Translate msgId="dashboard.securityInfo" />
            </SimpleText>
          </div>
        </div>
        <div className=" w-full flex-col lg:flex-row gap-5 mb-3 flex justify-between items-center border-b-2 py-5">
          <div className=" self-start  flex gap-x-5">
            <Avatar size="xl" radius="full" src={'/images/profile.png'} alt="Profie Photo" />
            <div>
              <SimpleText component="h1" className="text-gray-300 cursor-pointer">
                27 March 2024 at 11:47pm
              </SimpleText>
              <SimpleText component="h1" className="cursor-pointer">
                Mac OS Safari 15.1
              </SimpleText>
            </div>
          </div>
          <Button variant="dark" className=" self-start rounded-lg !px-3 !py-2 !text-sm">
            <Translate msgId="dashboard.currentSession" />
          </Button>
        </div>
      </div>

      <div className="flex w-full flex-col lg:flex-row gap-5 items-center justify-between mt-5">
        <Button variant="darkPrimary" className=" w-full lg:w-[250px] rounded-lg">
          <Translate msgId="dashboard.updateSecurity" />
        </Button>
        <div className="group w-full lg:w-[200px]">
          <Button variant="light" className=" w-full transition-all rounded-lg border-raven-green-800 !text-raven-green-800 group-hover:bg-raven-green-800 group-hover:!text-white">
            <Translate msgId="dashboard.cancel" />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Security