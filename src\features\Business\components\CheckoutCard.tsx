import { Button } from '@/components/button';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import formatCurrencyUtil from '@/features/utils/formatCurrency';

const CheckoutCard = () => {
  return (
    <div className="">
      <div className="p-4 bg-white rounded-lg mb-5 text-sm">
        <div className="flex gap-5 items-center justify-between">
          <SimpleText component="h3" className=" ">
            <Translate msgId={'business.personalDetails'} />
          </SimpleText>

          <div className="h-1 bg-gray-500 w-6 rounded-lg" />
          <div className="h-4 w-4 p-2 bg-raven-green-800 rounded-full text-white flex items-center justify-center">
            <SimpleText component="h3" className="text-white text-xs p-2">
              2
            </SimpleText>
          </div>
          <SimpleText component="h3" className="">
            <Translate msgId={'business.payment'} />
          </SimpleText>
        </div>
      </div>

      <div className="p-4 bg-white rounded-lg mb-3 text-sm">
        <div className="flex gap-5 items-center justify-between mb-2">
          <div>
            <SimpleText component="h3" className="mb-2 ">
              Joseph Nartey
            </SimpleText>
            <SimpleText component="h3" className=" text-gray-400">
              <Translate msgId={'business.fullName'} />
            </SimpleText>
          </div>

          <div>
            <SimpleText component="h3" className=" mb-2">
              <Translate msgId={'<EMAIL>'} />
            </SimpleText>
            <SimpleText component="h3" className=" text-gray-400">
              <Translate msgId={'business.email'} />
            </SimpleText>
          </div>
        </div>
        <hr />
        <div className="flex gap-5 items-center justify-between mt-3">
          <SimpleText component="h3" className=" font-semibold">
            <Translate msgId={'business.total'} />
          </SimpleText>
          <SimpleText component="h3" className=" text-sm">
            {formatCurrencyUtil(3480)}
          </SimpleText>
        </div>
      </div>
      <div className="justify-center flex items-center mt-5">
        <Button variant="darkPrimary" className="rounded-lg !py-2">
            <Translate msgId={'business.checkOut'} />
        </Button>
      </div>
    </div>
  );
};

export default CheckoutCard;
