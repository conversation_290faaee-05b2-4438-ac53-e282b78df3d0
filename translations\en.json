{"general": {"noCountry": "No countries found", "back": "Back", "goBack": "Go Back", "logout": "Log out"}, "footer": {"socialMedia": "Follow us", "menu": "<PERSON><PERSON>", "copyright": "Copyright", "allRights": "All Rights Reserved"}, "home": {"personal": "Personal", "business": "Business", "company": "Company", "download": "Download app", "heroTitle": "Banking and <style1>technology</style1> solutions for the ", "heroTitleLast": "<style2>underbanked</style2>", "heroSubtitle": "Seamless, Swift, & Secure global money transfer", "getOnAppStores": "Download App on All Platforms", "featuredPartners": "Trusted by 250+ companies", "explore": "Explore", "earnHigh": "Get features to help you earn high interest", "exchange": "Exchange your money", "featureTitle1": "Physical and Virtual cards", "featureTitle2": "Cash Points & Withdrawals", "featureTitle3": "Budgeting Tools", "featureTitle4": "Wallets services", "featureTitle5": "Api integration", "featureDesc1": "Global transactions made easy with our International Virtual Cards, and Local & International Physical Cards", "featureDesc2": "Access your money whenever you need it with our widespread cash points, making cash withdrawals a breeze.", "featureDesc3": "Manage your finances smartly with our intuitive budgeting tools, helping you stay in control of your money.", "featureDesc4": "Safeguard your money with our secure wallet services, ensuring your funds are accessible whenever you need them", "featureDesc5": "Mobile Money & Bank Integration: Enjoy the flexibility of integrating your bank accounts and mobile money, making it easier to manage your funds", "exclusiveCards": "Exclusive Premium cards", "learnMore": "Learn More", "chooseYourPlan": "Choose your plan", "choosePlan": "Choose plan", "month": "Month", "free": "Free", "features": "Features", "standard": "Standard", "premium": "Premium", "gold": "Gold", "freeFeature1Title": "Everyday Benefits", "freeFeature1Desc": "Exclusive card & Cashback on card payments", "freeFeature2Title": "Cashback on Card payments", "freeFeature2Desc": "More cash back offers. Shop with confidence", "freeFeature3Title": "Fee-free ATM withdrawals", "freeFeature3Desc": "2000 / month limit", "premiumFeature1Title": "Free Features Plus", "premiumFeature1Desc": "You get all features in standard", "premiumFeature2Title": "Fee-free ATM withdrawals", "premiumFeature2Desc": "GHS 2000/month limit", "premiumFeature3Title": "Unlimited Fx", "premiumFeature3Desc": "No fee currency exchange, 10+ currencies", "goldFeature1Title": "Exclusive card", "goldFeature1Desc": "Personalized Gold card & Cashback on card payments", "goldFeature2Title": "Priority customer support", "goldFeature2Desc": "In-app chat", "goldFeature3Title": "Save on international transfer fees", "goldFeature3Desc": "Save money when you make international transfers to raven accounts", "terms": "Terms & Conditions", "legal": "Legal Agreements", "privacyPolicy": "Privacy Policy", "cookiePolicy": "<PERSON><PERSON>", "support": "Support", "getTheApp": "Get the app", "toSendGlobal": "to send money globally", "exclusiveCardsDescription": "Choose from a range of exclusive card designs and personalize your premium cars to make it uniquely yours (fees apply) Get it fast with free delivery.", "getCard": "Get a Card", "lifestyle": "Lifestyle", "dayToDay": "Make day-to-day spending a breeze with all things money in one place", "shopping": "Shopping", "earnMore": "Earn more! Save more! Shop more Go global with unlimited spending bonus try Raven now!", "startShopping": "Start Shopping", "youSend": "You Send", "compareRates": "compare rates", "exchangeRate": "Exchange Rate & Fees", "exchangeRateDescription": "Whether in USA,Ghana, Nigeria  Ethiopia and many more... The rates? Really cool.", "theyGet": "recipient gets", "currentRate": "Rate at", "getStarted": "Send Money", "uniqueFeatures": "Unique Features", "FeatureTitle": "Diverse Solutions for Diverse Needs", "unparalledAccess": "Unlock unparalleled access to global financial ecosystems with <PERSON>, your trusted partner in banking & payments", "sectionTitle5": "<style>Why</style> Choose <style>ravenapp?</style>", "sectionDesc5": "Crafting a New Standard in banking and Payments", "section5FeatTitle1": "Innovative Solutions", "section5FeatDesc1": "Cutting-edge technology meeting diverse financial needs", "section5FeatTitle2": "Unmatched Security", "section5FeatDesc2": "State-of-the-art security protocols protecting your transactions and data.", "section5FeatTitle3": "Universal Accessibility", "section5FeatDesc3": "Financial solutions designed to be accessible anywhere, anytime.", "section5FeatTitle4": "Customer-Centric Approach", "section5FeatDesc4": "Dedicated support and services focused on your satisfaction and success", "sectionTitle6": "Choose a convenient way to pay", "sectionDesc6": "With raven you get to pick from various methods of payment anytime any day", "ravenAccount": "<PERSON> Account", "cards": "Cards", "selectConvenient": "<style>Select </style>convenient payment <style>method</style>", "youGetToChoose": "With raven you get to choose your various methods of payment anytime any day", "plansToChoose": "Choose your plan", "ravenEasiest": "It's easier to send money into your recipient's raven account. All you need is his/her unique raven tag. It takes seconds to pay faster.", "startedInMinutues": "<style1>Get Started</style1> in minutes", "createAndStart": "Create an account and start using your raven app account in 4 easy steps via the app. (Download the app on play store or AppStore)", "signupWithMailPhone": "Signup with your email and phone number", "verfiyIdentity": "Verify your identity", "linkYourCard": "Link your card or load your account (you have options to open multiple accounts in different currencies including USD)", "sendMoney": "Send money and check your transfer status in real time", "createNow": "Create Account Now", "laserSecurity": "<style>Laser</style> focused <style>Security</style>", "onTheClock": "We're on the clock 24/7 monitoring and securing your money.", "securityTitle1": "Card Control", "securityDesc1": "Block transactions and merchants, and freeze it unfreeze your card Instantly", "securityTitle2": "Data Encryption", "securityDesc2": "All payments and identity verification is highly encrypted with our in-house security technology built on industry standards to keep signup secure and your account protected ", "securityTitle3": "Privacy Settings", "securityDesc3": "Keep your info safe with fingerprint & Face ID lock.", "securityTitle4": "Real time Notification", "securityDesc4": "instant payment notifications in real time to prevent fraud.", "bankPayment": "Bank Payment", "bankPaymentDesc": "Send money from anywhere to your recipient's local account. They will receive the funds in their local currency as well", "cardDesc": "Use raven card from your app or linked card to transfer money with minutes. Recipients receive funds in real time", "momoDesc": "Send money directly to friends,family, or businesses. All you ned is your recipient's mobile money number and network operator", "businessOnRaven": "Get your Business on Ravenhive", "businessOnRavenMsg": "Experience seamless Payment,Data and financial management in one platform. Discover how our financial accelerates your Business", "joinNow": "Join Now", "payWithRaven": "Pay with raven", "docs": "View Documentation", "getInTouch": "Get in touch", "qrGetApp": "Get the raven app", "scan": "Scan the QR code to download the app", "orClickDownload": "or Click to Download the app"}, "tips": {"ravenapp": "raven<PERSON>p", "cards": "Cards", "pricing": "Pricing", "resources": "Resources", "careers": "Careers", "aboutUs": "About Us", "docs": "Documentation", "smes": "SMEs", "enterprise": "Enterprise", "solutions": "Solutions"}, "business": {"whyChooseRavenBusiness": "Why Choose Raven Business", "featTitle1": "Multi-currency Accounts", "featTitle2": "Easy Onboarding", "featTitle3": "Support 24/7 instant payment to other raven account", "featTitle4": "Market leading FX Rate", "featTitle5": "Intuitive Dashboard", "featTitle6": "Security", "featDesc1": "To pay and get paid globally at low cost", "featDesc2": "In minutes, onboard your customers within your own, fully-braided environnment", "featDesc3": "If you pay to other raven accounts, the funds will arrive instantly.", "featDesc4": "Enjoy free fund transfer between raven accounts regardless of you and your supplier’s locations with competitive FX rates.", "featDesc5": "Manage every aspect of your business through our user-friendly dashboard - in the office or on the go. No tech knowledge required.", "featDesc6": "Your funds are protected with top security. + Talking of security ", "apiTitle1": "Wider market reach", "apiTitle2": "Initiate Payments", "apiTitle3": "Payment Verification", "apiTitle4": "Instant virtual card creation", "apiTitle5": "Customer verification", "apiTitle6": "Test Cards", "apiTitle": "Well documented and easy-to-use APIs for developers", "apiDesc": "With the raven business API you can integrate online payments by raven business directly into your website or app. We have integrations available for virtually diverse kind of used cases . Check the available <span>SDKs.</span>", "apiStartIntegration": "Start your integrations - Connect with our APIs", "subscribe": "Subscribe", "subsTitle": "Stay ahead of change", "subsDesc": "Subscribe to beyondmoney (our newsletter) to keep up with the latest insights.", "statsCurrencies": "<span>25+</span> Currencies", "statsCountries": "<span>28+</span> Countries", "statsApi": "Through One <span>API</span>", "onlineCheckout": "Online Checkout", "payout": "Payout", "realTimeAccounting": "Real-Time Accounting", "onlineCheckoutDesc": "Our online checkout boasts an intelligent payment ordering system, exceptional speed, and a sleek simple design. Crafted to enhance your cash flow and deliver an unique payment experience for your customers.", "payoutDesc": "Make single or bulk payments to raven user, local wallets and bank accounts from your Raven business dashboard.", "realTimeAccountingDesc": "Generate invoices for your customers and get paid from anywhere.", "getPaidFaster": "Get Paid Faster", "getPaidFasterDesc": "Accept and manage payments everywhere. Increase sales , cash flows and optimize your checkout experience with global and local payment methods for all your customers.", "getPaidFasterButton": "Start With Payments", "weAreHiring": "We're Hiring", "heroTitle": "Powering Your Business Globally", "heroDesc": "End to end payment , seamless checkouts, financial management tools in a single solution. We help you scale seamlessly and securely.", "getStarted": "Get Started", "talkToUs": "Talk To Us", "pay": "Pay", "checkOut": "Check Out", "yourPaymentLink": "Your Payment Link", "payInvoice": "Pay Invoice", "accountNumber": "Account Number", "bank": "Bank", "raven": "Raven", "total": "Total", "email": "Email", "fullName": "Full Name", "payment": "Payment", "personalDetails": "Personal Details"}, "support": {"gotQuestions": "Got questions? Find answers here.", "cards": "cards", "smes": "SMEs", "ravenapp": "raven<PERSON>p", "enterprise": "enterprise", "payment": "payment", "security": "security", "solutions": "solutions", "faq1T": "When do I receive my money?", "faq2T": "Who bears the transaction charge?", "faq3T": "How do I activate payment methods?", "faq1Content": "It depends on the region and payment methods used. Nevertheless 80% of transactions processed are realtime.", "faq2Content": "In ravenhive, the transaction charges are typically borne by the merchant using the platform, not the customer. These fees include gateway fees, payment method fees, and potentially foreign currency conversion fees, depending on the payment and settlement method used. These charges are deducted from the payment amount before funds are settled into the merchant's wallet. ravenhive collaborates with a global network of partner banks to facilitate cross-border payments and provides a detailed breakdown of fees in its reporting tools for transparency.", "faq3Content": "To activate payment methods in Ravenhive: <ol><li><b>Complete Merchant Setup:</b> Finalise your business account and ensure PCI DSS compliance.</li> <li><b>Select Payment Methods:</b> Choose preferred methods (Visa, Mastercard, Momo, telebirr, raven etc.) from the dashboard. </li> <li><b>Customise Settings:</b> Add branding, adjust currency preferences, and set settlement currencies.</li> <li><b>Approval:</b> Some methods may require ravenhive approval.</li> <li><b>Test and Launch:</b> Use the sandbox to test payments before going live.</li></ol>For detailed instructions, check <a>ravenhive's integration guide.</a>", "realPerson": "Talk to a real person", "sendMessage": "Send us a message", "hearFromYou": "We love to hear from you", "supportMail": "<EMAIL>"}, "card": {"ravenCards": "Raven Cards", "heroTitle": "Unlock the power of Raven  virtual and physical card", "getCard": "Get a Card", "momoToCardTitle": "No more delays at the bank. Convert your mobile money number into virtual cards", "momoToCardDesc": "Forget the wait for your physical debit card - embrace the convenience of Raven's virtual personal cards! Go fully digital and order, manage, and utilize your cards entirely online. Use them for all your online payments, as well as seamlessly integrate with Apple Pay and Google Pay for a hassle-free payment experience.", "ezwitchTitle": "Order Multi currency cards and local Ezwitch cards", "ezwitchDesc": "The Ezwitch card by Raven is a convenient and secure payments solution. Load funds, make payments online or in-store, and track transactions easily. Enjoy secure transactions, real-time notifications, and easy card managements.", "blackTitle": "Order Multi currency cards and local  Ezwitch cards", "blackDesc": "Unlock the power of raven global and domestic cards with the ability to apply for cards in three different currencies", "greenTitle": "Order Multi currency cards and local  Ezwitch cards", "greenDesc": "EUR, GBP, and USD. Pay for your purchases in the most convenient currency, and stay on top of your transactions through the web-based dashboard or directly within the Raven App.", "blueTitle": "Order Multi currency cards and local  Ezwitch cards", "blueDesc": "Embrace the flexibility and control of a fully digital financial experience, leaving the wait for a physical debit card behind.", "paymentDone": "Payment Done", "transactWithRaven": "Transact with <PERSON>", "exclusiveTitle": "Exclusive virtual card features to make payment instantly", "payWithPhone": "Pay with phone", "payWithPhoneDesc": "Raven's virtual cards work with Apple Pay and Google Pay, enabling contactless payments anywhere these mobile wallets are accepted.", "digitalService": "Benefits from digital services", "digitalServiceDesc": "Shop online with Raven’s virtual cards. Manage multi-currency cards through the app. Secure payments with two-step verification.", "yourActivities": "Analyse your activities", "yourActivitiesDesc": "<PERSON><PERSON><PERSON>'s virtual cards notify you of all transactions instantly. Set spending limits and track your expenses easily.", "requestTitle": "How to create a Virtual Personal card in Raven app", "getCashEasily": "Get your cash Easily", "completeVerification": "complete verification and choose virtual cardcomplete verification and choose virtual card", "step1": "Step 1", "step2": "Step 2", "step3": "Step 3", "step4": "Step 4", "discoverMore": "Discover More With Raven", "sendAndReceive": "Send and receive money globally", "sendAndReceiveDesc": "Raven enables domestic and international money transfers via SEPA. Its internal user-to-user transfers are instant and free", "openAccount": "opening a currency account", "openAccountDesc": "Raven lets you open additional accounts in EUR, GBP, and USD currencies directly from the dashboard. You can have up to 5 accounts per currency.", "exchange": "Exchange money transparently", "exchangeDesc": "Raven's services are available 24/7. You'll receive instant notifications about your account activity. Additionally, Raven offers business and merchant accounts if needed.", "openAnAccount": "Open an account", "protectPayments": "Protect your online & offline payments", "signUpToRaven": "Sign up to <PERSON> and choose a personal wallet type.", "corperateTitle": "Do everything online with Raven Business cards", "corperateDesc": "Raven's corporate virtual debit card simplifies business payments. Get it instantly for online, marketing, and travel expenses. Open a Raven business account and add your corporate card right away.", "corperateCards": "Corporate cards", "onlinePurchase": "Online purchase", "onlinePurchaseDesc": "Paid $500 on monthly subscription", "faqTitle": "Frequently Ask Questions", "card": "Card", "faqT1": "what is a virtual card and what is raven card ?", "faqT2": "why should I use raven virtual cards ?", "faqT3": "How do I get a card ?", "faqT4": "Is raven virtual card safe to use ?", "faqDesc": "This is the accordion content", "proStep1Title": "Protect your online & offline payments", "proStep1Desc": "Sign up to <PERSON> and choose a personal wallet type.", "proStep2Title": "Protect your online & offline payments", "proStep2Desc": "Fill in your personal details.", "proStep3Title": "Protect your online & offline payments", "proStep3Desc": "Verify your identity: take a picture of your ID and a video selfie.", "proStep4Title": "Protect your online & offline payments", "proStep4Desc": "Thats it, you’re ready to order a virtual personal card and make transactions!", "reqStep1Title": "How to create a Virtual Personal card in Raven app", "reqStep1Desc": "complete verification and choose virtual cardcomplete verification and choose virtual card", "reqStep2Title": "How to create a Virtual Personal card in Raven app", "reqStep2Desc": "Select card design and color", "reqStep3Title": "How to create a Virtual Personal card in Raven app", "reqStep3Desc": "create. and thats all. Your card is up for use instantly!"}, "auth": {"signup": "Sign Up", "signin": "Sign In", "login": "<PERSON><PERSON>", "signupSub": "Create your Raven account to continue", "signinSub": "Sign into your Raven account to continue ✨", "firstName": "First Name", "lastName": "Last Name", "otherNames": "Other Names", "companyEmail": "Company Email", "phoneNumber": "Phone Number", "country": "Country", "email": "Email Address", "location": "Company Location", "password": "Password", "confirmPassword": "Confirm Password", "passMin": "minimum 8 characters", "accountAlready": "Already have an account?", "noAccount": "Don't have a Raven account?", "agreeToTerms": "By signing up you agree to Raven <link1>Terms of service</link1> and <link2>Privacy policy.</link2>", "sendTerms": "By sending you agree to <PERSON> <link1>Terms of service</link1> and <link2>Privacy policy.</link2>", "noFirstName": "Please enter your first name", "shortName": "Please enter at least 2 characters", "longName": "Please enter not more than 50 characters", "noLastName": "Please enter your last name", "enterCompanyEmail": "Please enter your company email", "enterValidEmail": "Please enter a valid email address", "enterValidPhone": "Please enter a valid phone number", "enterPassword": "Please enter your password", "enterPasswordConfirm": "Please enter your password confirmation", "shortPassword": "Password should be at least 6 characters", "longPassword": "Password should be less than 50 characters", "passwordPattern": "Password should contain at least one number", "noMatchPassword": "Your passwords do not match", "confirmVerify": "Confirm Verify", "emailOtpSent": "we have sent a verification code to {{email}}", "resendCode": "Resend Code", "confirm": "Confirm", "verifySuccess": "Verification successful", "proceedToPhone": "Now proceed to verify phone number", "redirectToComplete": "We will redirect you to complete setting up your business", "message": "Message", "send": "Send", "termsOfService": "Terms of Service"}, "dashboard": {"privacy": "Privacy", "help": "Help", "dashboard": "Dashboard", "balance": "Balance", "income": "Income", "expenses": "Expenses", "monthlyOverview": "Monthly Earning Overview", "wallet": "Wallet", "noData": "No data", "makeSales": "As you make and receive payments, your monthly overview will show here", "addCardHere": "Add Your Card here", "addCard": "Add Card", "paymentMethod": "Payment Method", "paymentMethods": "Payment Methods", "addPayment": "Add Payment Method", "quickPayments": "Quick Payments", "sendMoney": "Send Money", "paysDisplayedHere": "Your payment transactions will be displayed over here", "recent": "Recent", "recentTrans": "Recent Transactions", "makePayment": "Make payment", "expiredDate": "Expired Date", "cardHolder": "Card Holder", "expiresOn": "Expires on", "settings": "Settings", "pay": "Pay", "getPaid": "Get Paid", "payments": "Payments", "chooseMethod": "Choose the kind of payment method you want to add", "card": "Card", "momo": "Mobile Money", "bankPayment": "Bank Payment", "cardDetails": "Card Details", "momoDetails": "Mobile Money Account Details", "enterCardDetails": "Enter the details of your card", "enterBankDetails": "Enter the details of your bank", "bankDetails": "Bank Account Details", "accountNumber": "Bank Account Number", "table": "Bank Account Name", "selectCard": "Select Card", "selectBank": "Select Bank", "ravenCard": "Raven Card", "visaCard": "Visa Card", "masterCard": "Mastercard", "expiryDate": "Expiry Date", "cvv": "CVV", "enterYourMomo": "Enter the details of your mobile money wallet", "addAccount": "Add Account", "selectNetwork": "Select Network", "customers": "Customers", "totalCustomers": "Total Customers", "vendors": "Vend<PERSON>", "clients": "Clients", "active": "Active", "search": "Search", "addCustomer": "Add Customer", "noVendor": "No vendors to display", "noClient": "No clients to display", "addClient": "Add client", "addVendor": "Add vendor", "addVendorToList": "Add a new vendor to your list of vendors", "addClientToList": "Add a new vendor to your list of vendors", "action": "Action", "editBill": "Edit Bill", "downloadReceipt": "Download receipt", "customerDetails": "Customer Details", "companyName": "Company Name", "fullName": "FullName", "emailAddress": "Email Address", "companyWebsite": "Company Website", "phoneNumber": "Phone Number", "profileDetails": "Profile Details", "updateProfile": "Update Profile", "profilePhoto": "Profile Photo", "profile": "Profile", "security": "Security", "accounts": "Accounts", "paymentLink": "Payment Link", "preferences": "Preferences", "api": "API", "apiSubTitle": "Keep track of all API test keys in your account", "integration": "Integration", "delete": "Delete", "uploadProfile": "Upload Profile", "cancel": "Cancel", "loginDetails": "<PERSON><PERSON>", "currentPassword": "Current Password", "securityQuestions": "Security Questions", "twoStepVerification": "2-step Verification", "securityCredential": "Security credentials", "currentSession": "Current Session", "updateSecurity": "Update Security", "bankCard": "Bank Card", "updateAccount": "Update Account", "otherAccounts": "Other Accounts", "remove": "Remove", "update": "Update", "createIntegration": "Create Integration", "manage": "Manage", "integrations": "Integrations", "goToDocs": "Go to Docs", "apiAndWebhooks": "API Keys & Webhooks", "liveMode": "Live Mode", "reviewInfo": "your security details are here", "securityInfo": "Review recent login activities", "liveModeSubTitle": "Generate production-level credential for integration with live service and data", "publicKey": "Public Key", "paymentLinks": "Payment Links", "privateKey": "Private Key", "webhookUrl": "Webhook URL", "callbackUrl": "Callback URL", "testKeys": "Your Test Keys", "generateKey": "Generate New API Keys", "saveChanges": "Save Changes", "copy": "Copy", "profileDetailsSubTitle": "update you photo and personal details here", "profilePhotoSubTitle": "This will be displayed on your profile", "integrationSubTitle": "Manage all your integrations", "testKeySubTitle": "Generate temporary credentials for testing and development in a sandbox environment", "neverUseTestKey": "Never use test API keys in live /production environment -doing so compromises security and system integrity", "savings": "Savings", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "withdrawMoney": "Withdraw Money", "upcomingBill": "Upcoming Bill", "totalRequest": "Total Request", "client": "Client", "vendor": "<PERSON><PERSON><PERSON>", "continue": "Continue", "selectCustomerType": "Select the customer type you want to add", "sendMoneyTo": "Send money to those you bought things from", "addInvoice": "Add Invoice", "sendPaymentRequest": "Send payment Request", "reconcilePayment": "Reconcile  Payment", "syncUpload": "Sync, upload or add them manually", "syncBack": "Sync back to your accounting software or export the payment info", "selectInvoice": "Select the invoices you want to send", "someSteps": "This are some few steps to follow", "getReady": "Get ready to be Paid", "next": "Next", "scheduleBill": "Schedule Bill", "totalSum": "Total Sum", "transactions": "Transactions", "refund": "Refund", "tableAccountName": "Account Name", "tableAccountNo": "Account Number", "type": "Type", "amount": "Amount", "transactionDate": "Transaction Date", "status": "Status", "subAccount": "SubAccount", "filter": "Filter", "downloadExcel": "Download Excel File", "paidBill": "<PERSON><PERSON>", "request": "Request", "howTo": "How  To make Payment Request", "selectHowTo": "Select how to make payment request", "generatePaymentLink": "Generate Payment Link", "paymentRequest": "Payment Request", "selectSubscription": "Select a subscription plan for your payment link", "back": "Back", "onetimePayment": "One Time Payment", "subscriptionPayment": "Subscription Payment", "donationPayment": "Donation Payment", "dropInvoice": "Drop invoice to upload or upload or", "browse": "Browse", "paymentDetails": "Payment Details", "customerEmail": "Customer <PERSON><PERSON>", "accountName": "Account Name", "accountEmail": "Account <PERSON><PERSON>", "currency": "currency", "description": "Description", "accNumber": "Account Number", "goHome": "Go Home", "linkGenerated": "Link Generated", "linkSent": "payment link sent to the email provided", "approvePayment": "Approve Payment", "refundDetails": "Refund Details", "vendorName": "Vendor Name", "totalAmount": "Total Amount", "dueDate": "Due Date", "billServiceCharge": "Bill/Service Charge", "note": "Note", "collectPayment": "Collect payment faster , no code required", "collectPaymentDes": "Pay or Receive payment from you customer by generating and sharing a payment link", "depositMoney": "Deposit money", "cardNumber": "Card Number", "generalLink": "General Link", "specificUser": "Specific User", "createLink": "Create Link", "makePaymentFixed": "Make user payment fixed", "uploadProductImage": "Upload product image", "raven": "Raven", "amountToPay": "Amount to pay", "collectPaymentFaster": "Collect payment faster", "collectPayNoCode": "Collect payment faster , No code required collect payment from anyone by sharing a link to a payment page", "makePaymentFaster": "Make payment faster", "makePayNoCode": "Make payment faster , Make payment to anyone with no stress by scheduling payments", "amountRequired": "amount is required", "emailRequired": "email is required", "accountNameRequired": "account name is required", "accountNumberRequired": "account number is required", "phoneNumberRequired": "phone number is required", "paymentChannels": "Payment Channels", "emailReceipt": "Email Receipts", "transactionEmails": "Transaction Emails ", "transferEmails": "Transfer Email", "transferEmailToReceipient": "Transfer Email to Receipient", "internationalPayments": "International Payments", "paymentSchedule": "Payment Schedule", "transferConfermation": "Transfer Confirmation", "allowCardPayment": "Allow Card Payment", "allowBankPayment": "Allow Bank Payment", "allowLocalPayment": "Allow Local Payment", "emailReceiptToCustomers": "Email Receipts to customer", "emailMe": "Email me for every transaction", "sendEmailNotification": "Send email notification to transfer receipient", "mannualSettlement": "Manual settlement ", "otpConfirmation": "OTP Confirmation Disabled", "change": "Change", "enable": "Enable", "withdrawalEmailOtpSent": "Kindly Enter  your secret code Sent to {{email}} to approve withdrawal", "enterSecretCode": "Enter your secret code", "statistics": "Statistics", "averageTransactions": "Average Transaction", "revenue": "Revenue", "netProfit": "Net Profit", "userManagement": "User Management", "adminSubTitle": "Manage your team members and their account permission here", "allUsers": "All users", "addUser": "Add user", "invite": "Invite", "joined": "Joined", "user": "User", "email": "Email", "access": "Access", "nicknameRequired": "Nickname Required", "planDuration": "Plan Duration", "poweredBy": "Powered by"}, "admin": {"addUser": "Add User", "whoToInvite": "Who would you like to invite? New user added will be notified and signed in via account", "manualRole": "Manual role", "defaultRole": "Default role", "role1Title": "Admin", "role1Des": "Full access to all features", "role2Title": "Cardholder", "role2Des": "Access to coperate card and customers", "role3Title": "Analyst", "role3Des": "Access to coperate card and customers", "role4Title": "Developer", "role4Des": "Access to transactional data", "role5Title": "View only", "role5Des": "Access to all features only but cannot view"}, "landing": {"payments": "Payments", "paymentHeroSubtitle": "Securely <span>ACCEPT</span> Payments worldwide…", "paymentHeroDes": "Online is a big place, Make the most of it! Accept Payments from your customers around the world via preferred payment methods. Over 20+ currencies with No coding required! Just one API", "contactSales": "Contact Sales", "seePricing": "See Pricing", "businessusingRaven": "Business using ravenhive", "howItWorks": "How It Works", "howItWorksTitle": "Accept Payments in minutes", "howItWorksSub": "without a single line of code", "howItWorksT1": "Generate your link", "howItWorksT2": "Share your link", "howItWorksT3": "Get Paid", "howItWorksD1": "Create a secure One time or reusable link on your dashboard.", "howItWorksD2": "Share your link via email , SMS, social media and more .", "howItWorksD3": "Receive money in your preferred account (bank account, local wallet or raven account ) and get notified for a successful payment.", "proceedToPayment": "Proceed to payment", "empowerTitle": "How we EMPOWER your business", "empowerSub": "we grow by making you grow", "empowerList1": "seamless authentication", "empowerList2": "Eliminate unnecessary Fx conversion fees", "empowerList3": "Risk management tools", "empowerList4": "Attract and convert more customers globally", "empowerList5": "Safeguard against fraud with our 3DS fraud orchestration Technology", "terminalTitle": "Collect offline Payments", "terminalSub": "Get your business on the go.", "terminalDes": "At Ravenhive we don't just empower businesses, we power lives.", "developersTitle": "Built for every kind of business", "developersSub": "by Developers for Developers", "developersDes": "Easily integrate payment channels for any use cases from online to bulk payouts - by an intelligently built customized API.", "revenueTitle": "GROW <span>your</span> REVENUE", "revenueSub": "how do you want to be paid?", "revenueDes": "Accept a wide array of popular global and local payment methods", "payout": "Payout", "payoutDes1": "Pay suppliers, gig economy workers or partners in real time in your preferred currency", "payoutDes2": "Make fast, cost effective transfers to suppliers across Africa in real time", "date": "Date", "payoutMethod": "Payout method", "status": "Status", "amount": "Amount", "transaction": "Transaction", "transactionDes1": "25+ currencies", "transactionDes2": "28+ countries ....", "transactionCountries": "with african countries logos. Ghana , Nigeria, DR. congo, Ethiopia , Tanzania, Togo, sierra leone and south Africa", "mangTitle": "Expenses management tools", "mangDes1": "Integrate with your accounting software at ease and manage employee expenses with tools available for FREE", "mangDes2": "20+ currency | Global & Local Payment methods - Cards , Local wallet , Bank Transfer, USSD.", "payoutTitle": "For local payout, calculate", "payoutDes": "See how much it  costs to use Raven payout for local payments. Enter an amount and calculate to see our charges.", "getStarted": "Get Started", "ravenhive": "ravenhive", "payoutTxt1": "RAVEN FEES (1.98%) PAYS", "payoutTxt2": "We'll settle you", "payoutTxt3": "Raven fees (1.9%)", "payoutTxt4": "Free, automatic settlement in 24 hours", "payoutTxt5": "No hidden fees", "payoutTxt6": "Zero management charges", "payoutTxt7": "Volume discounts available", "payoutTxt8": "Zero Integration fees", "growTxt1": "99.9% payment acceptance", "growTxt2": "Domestic cards 2.5%", "growTxt3": "International cards 3.0%", "growTxt4": "Local payment methods 1%", "growTxt5": "Payouts & Transfers", "growTxt6": "Banks 1%+ 0.9% above inter bank Fx fees ", "growTxt7": "Local wallet 1% + 0.9% above inter bank Fx fees", "growTxt8": "Same day Transfer of $2000 executes at an additional fee of $10-$20 😊 (You save over 70% more in fees )", "growTxt9": "Grow beyond borders with Ravenbusines", "growTxt10": "Everything you need to to simplify your global payments and financial operations", "coreApi": "Core API", "coreApiTitle": "EXPLORE CUSTOM PRICING OPTIONS ", "coreApiDes": "If you are a business , marketplace or platform with high payments volume , get in touch  with us to discuss custom pricing options. ", "getStartedTitle": "Ready to get started ?", "getStartedDes": "Create an account and start collecting and sending payments instantly!", "pricing": "Pricing", "pricingDes": "Flexible Pricing based on your business needs. Start winning new markets today!", "pricingTxt1": "Zero monthly fees", "pricingTxt2": "Zero setup fees", "pricingTxt3": "A full suit of out-of-the-box features", "pricingTxt4": "Simple and transparent pricing", "faq": "FAQ", "pricingFaq1": "When do I receive my money", "pricingFaq2": "Who bears the transaction charge", "pricingFaq3": "How do I Activate Payment methods "}, "enterprise": {"solutionsTitle1": "Industry-wide <style> payment solutions</style> and needs.", "solutionsTitle2": "With <br />a<br /> Single <br /> <style> Integration</style>,<br /> you can;", "solutionsDescription": "Expand your reach to millions of customers in rapidly growing African markets with a seamless integration. Ravenhive provides the perfect payment solution for global commerce, enabling acceptance of various payment methods and facilitating worldwide cross-border fund settlements.", "action1Title": "Deploy", "action2Title": "Transfer", "action3Title": "Manage", "action1SubTitle": "Integrate with ease and specificity", "action2SubTitle": "Control how money moves worldwide", "action3SubTitle": "Your dashboard, your space", "item1Description": "Our API offers seamless integration,bringing together all Ravenhive products and payment methods through a single, unified endpoint. It's quick to implement and enables effortless scalability without any barriers.", "item2Description": "Transform Payins and Payouts into a competitive edge with easy, convenient solutions that ensures funds reach you and your customers faster.", "item3Description": "A simple, intuitive way to monitor, set and section money movements- from reconciliation to global currency management.", "listItem1Title1": "Unified API", "listItem1Title2": "Drive conversions", "listItem1Title3": "Simplify Compliance", "listItem2Title1": "Bank Transfers", "listItem2Title2": "Card and local wallets", "listItem2Title3": "Payment Links", "listItem3Title1": "Dashboard", "listItem3Title2": "Realtime account Updater", "listItem3Title3": "Transparent FX", "listItem3Title4": "Insights", "listItem1Description1": "From fully custom to prebuilt interfaces that get you up and running fast.", "listItem1Description2": "Customize your payment flow and remove friction from you payment page.", "listItem1Description3": "Our payments API seamlessly adopts to fast-evolving regulations.", "listItem2Description1": "Make transfers to bank accounts using local clearing methods in hours-globally.", "listItem2Description2": "Make payouts to cards and local wallets with funds available to customers within minutes, not days.", "listItem2Description3": "Create payment links directly form the Dashboard or through our API.", "listItem3Description1": "Payins and Payouts in one place.", "listItem3Description2": "Deploy advanced payment security and encryption.", "listItem3Description3": "View interbank rates in realtime.", "listItem3Description4": "utilize detailed responses and error codes for every payout to gain valuable insights.", "integrationTitle1": "Reach new markets", "integrationTitle2": "Dashboard", "integrationTitle3": "Multiple local payment methods", "integrationDescription1": "Accept payments like a local business-in multiple currencies in Africa. Receive payments in local currencies, settle funds fast and gain valuable insights", "integrationDescription2": "Manage all your payments,track transactions and reconcile payments efficiently both local and cross-border through Ravenhive intuitive dashboard", "integrationDescription3": "Provide multiple payment methods to your customers"}, "careers": {"lookHere": "Looking to collect payments? look here", "hero": "<h1>Ravenhive is powering <br/>cross border<br/> payments in Africa</h1>", "changing": "We are changing the future of corss border, payments in Africa. We're rapidly expanding our team", "join": "Join Our team", "des1": "We are redefining the future of cross border payments in Africa. We're rapidly expanding our team", "title1": "Our Vision", "des2": "We maintain ridiculously high standards for who we work with and what we expect of them", "title2": "Our Values", "title3": "Our Goals", "title4": "Our Culture", "des4": "We possess an endless desire to create magical experiences for our customers", "explore": "Explore Opportunities", "role": "Roles", "roleTitle125": "Fullstack Software Engineer", "roleTitle3": "Data Analyst Engineer", "roleTitle4": "Junior AI Engineer", "roleTitle6": "Account Manager Engineer", "location1": "London Remote", "location2": "San Francisco Fulltime", "location3": "Ghana Fulltime", "location4": "London Internship", "location5": "San Francisco Fulltime", "location6": "South Africa Fulltime", "salary1": "$25 - $60 per hour", "salary2356": "$40k - $100k per year", "salary4": "$10 - $20 per hour", "account": "Don't have and account?", "applicationForm": "Application Details", "personalInfo": "Personal Info", "fullname": "Full name", "workEmail": "Work email", "number": "Phone number(optional)", "cv": "Upload your CV and cover page", "upload": "Upload", "download": "<PERSON><PERSON><PERSON> Template", "submit": "Submit", "uploadCover": "Upload cover letter", "selectElementsTitle1": "Work type", "selectElementsTitle2": "Location", "selectElementsTitle3": "Team", "selectElementsTitle4": "Employee type", "engineering": "Engineering & Design", "sales": "Sales & Business Development", "legal": "Legal & Compliance", "applications": "Available Roles", "position": "Position", "onsite": "Onsite", "remote": "Remote", "london": "London", "accra": "Accra", "sfo": "S F O", "apply": "Apply", "softwareLead": "Software Engineering Lead", "dataAnalyst": "Data Analyst Engineering Lead", "customerOnboarding": "Customer Onboarding Manager", "legalService": "Legal Servive", "hybrid": "Hybrid", "location": "Location", "status": "Status"}, "documentation": {"developers": "Developers", "hero": "Enjoy a flexible,secure,and easily integrated payment API", "accept": "Accept online and offline payments worldwide", "coding": "Our coding environment gives partners, merchants and financial companies the tools to build frictionless payment solutions that can sclae globally", "card1": "Api Reference", "card2": "Testing Guide", "card3": "Response Codes", "integration": "Integration made<br/> easy", "robust": "Robust and intuitive APIs for all payment types and solutions", "client": "Cleint Libraries for the leading developer platforms", "sdks": "SDK in six of the most popular coding languages", "comprehensive": "Comprehensive sandbox testing", "sample": "Sample code for hundreds of payment use cases", "technical": "See technical documents", "integrationCard1": "Client Libraries", "integrationCard2": "SDKs", "integrationCard3": "Sandbox testing", "interested": "Interested? Let's talk", "contactUs": "Contatct us"}, "about": {"team": "Team", "meet": "Meet the Team", "bio1": "<PERSON><PERSON><PERSON> is an entrepreneur and fintech innovator, leading Ravenhive Technologies, an cross-border payment platform that helps Afirca SMEs manage global transactions with ease. He also directes Hive.Network, a product of Ravenhive, connecting African tech talents to global job opportunites. With a background in banking, securities trading and tech systems for healthcare, <PERSON><PERSON><PERSON> is committed to financial inclusion and youth empowerment. His work focuses on creating accessible, affordable, and transparent solutions to drive global commerve and local impact", "bio2": "<PERSON> is a dedicated entreprneur and fintech enthusiast,serving as key team member at Ravenhive Technologies, where he contrinbutes to developing cross-border payment solutions for African SMEs. With a strong background in technology and business, <PERSON> palys a pivotal role in building scalable financial infrastructures that enhance global transactions. Passionate about innovation and financial inclusion, he Leads the Ravenhive team to create impactful solutions tath bridge local and gloabl markets.", "teamRole1": "CEO,Co-founder", "teamRole2": "<PERSON><PERSON>,Co-founder", "signUpNow": "Sign up now"}, "notFound": {"landingNotFound": "Page Not Found", "visitLanding": "Visit our homepage"}, "resources": {"resources": "Resources", "title1": "Knowledge hub", "title2": "Blog", "title3": "Customer stories", "title4": "Culture and worklife", "title5": "Discoveries", "des1": "Everything about Payments, Accounts Security and more", "des2": "Everything about us", "des3": "Everything about clients and customers", "des4": "Everything about how we do it at ravenhive", "des5": "Find out more about our researches", "notABank": "We are not a Bank,<span> We are better</span>"}, "docs": {"docs": "DOCS", "panelItem1": "Home", "panelItem2": "Core API", "panelItem3": "Payments", "panelItem4": "Transaction FX", "panelItem5": "Payouts", "panelItem6": "Global Treasury", "panelItem7": "Payments for Platforms", "panelItem8": "Lazer", "panelItem9": "Developer Tools", "panelItem10": "Trust Centre"}}