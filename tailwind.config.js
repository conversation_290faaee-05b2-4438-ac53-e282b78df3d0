/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      backgroundImage: {
        'business-page-feature-item': "url('/images/business-feature-bg.png')"
      },
      colors: {
        'raven-green': {
          50: '#F4FFF9',
          500: '#11E342',
          800: '#128542',
          900: '#324740'
        },
        darkGreen: {
          400: '#4EA26C',
          500: '#38756F'
        },
        dirtRed: {
          500: '#BF9D8A'
        },
        'raven-dark': { 900: '#191919' },
        'raven-link': '#279681'
      }
    }
  },
  // eslint-disable-next-line no-undef
  plugins: [require('@tailwindcss/forms'), require('tailwind-scrollbar')]
};
