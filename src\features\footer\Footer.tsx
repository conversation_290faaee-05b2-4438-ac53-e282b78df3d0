import { Logo } from '@/components/logo';
import { Translate } from '@/components/translate';
import { SimpleText } from '@/components/typography';
import facebook from '/icons/facebook.png';
import twitter from '/icons/twitter.png';
import instagram from '/icons/instagram.png';
import AppleButton from '@/components/download-buttons/AppleButton';
import GoogleButton from '@/components/download-buttons/GoogleButton';

const Footer = () => {
  const year = new Date().getFullYear();
  return (
    <footer className="w-full footer text-white md:px-24 px-5 relative z-0">
      <div className="relative grid grid-cols-1 md:grid-cols-4 gap-16 py-20">
        <div className="footer-cirle absolute left-10 z-1" />
        <div className="footer-cirle absolute right-10 z-1" />
        <div className="space-y-6">
          <Logo className="w-32" />
          <SimpleText className="font-semibold text-lg">
            <Translate msgId="footer.socialMedia" />
          </SimpleText>
          <div className="flex items-center gap-2">
            <div>
              <img className="w-10" src={facebook} alt="facebook icon" />
            </div>
            <div>
              <img className="w-10" src={twitter} alt="twitter icon" />
            </div>
            <div>
              <img className="w-10" src={instagram} alt="instagram icon" />
            </div>
          </div>
        </div>
        <div>
          <ul className="space-y-3">
            <li className="font-semibold text-lg">
              <Translate msgId="footer.menu" />
            </li>
            <li className="text-gray-400 text-sm">
              <Translate msgId="home.personal" />
            </li>
            <li className="text-gray-400 text-sm">
              <Translate msgId="home.business" />
            </li>
            <li className="text-gray-400 text-sm">
              <Translate msgId="home.company" />
            </li>
          </ul>
        </div>
        <div>
          <ul className="space-y-3">
            <li className="font-semibold text-lg">
              <Translate msgId="home.terms" />
            </li>
            <li className="text-gray-400 text-sm">
              <Translate msgId="home.legal" />
            </li>
            <li className="text-gray-400 text-sm">
              <Translate msgId="home.privacyPolicy" />
            </li>
            <li className="text-gray-400 text-sm">
              <Translate msgId="home.cookiePolicy" />
            </li>
            <li className="text-gray-400 text-sm">
              <Translate msgId="home.support" />
            </li>
          </ul>
        </div>
        <div className="space-y-3">
          <SimpleText className="text-lg font-semibold">
            <Translate msgId="home.getTheApp" />
          </SimpleText>
          <SimpleText className="text-gray-400 text-sm">
            <Translate msgId="home.toSendGlobal" />
          </SimpleText>
          <AppleButton className="w-24" />
          <div style={{ marginLeft: '-8px' }}>
            <GoogleButton className="w-32" />
          </div>
        </div>
      </div>
      <div className="text-center py-3">
        <SimpleText component="small" className="text-gray-400">
          &copy; <span translate="no">Raven</span>{' '}
          <Translate msgId="footer.copyright" /> {year}.{' '}
          <Translate msgId="footer.allRights" />
        </SimpleText>
      </div>
    </footer>
  );
};

export default Footer;
